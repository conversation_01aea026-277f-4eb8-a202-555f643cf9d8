#!/usr/bin/env python3
"""
检查WebSocket市场路由
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.api.v1.websocket_market import router
    
    print(f"Router has {len(router.routes)} routes:")
    for route in router.routes:
        if hasattr(route, 'path'):
            methods = getattr(route, 'methods', 'WebSocket')
            print(f"  {route.path} - {methods}")
            
    print("\nRouter prefix:", getattr(router, 'prefix', 'None'))
    print("Router tags:", getattr(router, 'tags', 'None'))
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
