# 量化投资平台环境配置模板
# 复制此文件为 .env 并填入实际值

# =============================================================================
# 应用基础配置
# =============================================================================
PROJECT_NAME=量化投资后端平台
VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-secret-key-here-change-in-production

# =============================================================================
# 服务器配置
# =============================================================================
HOST=0.0.0.0
PORT=8000
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# 安全配置
# =============================================================================
ENCRYPTION_MASTER_KEY=your-encryption-master-key-here
ENABLE_IP_WHITELIST=false

# =============================================================================
# 数据库配置
# =============================================================================
DATABASE_URL=sqlite:///./quant_platform.db
# 生产环境使用PostgreSQL:
# DATABASE_URL=postgresql://user:password@localhost/quant_platform

# =============================================================================
# Redis配置
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# =============================================================================
# 市场数据源配置
# =============================================================================
# Tushare配置 (主要数据源)
TUSHARE_TOKEN=your-tushare-token-here
TUSHARE_BASE_URL=http://api.tushare.pro
TUSHARE_MAX_CALLS_PER_MINUTE=200

# AKShare配置 (备用数据源)
AKSHARE_ENABLED=true

# 数据源策略
USE_REAL_DATA=true
PRIMARY_DATA_SOURCE=tushare
FALLBACK_DATA_SOURCE=akshare

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_ENABLED=true
CACHE_TTL_REALTIME=30
CACHE_TTL_KLINE=300
CACHE_TTL_SEARCH=1800
CACHE_TTL_QUOTE=60

# =============================================================================
# WebSocket配置
# =============================================================================
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000
WS_MESSAGE_QUEUE_SIZE=100

# =============================================================================
# 监控配置
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=INFO

# =============================================================================
# 前端CORS配置
# =============================================================================
BACKEND_CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173,http://127.0.0.1:3000

# =============================================================================
# 文件存储配置
# =============================================================================
DATA_DIR=./data
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB

# =============================================================================
# 邮件配置 (可选)
# =============================================================================
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true

# =============================================================================
# 第三方服务配置 (可选)
# =============================================================================
SENTRY_DSN=
PROMETHEUS_ENABLED=false
