"""
统一缓存管理系统
提供Redis优先的统一缓存接口，支持自动降级到内存缓存
"""
import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import asyncio
import logging
from abc import ABC, abstractmethod
from enum import Enum

import redis.asyncio as redis
from redis.asyncio import Redis

from app.core.config import settings

logger = logging.getLogger(__name__)


class CacheType(Enum):
    """缓存类型枚举"""
    REALTIME = "realtime"      # 实时数据 (30秒)
    QUOTE = "quote"            # 行情数据 (5分钟)
    KLINE = "kline"            # K线数据 (15分钟)
    STOCK_INFO = "stock_info"  # 股票信息 (1小时)
    HISTORICAL = "historical"  # 历史数据 (1天)
    SEARCH = "search"          # 搜索结果 (10分钟)


class CacheConfig:
    """缓存配置"""
    
    # 默认TTL配置 (秒)
    DEFAULT_TTL = {
        CacheType.REALTIME: 30,
        CacheType.QUOTE: 300,      # 5分钟
        CacheType.KLINE: 900,      # 15分钟
        CacheType.STOCK_INFO: 3600, # 1小时
        CacheType.HISTORICAL: 86400, # 1天
        CacheType.SEARCH: 600,     # 10分钟
    }
    
    # Key前缀配置
    KEY_PREFIXES = {
        CacheType.REALTIME: "rt",
        CacheType.QUOTE: "quote",
        CacheType.KLINE: "kline",
        CacheType.STOCK_INFO: "stock",
        CacheType.HISTORICAL: "hist",
        CacheType.SEARCH: "search",
    }
    
    @classmethod
    def get_ttl(cls, cache_type: CacheType) -> int:
        """获取缓存类型的默认TTL"""
        return cls.DEFAULT_TTL.get(cache_type, 300)
    
    @classmethod
    def get_key_prefix(cls, cache_type: CacheType) -> str:
        """获取缓存类型的Key前缀"""
        return cls.KEY_PREFIXES.get(cache_type, "cache")
    
    @classmethod
    def build_key(cls, cache_type: CacheType, *parts: str) -> str:
        """构建标准化的缓存Key"""
        prefix = cls.get_key_prefix(cache_type)
        key_parts = [prefix] + list(parts)
        return ":".join(key_parts)


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    async def clear(self, pattern: str = "*") -> int:
        """清除匹配的键"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        pass


class RedisBackend(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            value = await self.redis.get(key)
            if value is None:
                self._stats['misses'] += 1
                return None
            
            self._stats['hits'] += 1
            
            # 尝试JSON解析，失败则返回原始字符串
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value.decode('utf-8') if isinstance(value, bytes) else value
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"Redis get error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, str):
                serialized_value = value
            else:
                serialized_value = str(value)
            
            if expire:
                await self.redis.setex(key, expire, serialized_value)
            else:
                await self.redis.set(key, serialized_value)
            
            self._stats['sets'] += 1
            return True
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"Redis set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            result = await self.redis.delete(key)
            self._stats['deletes'] += 1
            return result > 0
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"Redis delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"Redis exists error for key {key}: {e}")
            return False
    
    async def clear(self, pattern: str = "*") -> int:
        """清除匹配的键"""
        try:
            keys = await self.redis.keys(pattern)
            if keys:
                result = await self.redis.delete(*keys)
                self._stats['deletes'] += len(keys)
                return result
            return 0
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"Redis clear error for pattern {pattern}: {e}")
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            info = await self.redis.info()
            return {
                **self._stats,
                'backend': 'redis',
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
            }
        except Exception as e:
            logger.error(f"Redis stats error: {e}")
            return {**self._stats, 'backend': 'redis', 'error': str(e)}


class MemoryBackend(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        async with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            cache_item = self._cache[key]
            
            # 检查是否过期
            if cache_item.get('expire_at') and datetime.now() > cache_item['expire_at']:
                del self._cache[key]
                self._stats['misses'] += 1
                return None
            
            self._stats['hits'] += 1
            return cache_item['value']
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        async with self._lock:
            cache_item = {
                'value': value,
                'created_at': datetime.now()
            }

            if expire:
                cache_item['expire_at'] = datetime.now() + timedelta(seconds=expire)

            self._cache[key] = cache_item
            self._stats['sets'] += 1
            return True

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats['deletes'] += 1
                return True
            return False

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        async with self._lock:
            if key not in self._cache:
                return False

            cache_item = self._cache[key]

            # 检查是否过期
            if cache_item.get('expire_at') and datetime.now() > cache_item['expire_at']:
                del self._cache[key]
                return False

            return True

    async def clear(self, pattern: str = "*") -> int:
        """清除匹配的键"""
        async with self._lock:
            if pattern == "*":
                count = len(self._cache)
                self._cache.clear()
                self._stats['deletes'] += count
                return count

            # 简单的模式匹配
            import fnmatch
            keys_to_delete = [key for key in self._cache.keys() if fnmatch.fnmatch(key, pattern)]
            for key in keys_to_delete:
                del self._cache[key]
            self._stats['deletes'] += len(keys_to_delete)
            return len(keys_to_delete)

    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        async with self._lock:
            return {
                **self._stats,
                'backend': 'memory',
                'total_keys': len(self._cache),
                'memory_usage': f"{len(str(self._cache))} bytes (estimated)"
            }


class UnifiedCacheManager:
    """统一缓存管理器"""

    def __init__(self):
        self.primary_backend: Optional[CacheBackend] = None
        self.fallback_backend: MemoryBackend = MemoryBackend()
        self._initialized = False

    async def initialize(self):
        """初始化缓存管理器"""
        if self._initialized:
            return

        try:
            # 尝试连接Redis
            redis_client = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_DB', 0),
                decode_responses=False,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            # 测试连接
            await redis_client.ping()
            self.primary_backend = RedisBackend(redis_client)
            logger.info("Unified cache initialized with Redis backend")

        except Exception as e:
            logger.warning(f"Redis connection failed: {e}, using memory backend only")
            self.primary_backend = None

        self._initialized = True

    async def get(self, cache_type: CacheType, *key_parts: str) -> Optional[Any]:
        """获取缓存值"""
        key = CacheConfig.build_key(cache_type, *key_parts)

        # 优先使用主后端
        if self.primary_backend:
            result = await self.primary_backend.get(key)
            if result is not None:
                return result

        # 回退到内存后端
        return await self.fallback_backend.get(key)

    async def set(self, cache_type: CacheType, value: Any, *key_parts: str, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        key = CacheConfig.build_key(cache_type, *key_parts)
        ttl = expire or CacheConfig.get_ttl(cache_type)

        success = False

        # 设置到主后端
        if self.primary_backend:
            success = await self.primary_backend.set(key, value, ttl)

        # 同时设置到内存后端作为备份
        await self.fallback_backend.set(key, value, ttl)

        return success

    async def delete(self, cache_type: CacheType, *key_parts: str) -> bool:
        """删除缓存"""
        key = CacheConfig.build_key(cache_type, *key_parts)

        success = False

        # 从主后端删除
        if self.primary_backend:
            success = await self.primary_backend.delete(key)

        # 从内存后端删除
        await self.fallback_backend.delete(key)

        return success

    async def exists(self, cache_type: CacheType, *key_parts: str) -> bool:
        """检查缓存是否存在"""
        key = CacheConfig.build_key(cache_type, *key_parts)

        # 优先检查主后端
        if self.primary_backend:
            if await self.primary_backend.exists(key):
                return True

        # 检查内存后端
        return await self.fallback_backend.exists(key)

    async def clear(self, cache_type: Optional[CacheType] = None) -> int:
        """清除缓存"""
        if cache_type:
            pattern = f"{CacheConfig.get_key_prefix(cache_type)}:*"
        else:
            pattern = "*"

        total_cleared = 0

        # 清除主后端
        if self.primary_backend:
            total_cleared += await self.primary_backend.clear(pattern)

        # 清除内存后端
        total_cleared += await self.fallback_backend.clear(pattern)

        return total_cleared

    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            fallback_stats = await self.fallback_backend.get_stats()
        except Exception as e:
            fallback_stats = {"error": str(e), "backend": "memory"}

        stats = {
            'primary_backend': None,
            'fallback_backend': fallback_stats
        }

        if self.primary_backend:
            try:
                stats['primary_backend'] = await self.primary_backend.get_stats()
            except Exception as e:
                stats['primary_backend'] = {"error": str(e), "backend": "redis"}

        return stats


# 全局缓存管理器实例
unified_cache = UnifiedCacheManager()
