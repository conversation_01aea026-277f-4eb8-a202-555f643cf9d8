"""
简化认证服务
用于解决启动依赖问题
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from app.core.config import get_settings

# 简化的User类
class User:
    def __init__(self, id=None, username=None, email=None, hashed_password=None, is_active=True, role="user"):
        self.id = id
        self.username = username
        self.email = email
        self.hashed_password = hashed_password
        self.is_active = is_active
        self.role = role

settings = get_settings()

class AuthService:
    """简化认证服务"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.secret_key = settings.SECRET_KEY
        self.algorithm = getattr(settings, 'JWT_ALGORITHM', 'HS256')
        self.access_token_expire_minutes = getattr(settings, 'JWT_EXPIRE_MINUTES', 30)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        try:
            return self.pwd_context.verify(plain_password, hashed_password)
        except Exception:
            return False
    
    def get_password_hash(self, password: str) -> str:
        """获取密码哈希"""
        return self.pwd_context.hash(password)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[dict]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            return None
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """认证用户（简化版本）"""
        if username == "admin" and password == "admin123":
            return User(
                id=1,
                username="admin",
                email="<EMAIL>",
                hashed_password=self.get_password_hash("admin123"),
                is_active=True,
                role="admin"
            )
        return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        if username == "admin":
            return User(
                id=1,
                username="admin", 
                email="<EMAIL>",
                hashed_password=self.get_password_hash("admin123"),
                is_active=True,
                role="admin"
            )
        return None
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        if user_id == 1:
            return User(
                id=1,
                username="admin",
                email="<EMAIL>", 
                hashed_password=self.get_password_hash("admin123"),
                is_active=True,
                role="admin"
            )
        return None

# 创建全局实例
auth_service = AuthService()
