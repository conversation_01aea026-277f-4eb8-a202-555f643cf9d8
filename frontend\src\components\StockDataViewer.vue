<!--
  股票数据查看器组件
  用于显示股票的详细历史数据
-->
<template>
  <div class="stock-data-viewer">
    <!-- 头部信息 -->
    <div class="viewer-header">
      <div class="stock-info">
        <h2>{{ name }} ({{ symbol }})</h2>
        <p class="stock-meta">历史数据详情</p>
      </div>
      <div class="viewer-actions">
        <el-button @click="refreshData" :loading="loading" :icon="Refresh">
          刷新数据
        </el-button>
        <el-button @click="exportData" :icon="Download">
          导出数据
        </el-button>
        <el-button @click="$emit('close')" :icon="Close">
          关闭
        </el-button>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="data-stats" v-if="stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">总记录数</span>
              <span class="stat-value">{{ stats.totalRecords }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">数据范围</span>
              <span class="stat-value">{{ stats.dateRange }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">最新价格</span>
              <span class="stat-value">{{ stats.latestPrice }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">数据完整度</span>
              <span class="stat-value">{{ stats.completeness }}%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <el-table
        :data="tableData"
        v-loading="loading"
        height="400"
        stripe
        border
      >
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="open" label="开盘价" width="100" />
        <el-table-column prop="high" label="最高价" width="100" />
        <el-table-column prop="low" label="最低价" width="100" />
        <el-table-column prop="close" label="收盘价" width="100" />
        <el-table-column prop="volume" label="成交量" width="120" />
        <el-table-column prop="amount" label="成交额" width="120" />
        <el-table-column prop="change" label="涨跌额" width="100" />
        <el-table-column prop="changePercent" label="涨跌幅" width="100">
          <template #default="{ row }">
            <span :class="row.changePercent > 0 ? 'text-red' : 'text-green'">
              {{ row.changePercent }}%
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download, Close } from '@element-plus/icons-vue'

// Props
interface Props {
  symbol: string
  name: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 统计信息
const stats = ref({
  totalRecords: 0,
  dateRange: '',
  latestPrice: 0,
  completeness: 0
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据加载
    const mockData = generateMockData()
    tableData.value = mockData.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
    total.value = mockData.length
    
    // 更新统计信息
    stats.value = {
      totalRecords: mockData.length,
      dateRange: `${mockData[0]?.date} - ${mockData[mockData.length - 1]?.date}`,
      latestPrice: mockData[mockData.length - 1]?.close || 0,
      completeness: 95
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  const data = []
  const startDate = new Date('2024-01-01')
  const endDate = new Date()
  let currentDate = new Date(startDate)
  let price = 10 + Math.random() * 20

  while (currentDate <= endDate) {
    const change = (Math.random() - 0.5) * 2
    const open = price
    const close = price + change
    const high = Math.max(open, close) + Math.random() * 0.5
    const low = Math.min(open, close) - Math.random() * 0.5
    
    data.push({
      date: currentDate.toISOString().split('T')[0],
      open: open.toFixed(2),
      high: high.toFixed(2),
      low: low.toFixed(2),
      close: close.toFixed(2),
      volume: Math.floor(Math.random() * 1000000),
      amount: Math.floor(Math.random() * 10000000),
      change: change.toFixed(2),
      changePercent: ((change / price) * 100).toFixed(2)
    })
    
    price = close
    currentDate.setDate(currentDate.getDate() + 1)
  }
  
  return data.reverse()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 导出数据
const exportData = () => {
  ElMessage.success('导出功能开发中...')
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadData()
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.stock-data-viewer {
  padding: 20px;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.stock-info h2 {
  margin: 0;
  color: #303133;
}

.stock-meta {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.viewer-actions {
  display: flex;
  gap: 10px;
}

.data-stats {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.data-table {
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
}

.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}
</style>
