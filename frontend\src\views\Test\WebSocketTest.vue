<template>
  <div class="websocket-test">
    <el-card class="test-card">
      <template #header>
        <h2>WebSocket智能推送测试</h2>
      </template>

      <!-- 连接状态 -->
      <div class="status-section">
        <h3>连接状态</h3>
        <WebSocketStatus :show-details="true" />
      </div>

      <!-- 控制面板 -->
      <div class="control-section">
        <h3>控制面板</h3>
        <el-space wrap>
          <el-button 
            @click="connectWebSocket" 
            :disabled="isConnected || isConnecting"
            type="primary"
          >
            连接WebSocket
          </el-button>
          <el-button 
            @click="disconnectWebSocket" 
            :disabled="!isConnected"
            type="danger"
          >
            断开连接
          </el-button>
          <el-button 
            @click="manualReconnect" 
            :disabled="!canManualReconnect"
            type="warning"
          >
            手动重连
          </el-button>
          <el-button @click="clearData" type="info">
            清空数据
          </el-button>
        </el-space>
      </div>

      <!-- 订阅测试 -->
      <div class="subscribe-section">
        <h3>订阅测试</h3>
        <el-space wrap>
          <el-input
            v-model="newSymbol"
            placeholder="输入股票代码"
            style="width: 200px"
            @keyup.enter="subscribeSymbol"
          />
          <el-button @click="subscribeSymbol" type="primary">
            订阅
          </el-button>
          <el-button @click="subscribeTestSymbols" type="success">
            订阅测试股票
          </el-button>
        </el-space>
        
        <div class="subscribed-symbols" v-if="subscriptions.length > 0">
          <h4>已订阅股票:</h4>
          <el-tag
            v-for="symbol in subscriptions"
            :key="symbol"
            closable
            @close="unsubscribeSymbol(symbol)"
            style="margin: 2px"
          >
            {{ symbol }}
          </el-tag>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="data-section">
        <h3>实时数据 ({{ latestQuotes.size }})</h3>
        <el-table 
          :data="quotesArray" 
          height="300"
          size="small"
          v-if="quotesArray.length > 0"
        >
          <el-table-column prop="symbol" label="代码" width="80" />
          <el-table-column prop="price" label="价格" width="80">
            <template #default="{ row }">
              <span :class="getPriceClass(row)">
                {{ row.price?.toFixed(2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="change" label="涨跌" width="80">
            <template #default="{ row }">
              <span :class="getChangeClass(row.change)">
                {{ row.change?.toFixed(2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="change_pct" label="涨跌幅" width="80">
            <template #default="{ row }">
              <span :class="getChangeClass(row.change_pct)">
                {{ (row.change_pct * 100)?.toFixed(2) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="volume" label="成交量" width="100" />
          <el-table-column prop="timestamp" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
        </el-table>
        
        <el-empty v-else description="暂无数据" />
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <h3>统计信息</h3>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="接收消息数">
            {{ connectionStats.totalMessages }}
          </el-descriptions-item>
          <el-descriptions-item label="最后消息时间">
            {{ formatTime(connectionStats.lastMessageTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="连接时长">
            {{ formatDuration() }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import WebSocketStatus from '@/components/market/WebSocketStatus.vue'
import { useMarketWebSocket } from '@/composables/useMarketWebSocket'
import { marketWebSocketService } from '@/services/market-websocket.service'

// 使用WebSocket状态管理
const {
  isConnected,
  isConnecting,
  subscriptions,
  latestQuotes,
  connectionStats,
  canManualReconnect,
  manualReconnect,
  subscribe,
  unsubscribe,
  getAllLatestQuotes
} = useMarketWebSocket()

// 本地状态
const newSymbol = ref('')
const testSymbols = ['000001', '000002', '600000', '600036', '000858']

// 计算属性
const quotesArray = computed(() => {
  return getAllLatestQuotes().sort((a, b) => a.symbol.localeCompare(b.symbol))
})

// 方法
const connectWebSocket = async () => {
  try {
    await marketWebSocketService.connect()
    ElMessage.success('WebSocket连接成功')
  } catch (error) {
    console.error('WebSocket连接失败:', error)
    ElMessage.error('WebSocket连接失败')
  }
}

const disconnectWebSocket = () => {
  marketWebSocketService.disconnect()
  ElMessage.info('WebSocket已断开')
}

const subscribeSymbol = () => {
  if (!newSymbol.value.trim()) {
    ElMessage.warning('请输入股票代码')
    return
  }
  
  subscribe(newSymbol.value.trim())
  ElMessage.success(`已订阅 ${newSymbol.value}`)
  newSymbol.value = ''
}

const unsubscribeSymbol = (symbol: string) => {
  unsubscribe(symbol)
  ElMessage.info(`已取消订阅 ${symbol}`)
}

const subscribeTestSymbols = () => {
  testSymbols.forEach(symbol => subscribe(symbol))
  ElMessage.success(`已订阅 ${testSymbols.length} 只测试股票`)
}

const clearData = () => {
  latestQuotes.value.clear()
  ElMessage.info('数据已清空')
}

const getPriceClass = (row: any) => {
  if (!row.change) return ''
  return row.change > 0 ? 'price-up' : row.change < 0 ? 'price-down' : ''
}

const getChangeClass = (change: number) => {
  if (!change) return ''
  return change > 0 ? 'change-up' : change < 0 ? 'change-down' : ''
}

const formatTime = (timestamp?: number) => {
  if (!timestamp) return '无'
  return new Date(timestamp).toLocaleTimeString()
}

const formatDuration = () => {
  if (!connectionStats.value.lastMessageTime) return '无'
  const duration = Date.now() - connectionStats.value.lastMessageTime
  if (duration < 60000) return `${Math.floor(duration / 1000)}秒前`
  return `${Math.floor(duration / 60000)}分钟前`
}

// 生命周期
onMounted(() => {
  // 自动连接
  if (!isConnected.value && !isConnecting.value) {
    connectWebSocket()
  }
})
</script>

<style scoped>
.websocket-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.status-section,
.control-section,
.subscribe-section,
.data-section,
.stats-section {
  margin-bottom: 24px;
}

.subscribed-symbols {
  margin-top: 12px;
}

.price-up {
  color: #f56c6c;
}

.price-down {
  color: #67c23a;
}

.change-up {
  color: #f56c6c;
}

.change-down {
  color: #67c23a;
}

h3 {
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
}

h4 {
  margin: 8px 0;
  color: var(--el-text-color-secondary);
}
</style>
