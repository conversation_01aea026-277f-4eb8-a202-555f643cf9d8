#!/usr/bin/env python3
"""
行情API对齐修复脚本
修复WebSocket推送机制和前端API路径问题
"""

import os
import re
from pathlib import Path

def fix_websocket_push_mechanism():
    """修复WebSocket推送机制"""
    print("🔌 修复WebSocket推送机制...")
    
    websocket_file = Path("backend/app/api/v1/websocket_enhanced.py")
    if not websocket_file.exists():
        print("❌ WebSocket文件不存在")
        return False
    
    with open(websocket_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复市场数据推送：从 send_personal_message 改为 broadcast_to_topic
    old_pattern = r'await enhanced_manager\.send_personal_message\(client_id, market_data\)'
    new_code = '''# 广播到市场数据主题
            await enhanced_manager.broadcast_to_topic("market:all", market_data)
            
            # 如果有特定股票数据，也广播到对应主题
            if "data" in market_data and "stocks" in market_data["data"]:
                for stock in market_data["data"]["stocks"][:5]:  # 限制前5只股票
                    symbol = stock.get("symbol")
                    if symbol:
                        await enhanced_manager.broadcast_to_topic(f"market:{symbol}", {
                            "type": "tick",
                            "symbol": symbol,
                            "data": stock,
                            "timestamp": market_data["timestamp"]
                        })'''
    
    if re.search(old_pattern, content):
        content = re.sub(old_pattern, new_code, content)
        print("  ✅ 修复市场数据推送机制")
        
        with open(websocket_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
    else:
        print("  ℹ️ 未找到需要修复的推送代码")
        return False

def create_frontend_api_adapter():
    """创建前端API适配器"""
    print("\n🎨 创建前端API适配器...")
    
    adapter_content = '''/**
 * 行情API适配器
 * 统一前端API调用，实现增强版->基础版->Mock的降级机制
 */

import { httpClient } from '@/api/http'
import type { ApiResponse } from '@/types/api'

// API路径配置
const API_PATHS = {
  // 增强版API路径（优先）
  enhanced: {
    stock: (symbol: string) => `/market/enhanced-market/stock/${symbol}`,
    quotes: (symbols: string) => `/market/enhanced-market/quotes?symbols=${symbols}`,
    kline: (symbol: string, period = '1d', limit = 100) => 
      `/market/enhanced-market/kline/${symbol}?period=${period}&limit=${limit}`,
    search: (query: string, limit = 20) => 
      `/market/enhanced-market/search?query=${query}&limit=${limit}`,
    watchlist: () => `/market/enhanced-market/watchlist`,
    addWatchlist: (symbol: string) => `/market/enhanced-market/watchlist?symbol=${symbol}`,
    removeWatchlist: (symbol: string) => `/market/enhanced-market/watchlist/${symbol}`,
    health: () => `/market/enhanced-market/health`
  },
  
  // 基础版API路径（fallback）
  basic: {
    stock: (symbol: string) => `/market/quotes/${symbol}`,
    quotes: (symbols: string) => `/market/quotes/realtime?symbols=${symbols}`,
    kline: (symbol: string, period = '1d', limit = 100) => 
      `/market/kline/${symbol}?period=${period}`,
    search: (query: string, limit = 20) => 
      `/market/search?keyword=${query}&limit=${limit}`,
    stockList: (market?: string, limit = 100) => 
      `/market/stocks/list?${market ? `market=${market}&` : ''}limit=${limit}`,
    overview: () => `/market/overview`,
    sectors: () => `/market/sectors/performance`,
    watchlist: () => `/market/watchlist`,
    addWatchlist: (symbol: string) => `/market/watchlist/${symbol}`,
    removeWatchlist: (symbol: string) => `/market/watchlist/${symbol}`,
    health: () => `/market/health`
  }
}

// 通用API调用函数
async function callWithFallback<T>(
  enhancedPath: string,
  basicPath: string,
  method: 'GET' | 'POST' | 'DELETE' = 'GET',
  data?: any
): Promise<ApiResponse<T>> {
  try {
    // 1. 尝试增强版API
    const response = await httpClient.request({
      method,
      url: enhancedPath,
      data
    })
    
    console.log(`✅ 增强版API成功: ${enhancedPath}`)
    return response.data
    
  } catch (enhancedError) {
    console.warn(`⚠️ 增强版API失败: ${enhancedPath}`, enhancedError)
    
    try {
      // 2. 降级到基础版API
      const response = await httpClient.request({
        method,
        url: basicPath,
        data
      })
      
      console.log(`✅ 基础版API成功: ${basicPath}`)
      return response.data
      
    } catch (basicError) {
      console.error(`❌ 基础版API也失败: ${basicPath}`, basicError)
      
      // 3. 返回Mock数据或抛出错误
      throw new Error(`所有API都失败: ${enhancedError.message}`)
    }
  }
}

// 导出的API方法
export const marketApi = {
  // 获取股票详情
  async getStock(symbol: string) {
    return callWithFallback(
      API_PATHS.enhanced.stock(symbol),
      API_PATHS.basic.stock(symbol)
    )
  },

  // 获取批量行情
  async getQuotes(symbols: string[]) {
    const symbolsStr = symbols.join(',')
    return callWithFallback(
      API_PATHS.enhanced.quotes(symbolsStr),
      API_PATHS.basic.quotes(symbolsStr)
    )
  },

  // 获取K线数据
  async getKlineData(symbol: string, period = '1d', limit = 100) {
    return callWithFallback(
      API_PATHS.enhanced.kline(symbol, period, limit),
      API_PATHS.basic.kline(symbol, period, limit)
    )
  },

  // 搜索股票
  async searchStocks(query: string, limit = 20) {
    return callWithFallback(
      API_PATHS.enhanced.search(query, limit),
      API_PATHS.basic.search(query, limit)
    )
  },

  // 获取股票列表（仅基础版有）
  async getStockList(market?: string, limit = 100) {
    try {
      const response = await httpClient.get(API_PATHS.basic.stockList(market, limit))
      return response.data
    } catch (error) {
      console.error('获取股票列表失败:', error)
      throw error
    }
  },

  // 获取市场概览（仅基础版有）
  async getMarketOverview() {
    try {
      const response = await httpClient.get(API_PATHS.basic.overview())
      return response.data
    } catch (error) {
      console.error('获取市场概览失败:', error)
      throw error
    }
  },

  // 获取板块表现（仅基础版有）
  async getSectorPerformance() {
    try {
      const response = await httpClient.get(API_PATHS.basic.sectors())
      return response.data
    } catch (error) {
      console.error('获取板块表现失败:', error)
      throw error
    }
  },

  // 自选股管理
  async getWatchlist() {
    return callWithFallback(
      API_PATHS.enhanced.watchlist(),
      API_PATHS.basic.watchlist()
    )
  },

  async addToWatchlist(symbol: string) {
    return callWithFallback(
      API_PATHS.enhanced.addWatchlist(symbol),
      API_PATHS.basic.addWatchlist(symbol),
      'POST'
    )
  },

  async removeFromWatchlist(symbol: string) {
    return callWithFallback(
      API_PATHS.enhanced.removeWatchlist(symbol),
      API_PATHS.basic.removeWatchlist(symbol),
      'DELETE'
    )
  },

  // 健康检查
  async checkHealth() {
    return callWithFallback(
      API_PATHS.enhanced.health(),
      API_PATHS.basic.health()
    )
  }
}

// WebSocket主题配置
export const WS_TOPICS = {
  MARKET_ALL: 'market:all',
  MARKET_SYMBOL: (symbol: string) => `market:${symbol}`,
  KLINE: (symbol: string, interval: string) => `market:${symbol}:kline:${interval}`
}

// WebSocket订阅辅助函数
export function createSubscribeMessage(topics: string[]) {
  return {
    type: 'subscribe',
    data: { topics }
  }
}

export function createUnsubscribeMessage(topics: string[]) {
  return {
    type: 'unsubscribe', 
    data: { topics }
  }
}

export default marketApi
'''
    
    adapter_path = Path("frontend/src/api/marketApiAdapter.ts")
    adapter_path.parent.mkdir(exist_ok=True)
    
    with open(adapter_path, 'w', encoding='utf-8') as f:
        f.write(adapter_content)
    
    print("✅ 前端API适配器已创建: frontend/src/api/marketApiAdapter.ts")
    return True

def create_websocket_topic_guide():
    """创建WebSocket主题使用指南"""
    print("\n📋 创建WebSocket主题使用指南...")
    
    guide_content = '''# WebSocket主题使用指南

## 🎯 主题命名规范

### 1. 全量市场数据
- **主题**: `market:all`
- **用途**: 接收市场整体数据，包括主要指数、热门股票等
- **推送频率**: 每秒1次
- **数据格式**:
```json
{
  "type": "market_overview",
  "topic": "market:all", 
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "indices": [...],
    "stocks": [...],
    "sectors": [...]
  }
}
```

### 2. 单股票实时数据
- **主题**: `market:{symbol}`
- **用途**: 接收特定股票的实时tick数据
- **推送频率**: 有变化时推送
- **数据格式**:
```json
{
  "type": "tick",
  "topic": "market:000001",
  "symbol": "000001",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "currentPrice": 12.34,
    "change": 0.12,
    "changePercent": 0.98,
    "volume": 1234567
  }
}
```

### 3. K线数据推送
- **主题**: `market:{symbol}:kline:{interval}`
- **用途**: 接收K线数据更新
- **推送频率**: K线周期结束时推送
- **数据格式**:
```json
{
  "type": "kline",
  "topic": "market:000001:kline:1d",
  "symbol": "000001",
  "interval": "1d",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "open": 12.00,
    "high": 12.50,
    "low": 11.80,
    "close": 12.34,
    "volume": 1234567
  }
}
```

## 🔌 前端订阅示例

### Vue组合式API使用
```typescript
import { useWebSocket } from '@/composables/useWebSocket'
import { WS_TOPICS, createSubscribeMessage } from '@/api/marketApiAdapter'

export default {
  setup() {
    const { connect, subscribe, unsubscribe, onMessage } = useWebSocket()
    
    // 连接WebSocket
    connect('ws://localhost:8000/api/v1/ws')
    
    // 订阅全量市场数据
    subscribe([WS_TOPICS.MARKET_ALL])
    
    // 订阅特定股票
    subscribe([WS_TOPICS.MARKET_SYMBOL('000001')])
    
    // 监听消息
    onMessage((data) => {
      if (data.type === 'tick') {
        // 处理实时行情
        updateStockPrice(data.symbol, data.data)
      } else if (data.type === 'market_overview') {
        // 处理市场概览
        updateMarketOverview(data.data)
      }
    })
    
    return { ... }
  }
}
```

### 在Pinia Store中使用
```typescript
// stores/market.ts
import { defineStore } from 'pinia'
import { useWebSocket } from '@/composables/useWebSocket'
import { WS_TOPICS } from '@/api/marketApiAdapter'

export const useMarketStore = defineStore('market', () => {
  const { subscribe, onMessage } = useWebSocket()
  
  // 初始化WebSocket订阅
  const initWebSocket = () => {
    // 订阅全量数据
    subscribe([WS_TOPICS.MARKET_ALL])
    
    // 监听消息并更新store
    onMessage((data) => {
      if (data.topic === WS_TOPICS.MARKET_ALL) {
        updateMarketData(data.data)
      }
    })
  }
  
  return { initWebSocket, ... }
})
```

## 🔧 后端推送实现

### 推送全量市场数据
```python
# 在后端服务中
await enhanced_manager.broadcast_to_topic("market:all", {
    "type": "market_overview",
    "timestamp": datetime.now().isoformat(),
    "data": market_data
})
```

### 推送单股票数据
```python
await enhanced_manager.broadcast_to_topic(f"market:{symbol}", {
    "type": "tick", 
    "symbol": symbol,
    "timestamp": datetime.now().isoformat(),
    "data": stock_data
})
```

## 📊 性能优化建议

1. **订阅管理**: 只订阅需要的主题，及时取消不需要的订阅
2. **消息过滤**: 在前端对消息进行适当的过滤和去重
3. **批量更新**: 将多个小更新合并为批量更新
4. **内存管理**: 定期清理过期的历史数据

## 🚨 错误处理

### 连接断开重连
```typescript
const { connect, isConnected } = useWebSocket()

// 监听连接状态
watch(isConnected, (connected) => {
  if (!connected) {
    // 重新连接并重新订阅
    setTimeout(() => {
      connect('ws://localhost:8000/api/v1/ws')
      // 重新订阅之前的主题
    }, 3000)
  }
})
```

### 消息处理错误
```typescript
onMessage((data) => {
  try {
    // 处理消息
    handleMessage(data)
  } catch (error) {
    console.error('处理WebSocket消息失败:', error)
    // 记录错误但不中断其他消息处理
  }
})
```
'''
    
    guide_path = Path("docs/websocket-topics-guide.md")
    guide_path.parent.mkdir(exist_ok=True)
    
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ WebSocket主题使用指南已创建: docs/websocket-topics-guide.md")
    return True

def main():
    """主函数"""
    print("🚀 开始行情API对齐修复...\n")
    
    success_count = 0
    
    # 1. 修复WebSocket推送机制
    if fix_websocket_push_mechanism():
        success_count += 1
    
    # 2. 创建前端API适配器
    if create_frontend_api_adapter():
        success_count += 1
    
    # 3. 创建WebSocket使用指南
    if create_websocket_topic_guide():
        success_count += 1
    
    print(f"\n📊 修复结果: {success_count}/3 完成")
    
    if success_count >= 2:
        print("🎉 行情API对齐修复基本完成！")
        print("\n📋 下一步操作:")
        print("1. 重启后端服务以应用WebSocket修复")
        print("2. 在前端使用新的marketApiAdapter替代原有API调用")
        print("3. 参考WebSocket主题指南实现实时数据订阅")
        print("4. 测试API降级机制是否正常工作")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
