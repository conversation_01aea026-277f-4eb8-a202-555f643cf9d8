# API接口文档模板

## 接口概述
- **接口名称**: [接口名称]
- **接口路径**: [HTTP方法] /api/v1/[路径]
- **接口描述**: [简要描述接口功能]
- **开发者**: [开发者姓名]
- **创建时间**: [YYYY-MM-DD]
- **更新时间**: [YYYY-MM-DD]

## 请求参数

### 路径参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | int | 是 | 资源ID | 123 |

### 查询参数
| 参数名 | 类型 | 必填 | 描述 | 示例 | 默认值 |
|--------|------|------|------|------|--------|
| page | int | 否 | 页码 | 1 | 1 |
| size | int | 否 | 每页数量 | 20 | 10 |

### 请求体
```json
{
  "field1": "value1",
  "field2": "value2"
}
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "detail": "detailed error information"
}
```

## 状态码说明
| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### cURL
```bash
curl -X GET "http://localhost:8000/api/v1/example" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"
```

### Python
```python
import requests

response = requests.get(
    "http://localhost:8000/api/v1/example",
    headers={"Authorization": "Bearer your-token"}
)
print(response.json())
```

### JavaScript
```javascript
fetch('/api/v1/example', {
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项
- [特殊说明]
- [使用限制]
- [性能考虑]
