<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化平台系统验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log { 
            background-color: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace; 
            font-size: 12px; 
            max-height: 300px; 
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 量化投资平台系统验证</h1>
        <p>这是一个系统验证页面，用于测试后端API和前端功能的实际工作状态</p>
    </div>

    <div class="container">
        <h2>📊 后端API测试</h2>
        <div class="test-section">
            <h3>基础API连接测试</h3>
            <button onclick="testBasicAPI()">测试基础连接</button>
            <button onclick="testHealthCheck()">健康检查</button>
            <button onclick="testCORS()">CORS测试</button>
            <div id="basic-results"></div>
        </div>
        
        <div class="test-section">
            <h3>历史数据API测试</h3>
            <button onclick="testHistoricalData()">获取股票列表</button>
            <button onclick="testMarketStats()">市场统计</button>
            <button onclick="testSearchStocks()">搜索股票</button>
            <div id="historical-results"></div>
        </div>

        <div class="test-section">
            <h3>实时行情测试</h3>
            <button onclick="testWebSocketConnection()">WebSocket连接</button>
            <button onclick="testMarketData()">实时行情数据</button>
            <div id="realtime-results"></div>
        </div>
    </div>

    <div class="container">
        <h2>🖥️ 前端功能测试</h2>
        <div class="test-section">
            <h3>路由测试</h3>
            <button onclick="testRoutes()">检查页面路由</button>
            <div id="route-results"></div>
        </div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <button onclick="clearLog()">清除日志</button>
        <div id="test-log" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const WS_URL = 'ws://localhost:8000/api/v1/ws';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('test-log');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        async function testBasicAPI() {
            log('开始测试基础API连接...');
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                if (response.ok) {
                    showResult('basic-results', `✅ 基础连接成功: ${data.project} v${data.version}`, 'success');
                    log('基础API连接成功');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                showResult('basic-results', `❌ 基础连接失败: ${error.message}`, 'error');
                log(`基础API连接失败: ${error.message}`, 'error');
            }
        }

        async function testHealthCheck() {
            log('测试健康检查...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                if (response.ok && data.status === 'healthy') {
                    showResult('basic-results', `✅ 健康检查通过: ${data.services.api}`, 'success');
                    log('健康检查通过');
                } else {
                    throw new Error(data.error || 'Health check failed');
                }
            } catch (error) {
                showResult('basic-results', `❌ 健康检查失败: ${error.message}`, 'error');
                log(`健康检查失败: ${error.message}`, 'error');
            }
        }

        async function testCORS() {
            log('测试CORS配置...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/cors/test`);
                const data = await response.json();
                if (response.ok && data.success) {
                    showResult('basic-results', `✅ CORS配置正确: ${data.message}`, 'success');
                    log('CORS配置正确');
                } else {
                    throw new Error('CORS test failed');
                }
            } catch (error) {
                showResult('basic-results', `❌ CORS配置问题: ${error.message}`, 'error');
                log(`CORS配置问题: ${error.message}`, 'error');
            }
        }

        async function testHistoricalData() {
            log('测试历史数据API...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/market/historical/stocks?page=1&page_size=5`);
                const data = await response.json();
                if (response.ok && data.success) {
                    const count = data.data.stocks ? data.data.stocks.length : 0;
                    showResult('historical-results', `✅ 获取股票列表成功: 共${data.data.total}只股票，返回${count}条`, 'success');
                    log(`历史数据API工作正常，获取到${count}条股票数据`);
                } else {
                    throw new Error(data.message || 'API返回错误');
                }
            } catch (error) {
                showResult('historical-results', `❌ 历史数据获取失败: ${error.message}`, 'error');
                log(`历史数据API失败: ${error.message}`, 'error');
            }
        }

        async function testMarketStats() {
            log('测试市场统计API...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/market/historical/stats`);
                const data = await response.json();
                if (response.ok && data.success) {
                    const stats = data.data;
                    showResult('historical-results', `✅ 市场统计获取成功: 总股票${stats.total_stocks}只`, 'success');
                    log('市场统计API工作正常');
                } else {
                    throw new Error(data.message || 'Stats API返回错误');
                }
            } catch (error) {
                showResult('historical-results', `❌ 市场统计获取失败: ${error.message}`, 'error');
                log(`市场统计API失败: ${error.message}`, 'error');
            }
        }

        async function testSearchStocks() {
            log('测试股票搜索API...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/market/historical/search?keyword=600000&limit=5`);
                const data = await response.json();
                if (response.ok && data.success) {
                    const count = Array.isArray(data.data) ? data.data.length : 0;
                    showResult('historical-results', `✅ 股票搜索成功: 找到${count}个结果`, 'success');
                    log(`股票搜索API工作正常，搜索"600000"找到${count}个结果`);
                } else {
                    throw new Error(data.message || 'Search API返回错误');
                }
            } catch (error) {
                showResult('historical-results', `❌ 股票搜索失败: ${error.message}`, 'error');
                log(`股票搜索API失败: ${error.message}`, 'error');
            }
        }

        async function testWebSocketConnection() {
            log('测试WebSocket连接...');
            try {
                const ws = new WebSocket(WS_URL);
                
                ws.onopen = function() {
                    showResult('realtime-results', '✅ WebSocket连接成功', 'success');
                    log('WebSocket连接建立成功');
                    
                    // 发送测试消息
                    ws.send(JSON.stringify({
                        type: 'test',
                        message: 'Hello WebSocket'
                    }));
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    showResult('realtime-results', `✅ WebSocket消息接收: ${data.type}`, 'success');
                    log(`收到WebSocket消息: ${data.type} - ${data.message || ''}`);
                    
                    // 测试完成后关闭连接
                    setTimeout(() => ws.close(), 2000);
                };
                
                ws.onerror = function(error) {
                    showResult('realtime-results', `❌ WebSocket错误: ${error}`, 'error');
                    log(`WebSocket连接错误: ${error}`, 'error');
                };
                
                ws.onclose = function() {
                    showResult('realtime-results', '🔌 WebSocket连接已关闭', 'info');
                    log('WebSocket连接已关闭');
                };
                
            } catch (error) {
                showResult('realtime-results', `❌ WebSocket测试失败: ${error.message}`, 'error');
                log(`WebSocket测试失败: ${error.message}`, 'error');
            }
        }

        async function testMarketData() {
            log('测试实时行情数据API...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/market/quote/000001.SZ`);
                const data = await response.json();
                if (response.ok && data.success) {
                    showResult('realtime-results', `✅ 实时行情获取成功: ${data.data.name}`, 'success');
                    log('实时行情API工作正常');
                } else {
                    throw new Error(data.message || '行情API返回错误');
                }
            } catch (error) {
                showResult('realtime-results', `❌ 实时行情获取失败: ${error.message}`, 'error');
                log(`实时行情API失败: ${error.message}`, 'error');
            }
        }

        function testRoutes() {
            log('检查前端路由配置...');
            const routes = [
                { path: '/', name: '首页' },
                { path: '/market', name: '行情中心' },
                { path: '/market/historical', name: '历史数据' },
                { path: '/market/realtime', name: '实时行情' },
                { path: '/api-test', name: 'API测试' }
            ];
            
            routes.forEach(route => {
                const link = document.createElement('a');
                link.href = route.path;
                link.target = '_blank';
                link.textContent = `${route.name} (${route.path})`;
                link.style.display = 'block';
                link.style.margin = '5px 0';
                link.style.color = '#007bff';
                
                showResult('route-results', '', 'info');
                const lastResult = document.getElementById('route-results').lastElementChild;
                lastResult.appendChild(link);
            });
            
            log('前端路由链接已生成，请点击测试');
        }

        // 页面加载完成后显示欢迎信息
        window.onload = function() {
            log('系统验证页面加载完成，请开始测试');
            showResult('basic-results', '👋 欢迎使用系统验证工具', 'info');
        };
    </script>
</body>
</html>