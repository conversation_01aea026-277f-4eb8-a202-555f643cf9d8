"""
增强版市场数据API - V2版本
实现完整的REST权威路径表，支持多数据源切换
路径前缀: /api/v1/market/enhanced-market/*
"""

import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Depends, Path
from fastapi.responses import JSONResponse

from app.services.data_source_manager import data_source_manager, DataSourceManager
from app.core.exceptions import DataSourceException, MarketDataException
from app.schemas.response import ApiResponse, create_success_response, create_error_response
from app.schemas.market_data import (
    StockListResponse, 
    QuoteResponse, 
    KlineResponse, 
    SectorResponse,
    SearchResponse,
    MarketOverviewResponse,
    RankingResponse,
    IndexResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/enhanced-market", tags=["Enhanced Market Data"])


def get_data_source_manager() -> DataSourceManager:
    """获取数据源管理器依赖"""
    return data_source_manager


@router.get("/stocks", response_model=StockListResponse)
async def get_stocks(
    market: Optional[str] = Query(None, description="市场代码，如SH、SZ"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=1000, description="每页数量"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取股票列表
    
    - **market**: 市场代码（SH=上海，SZ=深圳）
    - **page**: 页码，从1开始
    - **size**: 每页数量，最大1000
    """
    try:
        logger.info(f"Fetching stocks: market={market}, page={page}, size={size}")
        
        # 获取股票列表
        all_stocks = await ds_manager.get_stock_list(market)
        
        # 分页处理
        start_idx = (page - 1) * size
        end_idx = start_idx + size
        stocks = all_stocks[start_idx:end_idx]
        
        return create_success_response({
            "stocks": stocks,
            "pagination": {
                "page": page,
                "size": size,
                "total": len(all_stocks),
                "pages": (len(all_stocks) + size - 1) // size
            },
            "data_source": ds_manager.current_source
        })
        
    except DataSourceException as e:
        logger.error(f"Data source error: {e}")
        raise HTTPException(status_code=503, detail=f"Data source unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching stocks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/quotes", response_model=QuoteResponse)
async def get_batch_quotes(
    symbols: str = Query(..., description="股票代码列表，逗号分隔，如000001,000002"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    批量获取股票行情
    
    - **symbols**: 股票代码列表，用逗号分隔，如"000001,000002,600000"
    """
    try:
        symbol_list = [s.strip() for s in symbols.split(",") if s.strip()]
        if not symbol_list:
            raise HTTPException(status_code=400, detail="No valid symbols provided")
        
        if len(symbol_list) > 100:
            raise HTTPException(status_code=400, detail="Too many symbols, maximum 100")
        
        logger.info(f"Fetching batch quotes for {len(symbol_list)} symbols")
        
        quotes = await ds_manager.get_batch_quotes(symbol_list)
        
        return create_success_response({
            "quotes": quotes,
            "count": len(quotes),
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except DataSourceException as e:
        logger.error(f"Data source error: {e}")
        raise HTTPException(status_code=503, detail=f"Data source unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching batch quotes: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/quotes/{symbol}", response_model=QuoteResponse)
async def get_quote(
    symbol: str = Path(..., description="股票代码"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取单只股票实时行情
    
    - **symbol**: 股票代码，如000001、600000
    """
    try:
        logger.info(f"Fetching quote for symbol: {symbol}")
        
        quote = await ds_manager.get_realtime_quote(symbol)
        
        return create_success_response({
            "quote": quote,
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except DataSourceException as e:
        logger.error(f"Data source error: {e}")
        raise HTTPException(status_code=503, detail=f"Data source unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching quote for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/kline/{symbol}", response_model=KlineResponse)
async def get_kline(
    symbol: str = Path(..., description="股票代码"),
    period: str = Query("1d", description="K线周期，如1d、1h、5m"),
    limit: int = Query(100, ge=1, le=1000, description="数据条数，最大1000"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取K线数据
    
    - **symbol**: 股票代码
    - **period**: K线周期（1d=日线，1h=小时线，5m=5分钟线）
    - **limit**: 返回数据条数，最大1000
    """
    try:
        logger.info(f"Fetching kline for {symbol}: period={period}, limit={limit}")
        
        klines = await ds_manager.get_kline_data(symbol, period, limit)
        
        return create_success_response({
            "symbol": symbol,
            "period": period,
            "klines": klines,
            "count": len(klines),
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except DataSourceException as e:
        logger.error(f"Data source error: {e}")
        raise HTTPException(status_code=503, detail=f"Data source unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching kline for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sectors", response_model=SectorResponse)
async def get_sectors(
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取板块列表
    """
    try:
        logger.info("Fetching sectors")
        
        # 模拟板块数据
        sectors = [
            {"code": "technology", "name": "科技股", "stock_count": 150, "change_pct": 2.34},
            {"code": "finance", "name": "金融股", "stock_count": 80, "change_pct": -1.23},
            {"code": "healthcare", "name": "医疗健康", "stock_count": 120, "change_pct": 1.56},
            {"code": "consumer", "name": "消费股", "stock_count": 200, "change_pct": 0.89},
        ]
        
        return create_success_response({
            "sectors": sectors,
            "count": len(sectors),
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error fetching sectors: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sectors/{sector_code}/stocks")
async def get_sector_stocks(
    sector_code: str = Path(..., description="板块代码"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取板块成分股
    
    - **sector_code**: 板块代码
    - **page**: 页码
    - **size**: 每页数量
    """
    try:
        logger.info(f"Fetching stocks for sector: {sector_code}")
        
        # 模拟板块成分股数据
        if sector_code == "technology":
            stocks = [
                {"symbol": "000001", "name": "平安银行", "price": 12.34, "change_pct": 2.45},
                {"symbol": "000002", "name": "万科A", "price": 8.56, "change_pct": -1.23},
            ]
        else:
            stocks = []
        
        # 分页处理
        start_idx = (page - 1) * size
        end_idx = start_idx + size
        paginated_stocks = stocks[start_idx:end_idx]
        
        return create_success_response({
            "sector_code": sector_code,
            "stocks": paginated_stocks,
            "pagination": {
                "page": page,
                "size": size,
                "total": len(stocks),
                "pages": (len(stocks) + size - 1) // size
            },
            "data_source": ds_manager.current_source
        })
        
    except Exception as e:
        logger.error(f"Error fetching sector stocks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search", response_model=SearchResponse)
async def search_stocks(
    q: str = Query(..., description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    搜索股票
    
    - **q**: 搜索关键词（股票代码或名称）
    - **limit**: 返回结果数量
    """
    try:
        logger.info(f"Searching stocks with keyword: {q}")
        
        results = await ds_manager.search_stocks(q)
        
        # 限制返回数量
        results = results[:limit]
        
        return create_success_response({
            "keyword": q,
            "results": results,
            "count": len(results),
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except DataSourceException as e:
        logger.error(f"Data source error: {e}")
        raise HTTPException(status_code=503, detail=f"Data source unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error searching stocks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/overview", response_model=MarketOverviewResponse)
async def get_market_overview(
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取市场概览
    """
    try:
        logger.info("Fetching market overview")
        
        # 模拟市场概览数据
        overview = {
            "total_stocks": 4500,
            "trading_stocks": 4200,
            "gainers": 2100,
            "losers": 1800,
            "unchanged": 300,
            "total_volume": 123456789012,
            "total_turnover": 987654321098.76,
            "indices": {
                "sh_composite": {"value": 3245.67, "change": 12.34, "change_pct": 0.38},
                "sz_component": {"value": 11234.56, "change": -23.45, "change_pct": -0.21},
                "hs300": {"value": 4567.89, "change": 8.90, "change_pct": 0.20}
            }
        }
        
        return create_success_response({
            "overview": overview,
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error fetching market overview: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/ranking", response_model=RankingResponse)
async def get_market_ranking(
    type: str = Query("gainers", description="排行榜类型：gainers（涨幅榜）、losers（跌幅榜）、volume（成交量榜）、turnover（成交额榜）"),
    limit: int = Query(10, ge=1, le=100, description="返回数量"),
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取涨跌排行榜
    
    - **type**: 排行榜类型
      - gainers: 涨幅榜
      - losers: 跌幅榜  
      - volume: 成交量榜
      - turnover: 成交额榜
    - **limit**: 返回数量
    """
    try:
        logger.info(f"Fetching ranking: type={type}, limit={limit}")
        
        # 模拟排行榜数据
        import random
        ranking = []
        for i in range(limit):
            if type == "gainers":
                change_pct = round(random.uniform(5, 15), 2)
            elif type == "losers":
                change_pct = round(random.uniform(-15, -5), 2)
            else:
                change_pct = round(random.uniform(-5, 5), 2)
            
            ranking.append({
                "rank": i + 1,
                "symbol": f"00000{i+1:02d}",
                "name": f"股票{i+1}",
                "price": round(random.uniform(5, 50), 2),
                "change_pct": change_pct,
                "volume": random.randint(10000000, 500000000),
                "turnover": round(random.uniform(100000000, 2000000000), 2)
            })
        
        return create_success_response({
            "type": type,
            "ranking": ranking,
            "count": len(ranking),
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error fetching ranking: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/indices", response_model=IndexResponse)
async def get_indices(
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取指数列表
    """
    try:
        logger.info("Fetching indices")
        
        # 模拟指数数据
        indices = [
            {"code": "000001", "name": "上证指数", "value": 3245.67, "change": 12.34, "change_pct": 0.38},
            {"code": "399001", "name": "深证成指", "value": 11234.56, "change": -23.45, "change_pct": -0.21},
            {"code": "000300", "name": "沪深300", "value": 4567.89, "change": 8.90, "change_pct": 0.20},
            {"code": "399006", "name": "创业板指", "value": 2345.67, "change": 15.67, "change_pct": 0.67},
        ]
        
        return create_success_response({
            "indices": indices,
            "count": len(indices),
            "data_source": ds_manager.current_source,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error fetching indices: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/status")
async def get_data_source_status(
    ds_manager: DataSourceManager = Depends(get_data_source_manager)
) -> JSONResponse:
    """
    获取数据源状态
    """
    try:
        status = await ds_manager.get_source_status()
        
        return create_success_response({
            "current_source": ds_manager.current_source,
            "sources": {k: v.__dict__ for k, v in status.items()},
            "priority_order": ds_manager.priority_order,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error fetching data source status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")