# 行情中心验收检查清单

## 环境准备检查

### 1. 后端环境配置
- [ ] `.env` 文件已更新
  - [ ] `USE_REAL_DATA=true`
  - [ ] `TUSHARE_TOKEN` 已配置实际token
  - [ ] `AKSHARE_ENABLED=true`
  - [ ] `REDIS_URL` 已启用
  - [ ] `MARKET_DATA_CACHE_TTL=300`

### 2. 前端环境配置
- [ ] `.env.development` 文件已更新
  - [ ] `VITE_API_BASE_URL=http://localhost:8000/api/v1`
  - [ ] `VITE_WS_URL=ws://localhost:8000/api/v1/ws`
  - [ ] `VITE_USE_MOCK=false`
  - [ ] `VITE_USE_ENHANCED_API=true`

### 3. 依赖安装检查
- [ ] 后端依赖已安装
  ```bash
  pip install tushare akshare pandas-ta
  ```
- [ ] Redis服务已启动
  ```bash
  redis-server --port 6379
  ```
- [ ] 前端依赖完整 (已确认)

## REST API验证

### 1. 基础连通性测试

#### Swagger文档访问
- [ ] 访问 `http://localhost:8000/docs` 正常
- [ ] API文档加载完整
- [ ] 可以看到 `/api/v1/market/` 相关接口

#### 健康检查
- [ ] `GET /api/v1/health` 返回200
- [ ] `GET /api/v1/market/enhanced-market/health` 返回数据源状态

### 2. 核心接口验证

#### 股票列表接口
```bash
curl "http://localhost:8000/api/v1/market/stocks/list?page=1&page_size=10"
```
- [ ] 返回状态码200
- [ ] 响应包含 `code`, `message`, `data` 字段
- [ ] `data.items` 包含股票列表
- [ ] 每个股票包含: `symbol`, `name`, `current_price`, `change`, `change_percent`

#### 实时行情接口
```bash
curl "http://localhost:8000/api/v1/market/quotes/000001"
```
- [ ] 返回状态码200
- [ ] 包含完整行情数据
- [ ] 价格数据合理 (非0值)

#### 批量行情接口
```bash
curl "http://localhost:8000/api/v1/market/quotes/realtime?symbols=000001,000002,600036"
```
- [ ] 返回状态码200
- [ ] 返回多只股票数据
- [ ] 数据结构一致

#### K线数据接口
```bash
curl "http://localhost:8000/api/v1/market/kline/000001?period=1d&limit=100"
```
- [ ] 返回状态码200
- [ ] 包含K线数组
- [ ] 每条K线包含: `timestamp`, `open`, `high`, `low`, `close`, `volume`

#### 市场概览接口
```bash
curl "http://localhost:8000/api/v1/market/overview"
```
- [ ] 返回状态码200
- [ ] 包含指数数据
- [ ] 包含市场统计

#### 板块数据接口
```bash
curl "http://localhost:8000/api/v1/market/sectors/performance"
```
- [ ] 返回状态码200
- [ ] 包含板块列表
- [ ] 每个板块包含涨跌幅数据

#### 搜索接口
```bash
curl "http://localhost:8000/api/v1/market/enhanced-market/search?query=平安&limit=10"
```
- [ ] 返回状态码200
- [ ] 返回搜索结果
- [ ] 结果相关性合理

#### 自选股接口
```bash
# 获取自选股
curl "http://localhost:8000/api/v1/market/watchlist"

# 添加自选股
curl -X POST "http://localhost:8000/api/v1/market/watchlist/000001"

# 删除自选股
curl -X DELETE "http://localhost:8000/api/v1/market/watchlist/000001"
```
- [ ] 获取自选股返回200
- [ ] 添加自选股成功
- [ ] 删除自选股成功

### 3. 数据质量验证

#### 数据源验证
- [ ] 检查日志确认使用的数据源 (Tushare/AkShare/Mock)
- [ ] 价格数据在合理范围内
- [ ] 时间戳为最新交易时间
- [ ] 成交量数据非零

#### 缓存验证
- [ ] Redis中存在缓存数据
  ```bash
  redis-cli keys "*market*"
  ```
- [ ] 重复请求响应时间明显缩短
- [ ] 缓存TTL设置正确

## WebSocket验证

### 1. 连接测试

#### 基础连接
使用浏览器控制台或WebSocket客户端工具:
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/ws?token=dev-token-for-testing');

ws.onopen = () => console.log('WebSocket连接成功');
ws.onmessage = (event) => console.log('收到消息:', JSON.parse(event.data));
ws.onerror = (error) => console.error('WebSocket错误:', error);
```

- [ ] 连接建立成功
- [ ] 收到欢迎消息
- [ ] 连接状态稳定

### 2. 心跳机制测试
```javascript
// 发送心跳
ws.send(JSON.stringify({type: 'ping'}));
```
- [ ] 收到 `pong` 响应
- [ ] 响应包含时间戳
- [ ] 心跳间隔正常 (30秒)

### 3. 订阅功能测试

#### 订阅全量行情
```javascript
ws.send(JSON.stringify({
  type: 'subscribe',
  data: {
    topics: ['market:all']
  }
}));
```
- [ ] 收到订阅确认消息
- [ ] 开始接收行情推送
- [ ] 推送频率合理 (3秒左右)

#### 订阅单只股票
```javascript
ws.send(JSON.stringify({
  type: 'subscribe',
  data: {
    topics: ['market:000001']
  }
}));
```
- [ ] 收到订阅确认
- [ ] 接收到该股票的实时数据
- [ ] 数据格式正确

#### 订阅K线数据
```javascript
ws.send(JSON.stringify({
  type: 'subscribe',
  data: {
    topics: ['market:000001:kline:1d']
  }
}));
```
- [ ] 收到订阅确认
- [ ] 接收到K线更新 (如果在交易时间)

#### 取消订阅
```javascript
ws.send(JSON.stringify({
  type: 'unsubscribe',
  data: {
    topics: ['market:000001']
  }
}));
```
- [ ] 收到取消订阅确认
- [ ] 停止接收该主题的推送

### 4. 错误处理测试
```javascript
// 发送无效消息
ws.send('invalid json');

// 订阅无效主题
ws.send(JSON.stringify({
  type: 'subscribe',
  data: {
    topics: ['invalid:topic']
  }
}));
```
- [ ] 收到错误响应
- [ ] 错误信息清晰
- [ ] 连接保持稳定

## 前端页面验证

### 1. 行情总览页面
访问: `http://localhost:5173/market`

#### 页面加载
- [ ] 页面正常加载，无白屏
- [ ] Loading状态显示正常
- [ ] 无JavaScript错误

#### 指数卡片
- [ ] 显示主要指数 (上证指数、深证成指等)
- [ ] 指数数据为真实数据 (非默认值)
- [ ] 涨跌颜色正确 (红涨绿跌)
- [ ] 数据定期更新

#### 市场趋势图
- [ ] 图表正常渲染
- [ ] 显示真实的趋势数据 (非静态样本)
- [ ] 时间周期切换功能正常
- [ ] 图表响应式适配

#### 股票列表
- [ ] 显示股票列表
- [ ] 数据为真实行情
- [ ] 搜索功能正常
- [ ] 分页功能正常
- [ ] 排序功能正常

#### 排行榜
- [ ] 涨幅榜显示数据
- [ ] 跌幅榜显示数据
- [ ] 成交额榜显示数据
- [ ] 数据排序正确

#### 板块数据
- [ ] 显示板块列表
- [ ] 板块涨跌幅数据正确
- [ ] 活跃板块突出显示

#### 自选股功能
- [ ] 可以添加自选股
- [ ] 可以删除自选股
- [ ] 自选股列表正确显示
- [ ] 自选股行情实时更新

### 2. 历史数据页面
访问: `http://localhost:5173/market/historical`

- [ ] 页面正常加载
- [ ] 历史数据查询功能正常
- [ ] 数据导出功能正常
- [ ] 统计分析显示正确

### 3. 实时性验证
- [ ] 页面数据定期自动更新
- [ ] WebSocket连接状态显示正确
- [ ] 断网重连功能正常
- [ ] 数据更新无明显延迟

## 性能验证

### 1. 响应时间
- [ ] API响应时间 < 500ms
- [ ] 页面首次加载 < 3s
- [ ] WebSocket连接建立 < 1s

### 2. 并发测试
- [ ] 多个WebSocket连接同时工作正常
- [ ] 高频API请求不会导致服务崩溃
- [ ] 缓存有效减少数据库压力

### 3. 内存使用
- [ ] 长时间运行内存使用稳定
- [ ] 无明显内存泄漏
- [ ] WebSocket连接数控制在合理范围

## 错误处理验证

### 1. 网络异常
- [ ] 断网时显示友好错误信息
- [ ] 网络恢复后自动重连
- [ ] 降级到缓存数据

### 2. 数据源异常
- [ ] Tushare服务不可用时降级到AkShare
- [ ] 所有数据源不可用时使用Mock数据
- [ ] 错误信息记录到日志

### 3. 参数错误
- [ ] 无效股票代码返回友好错误
- [ ] 参数缺失时返回明确提示
- [ ] 前端表单验证正常

## 日志检查

### 1. 后端日志
- [ ] 启动日志显示数据源初始化状态
- [ ] API请求日志记录完整
- [ ] 错误日志包含详细堆栈信息
- [ ] WebSocket连接日志正常

### 2. 前端控制台
- [ ] 无JavaScript错误
- [ ] WebSocket连接日志正常
- [ ] API请求日志清晰
- [ ] 缓存命中日志显示

## 验收标准

### 必须通过项 (阻塞性问题)
- [ ] 所有核心REST API返回200状态码
- [ ] WebSocket连接建立成功
- [ ] 前端页面正常加载显示数据
- [ ] 数据为真实行情 (非Mock)

### 建议通过项 (优化性问题)
- [ ] 响应时间满足性能要求
- [ ] 缓存机制工作正常
- [ ] 错误处理友好
- [ ] 日志记录完整

## 验收签字

- [ ] 开发人员自测完成: _________________ 日期: _________
- [ ] 测试人员验收通过: _________________ 日期: _________
- [ ] 产品经理确认: _________________ 日期: _________

## 备注

记录验收过程中发现的问题和解决方案:

```
问题1: 
解决方案: 

问题2:
解决方案:
```
