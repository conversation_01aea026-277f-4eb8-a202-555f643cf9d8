"""
最小化的FastAPI应用入口
仅包含基础功能，用于测试行情中心
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import random
from datetime import datetime

# 创建FastAPI应用
app = FastAPI(
    title="量化投资平台 - 最小版",
    description="最小化API用于行情中心测试",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 健康检查
@app.get("/api/v1/health")
async def health_check():
    return JSONResponse({
        "status": "ok",
        "message": "最小版API服务运行正常",
        "version": "1.0.0"
    })

# 模拟市场概览数据
@app.get("/api/v1/market/overview")
async def get_market_overview():
    return JSONResponse({
        "code": 200,
        "data": {
            "indices": [
                {
                    "code": "000001",
                    "name": "上证指数",
                    "current": 3245.68,
                    "change": 12.45,
                    "change_percent": 0.38,
                    "volume": 245680000,
                    "turnover": **********
                },
                {
                    "code": "399001", 
                    "name": "深证成指",
                    "current": 10856.34,
                    "change": -23.67,
                    "change_percent": -0.22,
                    "volume": 189450000,
                    "turnover": **********
                },
                {
                    "code": "399006",
                    "name": "创业板指", 
                    "current": 2234.56,
                    "change": 8.92,
                    "change_percent": 0.40,
                    "volume": 156780000,
                    "turnover": 1987654000
                }
            ],
            "stats": {
                "advancers": 1245,
                "decliners": 987, 
                "unchanged": 234
            }
        }
    })

# 模拟股票列表
@app.get("/api/v1/market/stocks")
async def get_stock_list():
    stocks = []
    symbols = ["000001", "000002", "600000", "600036", "000858", "002415"]
    names = ["平安银行", "万科A", "浦发银行", "招商银行", "五粮液", "海康威视"]
    
    for i, (symbol, name) in enumerate(zip(symbols, names)):
        change = round(random.uniform(-5, 5), 2)
        price = round(random.uniform(10, 100), 2)
        stocks.append({
            "symbol": symbol,
            "name": name,
            "currentPrice": price,
            "change": change,
            "changePercent": round(change / price * 100, 2),
            "volume": random.randint(1000000, 10000000),
            "amount": random.randint(100000000, 1000000000),
            "industry": "银行" if "银行" in name else "其他",
            "market": "SH" if symbol.startswith("6") else "SZ",
            "pinyin": name
        })
    
    return JSONResponse(stocks)

# 模拟板块数据
@app.get("/api/v1/market/sectors")
async def get_sectors():
    return JSONResponse({
        "code": 200,
        "data": {
            "sectors": [
                {"name": "银行", "change_percent": 1.2, "stock_count": 36},
                {"name": "医药生物", "change_percent": -0.8, "stock_count": 156},
                {"name": "食品饮料", "change_percent": 2.1, "stock_count": 78}
            ]
        }
    })

# 模拟排行榜数据
@app.get("/api/v1/market/rankings")
async def get_rankings():
    return JSONResponse({
        "code": 200,
        "data": {
            "rankings": [
                {
                    "rank": 1,
                    "symbol": "000001", 
                    "name": "平安银行",
                    "current_price": 12.35,
                    "change": 1.15,
                    "change_percent": 10.28,
                    "volume": 234567890
                }
            ]
        }
    })

# 模拟历史数据统计
@app.get("/api/v1/market/historical/stats")
async def get_historical_stats():
    return JSONResponse({
        "success": True,
        "data": {
            "total_stocks": 4800,
            "markets": {"SH": 1800, "SZ": 2500, "BJ": 500},
            "industries": {
                "银行": 36,
                "医药生物": 156, 
                "食品饮料": 78,
                "电子": 234,
                "计算机": 198
            },
            "data_range": {
                "start_date": "2020-01-01",
                "end_date": "2024-12-31"
            },
            "last_updated": datetime.now().isoformat()
        }
    })

# 模拟历史股票列表
@app.get("/api/v1/market/historical/stocks")
async def get_historical_stocks():
    stocks = []
    for i in range(50):
        symbol = f"{str(i).zfill(6)}"
        stocks.append({
            "symbol": symbol,
            "name": f"测试股票{i:03d}",
            "market": "SH" if i % 2 == 0 else "SZ",
            "industry": ["银行", "医药", "科技", "消费"][i % 4],
            "last_price": round(random.uniform(5, 50), 2),
            "change_percent": round(random.uniform(-5, 5), 2),
            "total_records": random.randint(500, 2000),
            "start_date": "2020-01-01",
            "end_date": "2024-12-31"
        })
    
    return JSONResponse({
        "success": True,
        "data": stocks,
        "total": len(stocks)
    })

if __name__ == "__main__":
    print("Starting minimal API server...")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        access_log=True
    )