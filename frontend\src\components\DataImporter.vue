<template>
  <div class="data-importer">
    <el-tabs v-model="activeTab" type="card">
      <!-- CSV文件导入 -->
      <el-tab-pane label="CSV文件导入" name="csv">
        <div class="import-section">
          <div class="upload-area">
            <el-upload
              ref="uploadRef"
              class="csv-upload"
              drag
              :auto-upload="false"
              :show-file-list="true"
              :on-change="handleFileChange"
              :before-upload="beforeUpload"
              accept=".csv"
              :limit="1"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                拖拽CSV文件到此处，或<em>点击选择文件</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持.csv格式文件，文件大小不超过100MB
                </div>
              </template>
            </el-upload>
          </div>

          <div v-if="selectedFile" class="file-info">
            <el-card>
              <h4>文件信息</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="文件名">{{ selectedFile.name }}</el-descriptions-item>
                <el-descriptions-item label="文件大小">{{ formatFileSize(selectedFile.size) }}</el-descriptions-item>
                <el-descriptions-item label="上传时间">{{ new Date().toLocaleString() }}</el-descriptions-item>
                <el-descriptions-item label="文件类型">{{ selectedFile.type || 'CSV' }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </div>

          <div class="import-config">
            <el-form :model="importForm" label-width="120px">
              <el-form-item label="股票代码" required>
                <el-input
                  v-model="importForm.symbol"
                  placeholder="请输入股票代码，如：000001"
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="股票名称">
                <el-input
                  v-model="importForm.name"
                  placeholder="请输入股票名称，如：平安银行"
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="更新模式">
                <el-radio-group v-model="importForm.updateMode">
                  <el-radio label="replace">替换现有数据</el-radio>
                  <el-radio label="append">追加到现有数据</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="数据验证">
                <el-checkbox v-model="importForm.validateData">启用数据验证</el-checkbox>
                <el-checkbox v-model="importForm.skipErrors">跳过错误行</el-checkbox>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据预览 -->
          <div v-if="previewData.length > 0" class="data-preview">
            <el-card>
              <template #header>
                <div class="preview-header">
                  <span>数据预览 (前5行)</span>
                  <el-tag type="info">共 {{ previewData.length }} 行数据</el-tag>
                </div>
              </template>
              
              <el-table :data="previewData.slice(0, 5)" size="small" max-height="300">
                <el-table-column
                  v-for="(value, key) in previewData[0]"
                  :key="key"
                  :prop="key"
                  :label="key"
                  show-overflow-tooltip
                />
              </el-table>
            </el-card>
          </div>

          <div class="import-actions">
            <el-button @click="previewCSV" :disabled="!selectedFile" icon="View">
              预览数据
            </el-button>
            <el-button
              type="primary"
              @click="startImport"
              :loading="importing"
              :disabled="!selectedFile || !importForm.symbol"
              icon="Upload"
            >
              开始导入
            </el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- API数据拉取 -->
      <el-tab-pane label="API数据拉取" name="api">
        <div class="api-section">
          <el-alert
            title="API数据拉取"
            description="从第三方数据源拉取最新的历史数据"
            type="info"
            show-icon
            :closable="false"
            style="margin-bottom: 20px"
          />

          <el-form :model="apiForm" label-width="120px">
            <el-form-item label="数据源">
              <el-select v-model="apiForm.source" placeholder="选择数据源">
                <el-option label="Tushare" value="tushare" />
                <el-option label="新浪财经" value="sina" />
                <el-option label="腾讯财经" value="tencent" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="股票代码">
              <el-input
                v-model="apiForm.symbol"
                placeholder="请输入股票代码"
                style="width: 200px"
              />
            </el-form-item>
            
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="apiForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-form>

          <div class="api-actions">
            <el-button
              type="primary"
              @click="fetchApiData"
              :loading="fetchingApi"
              icon="Download"
            >
              拉取数据
            </el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- 批量导入 -->
      <el-tab-pane label="批量导入" name="batch">
        <div class="batch-section">
          <el-alert
            title="批量导入功能"
            description="支持同时导入多个股票的CSV文件"
            type="warning"
            show-icon
            :closable="false"
            style="margin-bottom: 20px"
          />

          <el-upload
            class="batch-upload"
            drag
            multiple
            :auto-upload="false"
            :show-file-list="true"
            :on-change="handleBatchFileChange"
            accept=".csv"
            :limit="10"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              拖拽多个CSV文件到此处进行批量导入
            </div>
            <template #tip>
              <div class="el-upload__tip">
                最多支持10个文件，每个文件不超过100MB
              </div>
            </template>
          </el-upload>

          <div v-if="batchFiles.length > 0" class="batch-file-list">
            <el-card>
              <template #header>
                <span>待导入文件列表 ({{ batchFiles.length }}个)</span>
              </template>
              
              <el-table :data="batchFiles" size="small">
                <el-table-column prop="name" label="文件名" />
                <el-table-column prop="size" label="大小">
                  <template #default="{ row }">
                    {{ formatFileSize(row.size) }}
                  </template>
                </el-table-column>
                <el-table-column label="状态">
                  <template #default="{ row }">
                    <el-tag
                      :type="getFileStatusType(row.status)"
                      size="small"
                    >
                      {{ getFileStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="{ row, $index }">
                    <el-button
                      size="small"
                      type="danger"
                      @click="removeBatchFile($index)"
                      icon="Delete"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>

          <div class="batch-actions">
            <el-button
              type="primary"
              @click="startBatchImport"
              :loading="batchImporting"
              :disabled="batchFiles.length === 0"
              icon="Upload"
            >
              开始批量导入
            </el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 导入进度 -->
    <div v-if="importProgress.visible" class="import-progress">
      <el-card>
        <template #header>
          <div class="progress-header">
            <span>导入进度</span>
            <el-button v-if="!importing" @click="importProgress.visible = false" icon="Close" size="small" />
          </div>
        </template>
        
        <div class="progress-content">
          <el-progress
            :percentage="importProgress.percentage"
            :status="importProgress.status"
          />
          
          <div class="progress-details">
            <p>{{ importProgress.message }}</p>
            <el-descriptions :column="3" size="small">
              <el-descriptions-item label="总记录数">{{ importProgress.total }}</el-descriptions-item>
              <el-descriptions-item label="成功导入">{{ importProgress.success }}</el-descriptions-item>
              <el-descriptions-item label="失败记录">{{ importProgress.errors }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-if="importProgress.logs.length > 0" class="progress-logs">
            <h5>导入日志</h5>
            <el-scrollbar max-height="200px">
              <div v-for="(log, index) in importProgress.logs" :key="index" class="log-item">
                <el-tag :type="log.type" size="small">{{ log.timestamp }}</el-tag>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { httpClient } from '@/api/http'
import Papa from 'papaparse'

// 事件定义
const emit = defineEmits(['import-success', 'close'])

// 响应式数据
const activeTab = ref('csv')
const importing = ref(false)
const fetchingApi = ref(false)
const batchImporting = ref(false)

const selectedFile = ref<File | null>(null)
const previewData = ref<any[]>([])
const batchFiles = ref<any[]>([])

const importForm = reactive({
  symbol: '',
  name: '',
  updateMode: 'replace',
  validateData: true,
  skipErrors: true
})

const apiForm = reactive({
  source: 'tushare',
  symbol: '',
  dateRange: [] as string[]
})

const importProgress = reactive({
  visible: false,
  percentage: 0,
  status: 'normal' as 'normal' | 'success' | 'warning' | 'exception',
  message: '',
  total: 0,
  success: 0,
  errors: 0,
  logs: [] as Array<{type: string, timestamp: string, message: string}>
})

// 文件处理
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
  previewData.value = []
}

const handleBatchFileChange = (file: any, fileList: any[]) => {
  batchFiles.value = fileList.map(f => ({
    ...f.raw,
    status: 'pending'
  }))
}

const beforeUpload = (file: File) => {
  const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv')
  const isLt100M = file.size / 1024 / 1024 < 100

  if (!isCSV) {
    ElMessage.error('只能上传CSV文件!')
    return false
  }
  if (!isLt100M) {
    ElMessage.error('文件大小不能超过100MB!')
    return false
  }
  return true
}

// 数据预览
const previewCSV = () => {
  if (!selectedFile.value) return

  const reader = new FileReader()
  reader.onload = (e) => {
    const csv = e.target?.result as string
    Papa.parse(csv, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        previewData.value = results.data
        ElMessage.success(`成功解析CSV文件，共 ${results.data.length} 行数据`)
      },
      error: (error) => {
        ElMessage.error(`解析CSV文件失败: ${error.message}`)
      }
    })
  }
  reader.readAsText(selectedFile.value)
}

// 单文件导入
const startImport = async () => {
  if (!selectedFile.value || !importForm.symbol) {
    ElMessage.warning('请选择文件并填写股票代码')
    return
  }

  try {
    importing.value = true
    showImportProgress()

    const formData = new FormData()
    formData.append('file', selectedFile.value)

    const response = await httpClient.post('/api/v1/enhanced-historical/historical/import-csv', formData, {
      params: {
        symbol: importForm.symbol,
        name: importForm.name,
        update_mode: importForm.updateMode
      },
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          updateProgress(percentage, `上传中... ${percentage}%`)
        }
      }
    })

    if (response.data && response.data.success) {
      updateProgress(100, '导入完成!', 'success')
      addLog('success', '数据导入成功')
      
      setTimeout(() => {
        emit('import-success')
        resetImportState()
      }, 2000)
      
      ElMessage.success('数据导入成功')
    } else {
      throw new Error(response.data?.message || '导入失败')
    }
  } catch (error: any) {
    console.error('导入失败:', error)
    updateProgress(0, '导入失败', 'exception')
    addLog('error', `导入失败: ${error.message}`)
    ElMessage.error(`导入失败: ${error.message}`)
  } finally {
    importing.value = false
  }
}

// API数据拉取
const fetchApiData = async () => {
  if (!apiForm.symbol) {
    ElMessage.warning('请填写股票代码')
    return
  }

  try {
    fetchingApi.value = true
    ElMessage.info('API数据拉取功能开发中...')
  } catch (error: any) {
    ElMessage.error(`拉取失败: ${error.message}`)
  } finally {
    fetchingApi.value = false
  }
}

// 批量导入
const startBatchImport = async () => {
  if (batchFiles.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    batchImporting.value = true
    showImportProgress()

    for (let i = 0; i < batchFiles.value.length; i++) {
      const file = batchFiles.value[i]
      const percentage = Math.round(((i + 1) / batchFiles.value.length) * 100)
      
      updateProgress(percentage, `正在处理 ${file.name}...`)
      addLog('info', `开始处理文件: ${file.name}`)

      // 这里应该实现批量导入逻辑
      // 暂时模拟处理过程
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      file.status = 'completed'
      addLog('success', `文件 ${file.name} 处理完成`)
    }

    updateProgress(100, '批量导入完成!', 'success')
    ElMessage.success('批量导入完成')
    
    setTimeout(() => {
      emit('import-success')
      resetImportState()
    }, 2000)

  } catch (error: any) {
    updateProgress(0, '批量导入失败', 'exception')
    addLog('error', `批量导入失败: ${error.message}`)
    ElMessage.error(`批量导入失败: ${error.message}`)
  } finally {
    batchImporting.value = false
  }
}

// 进度管理
const showImportProgress = () => {
  importProgress.visible = true
  importProgress.percentage = 0
  importProgress.status = 'normal'
  importProgress.message = '准备导入...'
  importProgress.logs = []
}

const updateProgress = (percentage: number, message: string, status: any = 'normal') => {
  importProgress.percentage = percentage
  importProgress.message = message
  importProgress.status = status
}

const addLog = (type: string, message: string) => {
  importProgress.logs.push({
    type,
    timestamp: new Date().toLocaleTimeString(),
    message
  })
}

const resetImportState = () => {
  selectedFile.value = null
  previewData.value = []
  batchFiles.value = []
  importForm.symbol = ''
  importForm.name = ''
  importProgress.visible = false
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'error': return 'danger'
    case 'processing': return 'warning'
    default: return 'info'
  }
}

const getFileStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'error': return '失败'
    case 'processing': return '处理中'
    default: return '待处理'
  }
}

const removeBatchFile = (index: number) => {
  batchFiles.value.splice(index, 1)
}
</script>

<style scoped>
.data-importer {
  padding: 20px;
}

.import-section,
.api-section,
.batch-section {
  min-height: 400px;
}

.upload-area {
  margin-bottom: 20px;
}

.csv-upload,
.batch-upload {
  width: 100%;
}

.file-info {
  margin-bottom: 20px;
}

.import-config {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.data-preview {
  margin-bottom: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.import-actions,
.api-actions,
.batch-actions {
  text-align: center;
  margin-top: 20px;
}

.batch-file-list {
  margin: 20px 0;
}

.import-progress {
  margin-top: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-content {
  padding: 20px 0;
}

.progress-details {
  margin: 20px 0;
}

.progress-logs {
  margin-top: 20px;
}

.progress-logs h5 {
  margin-bottom: 10px;
  color: #606266;
}

.log-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 5px 0;
}

.log-message {
  margin-left: 10px;
  font-size: 12px;
  color: #606266;
}
</style>