# CI 测试环境 Docker Compose 配置
# 专门用于 CI/CD 流程中的集成测试和 E2E 测试
version: '3.8'

services:
  # PostgreSQL 测试数据库
  postgres:
    image: postgres:15-alpine
    container_name: ci-postgres
    restart: "no"
    environment:
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_DB: test_db
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ../../../scripts/database/init_test.sql:/docker-entrypoint-initdb.d/init_test.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_db"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 10s
    networks:
      - ci-network
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=100
      -c shared_buffers=128MB
      -c effective_cache_size=512MB
      -c work_mem=2MB
      -c maintenance_work_mem=32MB
      -c fsync=off
      -c synchronous_commit=off
      -c full_page_writes=off

  # Redis 测试缓存
  redis:
    image: redis:7-alpine
    container_name: ci-redis
    restart: "no"
    ports:
      - "6379:6379"
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 2s
      timeout: 3s
      retries: 10
      start_period: 5s
    networks:
      - ci-network
    command: >
      redis-server
      --appendonly no
      --save ""
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru

  # 后端测试服务
  backend:
    build:
      context: ../../..
      dockerfile: docker/backend/Dockerfile.ci
      args:
        PYTHON_VERSION: "3.11"
    container_name: ci-backend
    restart: "no"
    ports:
      - "8000:8000"
    environment:
      # 数据库配置
      DATABASE_URL: postgresql+asyncpg://test_user:test_password@postgres:5432/test_db
      DATABASE_TEST_URL: postgresql+asyncpg://test_user:test_password@postgres:5432/test_db
      
      # Redis 配置
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/1
      CELERY_RESULT_BACKEND: redis://redis:6379/2
      
      # 应用配置
      ENVIRONMENT: test
      DEBUG: false
      SECRET_KEY: test-secret-key-for-ci-only
      JWT_SECRET_KEY: test-jwt-secret-key-for-ci-only
      
      # 测试配置
      TESTING: true
      PYTEST_CURRENT_TEST: true
      
      # 日志配置
      LOG_LEVEL: INFO
      LOG_FORMAT: json
      
      # 外部服务配置（测试环境）
      TUSHARE_TOKEN: test-token
      AKSHARE_ENABLED: false
      
      # 性能配置
      WORKERS: 1
      MAX_CONNECTIONS: 10
      
      # 安全配置
      CORS_ORIGINS: http://localhost:5173,http://frontend:5173
      ALLOWED_HOSTS: localhost,backend,127.0.0.1
    volumes:
      - ../../../backend/app:/app/app:ro
      - ../../../backend/tests:/app/tests:ro
      - ci_backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ci-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 6
      start_period: 30s
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python -c 'import asyncio; from app.core.database import check_db_connection; asyncio.run(check_db_connection())' &&
        echo 'Running database migrations...' &&
        alembic upgrade head &&
        echo 'Starting application...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1
      "

  # 前端测试服务
  frontend:
    build:
      context: ../../..
      dockerfile: docker/frontend/Dockerfile.ci
      args:
        NODE_VERSION: "18"
        VITE_API_BASE_URL: http://backend:8000/api/v1
        VITE_WS_URL: ws://backend:8000/ws
        VITE_APP_TITLE: 量化投资平台 (CI测试)
        VITE_APP_VERSION: ci-test
    container_name: ci-frontend
    restart: "no"
    ports:
      - "5173:5173"
    environment:
      NODE_ENV: test
      VITE_API_BASE_URL: http://localhost:8000/api/v1
      VITE_WS_URL: ws://localhost:8000/ws
      VITE_APP_TITLE: 量化投资平台 (CI测试)
    volumes:
      - ../../../frontend/src:/app/src:ro
      - ../../../frontend/public:/app/public:ro
      - ci_frontend_logs:/app/logs
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - ci-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5173/"]
      interval: 10s
      timeout: 5s
      retries: 6
      start_period: 20s

  # Nginx 代理（用于 E2E 测试）
  nginx:
    image: nginx:1.25-alpine
    container_name: ci-nginx
    restart: "no"
    ports:
      - "80:80"
    volumes:
      - ../../nginx/ci/default.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      frontend:
        condition: service_healthy
      backend:
        condition: service_healthy
    networks:
      - ci-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # 测试运行器（可选，用于运行特定测试）
  test-runner:
    build:
      context: ../../..
      dockerfile: docker/test/Dockerfile.test-runner
    container_name: ci-test-runner
    restart: "no"
    environment:
      DATABASE_URL: postgresql+asyncpg://test_user:test_password@postgres:5432/test_db
      REDIS_URL: redis://redis:6379/0
      BACKEND_URL: http://backend:8000
      FRONTEND_URL: http://frontend:5173
      NGINX_URL: http://nginx:80
    volumes:
      - ../../../backend/tests:/app/backend/tests:ro
      - ../../../frontend/tests:/app/frontend/tests:ro
      - ../../../scripts/testing:/app/scripts:ro
      - ci_test_results:/app/results
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy
      frontend:
        condition: service_healthy
    networks:
      - ci-network
    profiles:
      - testing
    command: ["sleep", "infinity"]  # 保持容器运行，等待测试命令

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  ci_backend_logs:
    driver: local
  ci_frontend_logs:
    driver: local
  ci_test_results:
    driver: local

networks:
  ci-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
