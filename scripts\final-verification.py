#!/usr/bin/env python3
"""
最终验证脚本
验证立即行动计划的完成情况
"""

import os
import sys
import json
import sqlite3
from pathlib import Path

def check_cleanup_status():
    """检查清理状态"""
    print("🧹 检查文件清理状态...")
    
    project_root = Path(__file__).parent.parent
    
    # 检查已删除的重复文件
    removed_files = [
        "backend/backup/duplicates/enhanced_historical_service.py",
        "frontend/src/api/market_original.ts.bak",
        "frontend/src/main.backup.ts",
        "config/environment/development.env.template"
    ]
    
    cleanup_success = True
    for file_path in removed_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"❌ 文件未删除: {file_path}")
            cleanup_success = False
        else:
            print(f"✅ 文件已删除: {file_path}")
    
    return cleanup_success

def check_config_integration():
    """检查配置整合状态"""
    print("\n⚙️ 检查配置整合状态...")
    
    project_root = Path(__file__).parent.parent
    
    # 检查新配置文件
    config_files = [
        ".env.template",
        ".env.development", 
        ".env.production",
        "scripts/setup-env.py"
    ]
    
    config_success = True
    for file_path in config_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ 配置文件存在: {file_path}")
        else:
            print(f"❌ 配置文件缺失: {file_path}")
            config_success = False
    
    return config_success

def check_data_initialization():
    """检查数据初始化状态"""
    print("\n📊 检查数据初始化状态...")
    
    project_root = Path(__file__).parent.parent
    
    # 检查数据目录
    data_dir = project_root / "data"
    if not data_dir.exists():
        print("❌ 数据目录不存在")
        return False
    
    # 检查股票数据文件
    stocks_dir = data_dir / "historical" / "stocks"
    if not stocks_dir.exists():
        print("❌ 股票数据目录不存在")
        return False
    
    csv_files = list(stocks_dir.glob("*.csv"))
    print(f"✅ 股票数据文件: {len(csv_files)} 个")
    
    # 检查索引数据库
    index_file = data_dir / "index" / "stock_index.db"
    if not index_file.exists():
        print("❌ 索引数据库不存在")
        return False
    
    # 检查索引数据库内容
    try:
        conn = sqlite3.connect(str(index_file))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM stock_index")
        count = cursor.fetchone()[0]
        conn.close()
        print(f"✅ 索引数据库记录: {count} 条")
        
        if count > 0:
            return True
        else:
            print("❌ 索引数据库为空")
            return False
    except Exception as e:
        print(f"❌ 索引数据库检查失败: {e}")
        return False

def check_scripts():
    """检查脚本文件"""
    print("\n🔧 检查脚本文件...")
    
    project_root = Path(__file__).parent.parent
    
    scripts = [
        "scripts/init-data.py",
        "scripts/setup-env.py",
        "scripts/final-verification.py"
    ]
    
    scripts_success = True
    for script_path in scripts:
        full_path = project_root / script_path
        if full_path.exists():
            print(f"✅ 脚本存在: {script_path}")
        else:
            print(f"❌ 脚本缺失: {script_path}")
            scripts_success = False
    
    return scripts_success

def generate_summary():
    """生成总结报告"""
    print("\n📋 立即行动计划完成总结")
    print("=" * 50)
    
    # 执行所有检查
    cleanup_ok = check_cleanup_status()
    config_ok = check_config_integration()
    data_ok = check_data_initialization()
    scripts_ok = check_scripts()
    
    # 总结
    print("\n🎯 完成情况:")
    print(f"   ✅ 清理重复文件: {'完成' if cleanup_ok else '未完成'}")
    print(f"   ✅ 整合配置文件: {'完成' if config_ok else '未完成'}")
    print(f"   ✅ 完善数据初始化: {'完成' if data_ok else '未完成'}")
    print(f"   ✅ 创建管理脚本: {'完成' if scripts_ok else '未完成'}")
    
    overall_success = cleanup_ok and config_ok and data_ok and scripts_ok
    
    if overall_success:
        print("\n🎉 立即行动计划全部完成！")
        print("\n📝 下一步建议:")
        print("   1. 重启后端服务以应用所有更改")
        print("   2. 运行: python scripts/setup-env.py development")
        print("   3. 测试所有API端点功能")
        print("   4. 开始处理P1级别优化任务")
    else:
        print("\n⚠️ 部分任务未完成，请检查上述问题")
    
    return overall_success

def main():
    """主函数"""
    print("🚀 开始最终验证...")
    
    try:
        success = generate_summary()
        
        if success:
            print("\n✅ 验证通过！系统已准备就绪。")
            return 0
        else:
            print("\n❌ 验证失败，请修复问题后重试。")
            return 1
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
