"""
增强历史数据管理服务 - P0修复版
添加完善的错误处理、内存限制和超时机制
"""

import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import asyncio
import pickle
from concurrent.futures import ThreadPoolExecutor
import sqlite3
import json

from app.core.error_handler import ErrorHandler, MemoryLimiter, TimeoutManager, safe_operation

logger = logging.getLogger(__name__)


class EnhancedHistoricalDataManager:
    """增强历史数据管理器 - 包含P0修复"""
    
    def __init__(self):
        # 数据目录
        self.data_dir = Path(__file__).parent.parent.parent / "data" / "historical" / "stocks"
        self.index_dir = Path(__file__).parent.parent.parent / "data" / "index"
        self.index_dir.mkdir(exist_ok=True)
        
        # 索引文件
        self.stock_index_file = self.index_dir / "stock_index.db"
        self.metadata_file = self.index_dir / "metadata.json"
        
        # 内存限制器
        self.memory_limiter = MemoryLimiter(max_rows=50000)  # 限制5万行数据
        
        # 缓存
        self._data_cache = {}
        self._index_cache = {}
        self._cache_size_limit = 50  # 减少缓存大小
        
        # 线程池用于并发读取文件
        self._executor = ThreadPoolExecutor(max_workers=2)  # 减少线程数
        
        # 初始化标志
        self._initialized = False

    @safe_operation("initialization")
    async def ensure_initialized(self):
        """确保管理器已初始化"""
        if not self._initialized:
            try:
                await self._build_index()
                self._initialized = True
                logger.info("历史数据管理器初始化完成")
            except Exception as e:
                logger.error(f"初始化失败: {e}")
                # 即使初始化失败也设置标志，避免重复尝试
                self._initialized = True
                
    @safe_operation("file_read")
    async def _build_index(self):
        """构建股票数据索引"""
        try:
            if not self.data_dir.exists():
                logger.warning(f"数据目录不存在: {self.data_dir}")
                # 创建空索引
                await self._create_empty_index()
                return
                
            # 使用线程池扫描文件
            loop = asyncio.get_event_loop()
            index_data = await loop.run_in_executor(
                self._executor, 
                self._scan_data_files
            )
            
            # 保存索引到数据库
            await self._save_index_to_db(index_data)
            
        except Exception as e:
            logger.error(f"构建索引失败: {e}")
            await self._create_empty_index()

    def _scan_data_files(self) -> Dict[str, Any]:
        """扫描数据文件 - 同步方法在线程池中执行"""
        index_data = {}
        file_count = 0
        max_files = 1000  # 限制扫描文件数量
        
        try:
            for file_path in self.data_dir.rglob("*.csv"):
                if file_count >= max_files:
                    logger.warning(f"达到文件扫描限制: {max_files}")
                    break
                    
                try:
                    # 从文件名提取股票代码
                    symbol = file_path.stem
                    
                    # 快速检查文件
                    stat = file_path.stat()
                    if stat.st_size > 100 * 1024 * 1024:  # 100MB限制
                        logger.warning(f"文件过大，跳过: {file_path}")
                        continue
                    
                    # 读取文件头部信息
                    try:
                        df_sample = pd.read_csv(file_path, nrows=1)
                        if not df_sample.empty:
                            index_data[symbol] = {
                                'file_path': str(file_path),
                                'file_size': stat.st_size,
                                'last_modified': stat.st_mtime,
                                'columns': list(df_sample.columns),
                                'sample_data': df_sample.iloc[0].to_dict()
                            }
                            file_count += 1
                    except Exception as e:
                        logger.warning(f"读取文件样本失败 {file_path}: {e}")
                        continue
                        
                except Exception as e:
                    logger.warning(f"处理文件失败 {file_path}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"扫描数据文件失败: {e}")
            
        logger.info(f"扫描完成，找到 {len(index_data)} 个股票数据文件")
        return index_data

    async def _create_empty_index(self):
        """创建空索引"""
        try:
            # 创建空的SQLite数据库
            await self._init_db()
            
            # 创建空的元数据
            metadata = {
                'last_updated': datetime.now().isoformat(),
                'total_stocks': 0,
                'data_sources': [],
                'status': 'empty'
            }
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
                
            logger.info("创建空索引完成")
            
        except Exception as e:
            logger.error(f"创建空索引失败: {e}")

    async def _init_db(self):
        """初始化数据库"""
        try:
            def create_tables():
                conn = sqlite3.connect(str(self.stock_index_file))
                cursor = conn.cursor()
                
                # 创建股票索引表
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_index (
                    symbol TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    last_modified REAL,
                    columns TEXT,
                    status TEXT DEFAULT 'active'
                )
                ''')
                
                conn.commit()
                conn.close()
                
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, create_tables)
            
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")

    async def _save_index_to_db(self, index_data: Dict[str, Any]):
        """保存索引到数据库"""
        try:
            def save_data():
                conn = sqlite3.connect(str(self.stock_index_file))
                cursor = conn.cursor()
                
                # 清空现有数据
                cursor.execute('DELETE FROM stock_index')
                
                # 插入新数据
                for symbol, info in index_data.items():
                    cursor.execute('''
                    INSERT INTO stock_index 
                    (symbol, file_path, file_size, last_modified, columns)
                    VALUES (?, ?, ?, ?, ?)
                    ''', (
                        symbol,
                        info['file_path'],
                        info['file_size'],
                        info['last_modified'],
                        json.dumps(info['columns'])
                    ))
                
                conn.commit()
                conn.close()
                
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self._executor, save_data)
            
            # 更新元数据
            metadata = {
                'last_updated': datetime.now().isoformat(),
                'total_stocks': len(index_data),
                'data_sources': ['local_csv'],
                'status': 'ready'
            }
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
                
            logger.info(f"索引保存完成，共 {len(index_data)} 只股票")
            
        except Exception as e:
            logger.error(f"保存索引失败: {e}")

    @safe_operation("data_query")
    async def get_stock_data(
        self, 
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        columns: Optional[List[str]] = None
    ) -> Optional[pd.DataFrame]:
        """获取股票数据 - 带安全限制"""
        try:
            await self.ensure_initialized()
            
            # 从索引获取文件路径
            file_path = await self._get_file_path(symbol)
            if not file_path:
                logger.warning(f"未找到股票 {symbol} 的数据文件")
                return None
            
            # 在线程池中读取数据
            loop = asyncio.get_event_loop()
            df = await loop.run_in_executor(
                self._executor,
                self._read_csv_file,
                file_path, start_date, end_date, columns
            )
            
            if df is None or df.empty:
                return None
            
            # 内存安全检查
            self.memory_limiter.check_data_size(len(df), f"股票{symbol}数据查询")
            
            # 安全切片
            df = self.memory_limiter.safe_slice(df)
            
            return df
            
        except MemoryError as e:
            logger.error(f"内存不足: {e}")
            return None
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return None

    def _read_csv_file(
        self, 
        file_path: str, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        columns: Optional[List[str]] = None
    ) -> Optional[pd.DataFrame]:
        """读取CSV文件 - 同步方法"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:  # 50MB限制
                logger.warning(f"文件过大: {file_path} ({file_size/1024/1024:.1f}MB)")
                return None
            
            # 读取数据
            read_kwargs = {}
            if columns:
                read_kwargs['usecols'] = columns
                
            df = pd.read_csv(file_path, **read_kwargs)
            
            # 日期筛选
            if start_date or end_date:
                df = self._filter_by_date(df, start_date, end_date)
            
            return df
            
        except Exception as e:
            logger.error(f"读取CSV文件失败 {file_path}: {e}")
            return None

    def _filter_by_date(
        self, 
        df: pd.DataFrame, 
        start_date: Optional[datetime],
        end_date: Optional[datetime]
    ) -> pd.DataFrame:
        """按日期筛选数据"""
        try:
            # 寻找日期列
            date_columns = ['日期', 'date', 'Date', 'timestamp', '交易日期']
            date_col = None
            
            for col in date_columns:
                if col in df.columns:
                    date_col = col
                    break
            
            if not date_col:
                logger.warning("未找到日期列，跳过日期筛选")
                return df
            
            # 转换日期格式
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            
            # 筛选
            if start_date:
                df = df[df[date_col] >= start_date]
            if end_date:
                df = df[df[date_col] <= end_date]
                
            return df
            
        except Exception as e:
            logger.error(f"日期筛选失败: {e}")
            return df

    async def _get_file_path(self, symbol: str) -> Optional[str]:
        """从索引获取文件路径"""
        try:
            def query_db():
                conn = sqlite3.connect(str(self.stock_index_file))
                cursor = conn.cursor()
                
                cursor.execute(
                    'SELECT file_path FROM stock_index WHERE symbol = ?',
                    (symbol,)
                )
                result = cursor.fetchone()
                conn.close()
                
                return result[0] if result else None
                
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._executor, query_db)
            
        except Exception as e:
            logger.error(f"查询文件路径失败 {symbol}: {e}")
            return None

    async def get_stock_list(
        self,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        page: int = 1,
        page_size: int = 50
    ) -> List[Dict[str, Any]]:
        """获取股票列表 - 带分页和安全限制"""
        try:
            await self.ensure_initialized()
            
            # 限制页面大小
            page_size = min(page_size, 200)
            
            def query_stocks():
                conn = sqlite3.connect(str(self.stock_index_file))
                cursor = conn.cursor()
                
                # 基础查询
                query = 'SELECT symbol, file_path, file_size FROM stock_index WHERE status = "active"'
                params = []
                
                # 市场筛选（简化实现）
                if market:
                    if market.upper() == 'SH':
                        query += ' AND symbol LIKE "6%"'
                    elif market.upper() == 'SZ':
                        query += ' AND symbol LIKE "0%"'
                
                # 分页
                offset = (page - 1) * page_size
                query += f' LIMIT {page_size} OFFSET {offset}'
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                conn.close()
                
                return results
            
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(self._executor, query_stocks)
            
            # 构建返回数据
            stock_list = []
            for symbol, file_path, file_size in results:
                stock_list.append({
                    'symbol': symbol,
                    'name': f'股票{symbol}',  # 简化实现
                    'market': 'SH' if symbol.startswith('6') else 'SZ',
                    'file_size': file_size,
                    'status': 'active'
                })
            
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票 - 带安全限制"""
        try:
            await self.ensure_initialized()
            
            # 限制返回数量
            limit = min(limit, 50)
            
            def search_db():
                conn = sqlite3.connect(str(self.stock_index_file))
                cursor = conn.cursor()
                
                cursor.execute('''
                SELECT symbol, file_path FROM stock_index 
                WHERE symbol LIKE ? OR symbol LIKE ?
                LIMIT ?
                ''', (f'%{keyword}%', f'{keyword}%', limit))
                
                results = cursor.fetchall()
                conn.close()
                return results
            
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(self._executor, search_db)
            
            # 构建返回数据
            search_results = []
            for symbol, file_path in results:
                search_results.append({
                    'symbol': symbol,
                    'name': f'股票{symbol}',
                    'market': 'SH' if symbol.startswith('6') else 'SZ'
                })
                
            return search_results
            
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return []

    async def get_market_statistics(self) -> Dict[str, Any]:
        """获取市场统计信息"""
        try:
            await self.ensure_initialized()
            
            # 读取元数据
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            else:
                metadata = {'total_stocks': 0, 'status': 'empty'}
            
            return {
                'total_stocks': metadata.get('total_stocks', 0),
                'markets': {'SH': 0, 'SZ': 0},  # 简化实现
                'industries': {},  # 简化实现
                'data_range': {
                    'start_date': '2020-01-01',
                    'end_date': datetime.now().strftime('%Y-%m-%d')
                },
                'last_updated': metadata.get('last_updated'),
                'status': metadata.get('status', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'total_stocks': 0,
                'markets': {},
                'industries': {},
                'data_range': {},
                'status': 'error'
            }

    async def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, '_executor'):
                self._executor.shutdown(wait=True)
            self._data_cache.clear()
            self._index_cache.clear()
            logger.info("历史数据管理器清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


# 创建全局实例
enhanced_historical_manager = EnhancedHistoricalDataManager()