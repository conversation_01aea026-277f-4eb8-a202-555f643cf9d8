/**
 * 市场数据WebSocket服务
 * 专门处理实时行情数据推送
 */

import { ElMessage } from 'element-plus'
import mitt from 'mitt'
import type { Emitter } from 'mitt'
import { websocketConfig } from '@/config/websocket'
import type { QuoteData } from '@/types/market'

export interface MarketWebSocketEvents {
  connected: void
  disconnected: void
  error: Event
  quote: QuoteData
  quotes: QuoteData[]
  subscribed: string
  unsubscribed: string
}

export interface MarketMessage {
  type: 'quote' | 'quotes' | 'subscribed' | 'unsubscribed' | 'error' | 'pong' | 'welcome' | 'ping'
  data?: any
  symbol?: string
  symbols?: string[]
  message?: string
  timestamp?: string
  client_id?: string
}

export class MarketWebSocketService {
  private ws: WebSocket | null = null
  private eventEmitter: Emitter<MarketWebSocketEvents>
  private subscriptions = new Set<string>()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 10  // 增加最大重连次数
  private baseReconnectDelay = 1000  // 基础重连延迟
  private maxReconnectDelay = 30000  // 最大重连延迟
  private heartbeatInterval: NodeJS.Timeout | null = null
  private isConnecting = false
  private clientId: string
  private reconnectTimer: NodeJS.Timeout | null = null
  private manualDisconnect = false  // 标记是否为手动断开
  private connectionStartTime = 0
  private lastSuccessfulConnection = 0

  constructor() {
    this.eventEmitter = mitt<MarketWebSocketEvents>()
    this.clientId = `market-client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true
    this.manualDisconnect = false
    this.connectionStartTime = Date.now()

    try {
      const wsUrl = `${websocketConfig.market}?client_id=${this.clientId}`
      console.log(`Connecting to market WebSocket (attempt ${this.reconnectAttempts + 1}):`, wsUrl)

      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('Market WebSocket connected successfully')
        this.isConnecting = false
        this.reconnectAttempts = 0
        this.lastSuccessfulConnection = Date.now()
        this.clearReconnectTimer()

        this.eventEmitter.emit('connected')
        this.startHeartbeat()

        // 重新订阅之前的股票
        this.resubscribeAll()

        // 显示连接成功消息
        if (this.lastSuccessfulConnection > 0) {
          ElMessage.success('市场数据连接已恢复')
        }
      }

      this.ws.onmessage = (event) => {
        try {
          const message: MarketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.ws.onclose = (event) => {
        const connectionDuration = Date.now() - this.connectionStartTime
        console.log(`Market WebSocket disconnected: code=${event.code}, reason=${event.reason}, duration=${connectionDuration}ms`)

        this.isConnecting = false
        this.stopHeartbeat()
        this.eventEmitter.emit('disconnected')

        // 只有在非手动断开时才自动重连
        if (!this.manualDisconnect) {
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          } else {
            ElMessage.error({
              message: '市场数据连接失败，请检查网络后手动重连',
              duration: 0,
              showClose: true
            })
          }
        }
      }

      this.ws.onerror = (error) => {
        console.error('Market WebSocket error:', error)
        this.isConnecting = false
        this.eventEmitter.emit('error', error)
      }

    } catch (error) {
      this.isConnecting = false
      console.error('Failed to create WebSocket connection:', error)
      throw error
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.manualDisconnect = true
    this.clearReconnectTimer()
    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    this.subscriptions.clear()
    this.reconnectAttempts = this.maxReconnectAttempts // 阻止自动重连
  }

  /**
   * 手动重连
   */
  async reconnect(): Promise<void> {
    console.log('Manual reconnect requested')
    this.disconnect()

    // 重置重连状态
    this.reconnectAttempts = 0
    this.manualDisconnect = false

    // 短暂延迟后重连
    await new Promise(resolve => setTimeout(resolve, 500))
    await this.connect()
  }

  /**
   * 获取连接状态信息
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected(),
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      subscriptions: Array.from(this.subscriptions),
      lastSuccessfulConnection: this.lastSuccessfulConnection,
      canReconnect: this.reconnectAttempts >= this.maxReconnectAttempts
    }
  }

  /**
   * 订阅股票行情
   */
  subscribe(symbol: string): void {
    if (!symbol) return

    this.subscriptions.add(symbol)

    if (this.isConnected()) {
      this.sendMessage({
        type: 'subscribe',
        symbols: [symbol]  // 使用symbols数组格式，兼容后端API
      })
    }
  }

  /**
   * 取消订阅股票行情
   */
  unsubscribe(symbol: string): void {
    if (!symbol) return

    this.subscriptions.delete(symbol)

    if (this.isConnected()) {
      this.sendMessage({
        type: 'unsubscribe',
        symbols: [symbol]  // 使用symbols数组格式，兼容后端API
      })
    }
  }

  /**
   * 批量订阅
   */
  subscribeMultiple(symbols: string[]): void {
    symbols.forEach(symbol => this.subscribe(symbol))
  }

  /**
   * 批量取消订阅
   */
  unsubscribeMultiple(symbols: string[]): void {
    symbols.forEach(symbol => this.unsubscribe(symbol))
  }

  /**
   * 获取当前订阅列表
   */
  getSubscriptions(): string[] {
    return Array.from(this.subscriptions)
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 事件监听
   */
  on<K extends keyof MarketWebSocketEvents>(
    event: K,
    handler: (data: MarketWebSocketEvents[K]) => void
  ): void {
    this.eventEmitter.on(event, handler)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof MarketWebSocketEvents>(
    event: K,
    handler: (data: MarketWebSocketEvents[K]) => void
  ): void {
    this.eventEmitter.off(event, handler)
  }

  /**
   * 发送消息
   */
  private sendMessage(message: any): void {
    if (this.isConnected() && this.ws) {
      try {
        this.ws.send(JSON.stringify(message))
      } catch (error) {
        console.error('Failed to send WebSocket message:', error)
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: MarketMessage): void {
    switch (message.type) {
      case 'quote':
        if (message.data) {
          this.eventEmitter.emit('quote', message.data)
        }
        break

      case 'quotes':
        if (message.data && Array.isArray(message.data)) {
          this.eventEmitter.emit('quotes', message.data)
        }
        break

      case 'subscribed':
        if (message.symbols && message.symbols.length > 0) {
          // 处理批量订阅响应
          message.symbols.forEach(symbol => {
            this.eventEmitter.emit('subscribed', symbol)
          })
        } else if (message.symbol) {
          this.eventEmitter.emit('subscribed', message.symbol)
        }
        break

      case 'unsubscribed':
        if (message.symbols && message.symbols.length > 0) {
          // 处理批量取消订阅响应
          message.symbols.forEach(symbol => {
            this.eventEmitter.emit('unsubscribed', symbol)
          })
        } else if (message.symbol) {
          this.eventEmitter.emit('unsubscribed', message.symbol)
        }
        break

      case 'error':
        console.error('Market WebSocket error:', message.message)
        ElMessage.error(`行情数据错误: ${message.message}`)
        break

      case 'pong':
        // 心跳响应，不需要处理
        break

      case 'welcome':
        console.log('WebSocket welcome:', message.message)
        break

      case 'ping':
        // 服务器发送的心跳，自动回应
        if (this.isConnected()) {
          this.sendMessage({ type: 'pong' })
        }
        break

      default:
        console.warn('Unknown market message type:', message.type)
    }
  }

  /**
   * 重新订阅所有股票
   */
  private resubscribeAll(): void {
    if (this.subscriptions.size > 0) {
      // 批量重新订阅所有股票
      this.sendMessage({
        type: 'subscribe',
        symbols: Array.from(this.subscriptions)
      })
    }
  }

  /**
   * 计划重连（指数退避+抖动）
   */
  private scheduleReconnect(): void {
    if (this.manualDisconnect) {
      return
    }

    this.reconnectAttempts++

    // 指数退避计算：1s, 2s, 4s, 8s, 16s, 30s (cap)
    const exponentialDelay = this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    const cappedDelay = Math.min(exponentialDelay, this.maxReconnectDelay)

    // 添加±10%的随机抖动，避免多个客户端同时重连
    const jitter = cappedDelay * 0.1 * (Math.random() * 2 - 1)  // -10% to +10%
    const finalDelay = Math.max(500, cappedDelay + jitter)  // 最小500ms

    console.log(
      `Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} ` +
      `in ${Math.round(finalDelay)}ms (base: ${cappedDelay}ms, jitter: ${Math.round(jitter)}ms)`
    )

    this.clearReconnectTimer()
    this.reconnectTimer = setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts && !this.manualDisconnect) {
        this.connect().catch(error => {
          console.error(`Reconnect attempt ${this.reconnectAttempts} failed:`, error)
        })
      }
    }, finalDelay)
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()

    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected()) {
        this.sendMessage({ type: 'ping' })
      }
    }, 30000) // 每30秒发送一次心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
}

// 创建全局实例
export const marketWebSocketService = new MarketWebSocketService()

// 自动连接（可选）
if (typeof window !== 'undefined') {
  // 延迟连接，避免在页面加载时立即连接
  setTimeout(() => {
    marketWebSocketService.connect().catch(error => {
      console.warn('Failed to auto-connect to market WebSocket:', error)
    })
  }, 1000)
}
