"""
数据源健康监控API端点
提供数据源状态、健康检查、指标监控等功能
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, Any, List, Optional
import logging
import time
from datetime import datetime

from ..services.enhanced_data_source_manager import (
    get_data_source_manager, 
    init_data_source_manager,
    DataSourceType,
    DataSourceStatus
)
from ..core.auth import get_current_user
from ..schemas.response import ApiResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/data-source", tags=["数据源健康监控"])


@router.get("/health", response_model=ApiResponse)
async def get_data_source_health():
    """
    获取数据源健康状态
    """
    try:
        manager = get_data_source_manager()
        health_status = await manager.health_check()
        
        return ApiResponse(
            success=True,
            data=health_status,
            message="数据源健康状态获取成功"
        )
    except Exception as e:
        logger.error(f"获取数据源健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")


@router.get("/metrics", response_model=ApiResponse)
async def get_data_source_metrics():
    """
    获取数据源指标
    用于Prometheus等监控系统
    """
    try:
        manager = get_data_source_manager()
        metrics = manager.get_health_metrics()
        
        return ApiResponse(
            success=True,
            data=metrics,
            message="数据源指标获取成功"
        )
    except Exception as e:
        logger.error(f"获取数据源指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.post("/probe", response_model=ApiResponse)
async def perform_data_source_probe(background_tasks: BackgroundTasks):
    """
    执行数据源探测
    """
    try:
        manager = get_data_source_manager()
        
        # 在后台执行探测任务
        async def probe_task():
            try:
                probe_results = await manager.perform_minimal_probe()
                logger.info(f"数据源探测完成: {probe_results}")
            except Exception as e:
                logger.error(f"数据源探测失败: {e}")
        
        background_tasks.add_task(probe_task)
        
        return ApiResponse(
            success=True,
            data={"message": "数据源探测已启动"},
            message="探测任务已在后台启动"
        )
    except Exception as e:
        logger.error(f"启动数据源探测失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动探测失败: {str(e)}")


@router.post("/circuit-breaker/reset/{source_type}", response_model=ApiResponse)
async def reset_circuit_breaker(
    source_type: str,
    current_user: dict = Depends(get_current_user)
):
    """
    重置指定数据源的熔断器
    需要管理员权限
    """
    try:
        # 验证数据源类型
        try:
            data_source_type = DataSourceType(source_type.lower())
        except ValueError:
            raise HTTPException(
                status_code=400, 
                detail=f"无效的数据源类型: {source_type}"
            )
        
        manager = get_data_source_manager()
        await manager.force_reset_circuit_breaker(data_source_type)
        
        return ApiResponse(
            success=True,
            data={"source_type": source_type, "action": "circuit_breaker_reset"},
            message=f"{source_type} 熔断器已重置"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置熔断器失败: {e}")
        raise HTTPException(status_code=500, detail=f"重置熔断器失败: {str(e)}")


@router.get("/status/{source_type}", response_model=ApiResponse)
async def get_specific_source_status(source_type: str):
    """
    获取特定数据源的详细状态
    """
    try:
        # 验证数据源类型
        try:
            data_source_type = DataSourceType(source_type.lower())
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"无效的数据源类型: {source_type}"
            )
        
        manager = get_data_source_manager()
        health_status = manager.health_status.get(data_source_type)
        
        if not health_status:
            raise HTTPException(
                status_code=404,
                detail=f"未找到数据源: {source_type}"
            )
        
        # 转换为字典格式
        status_data = {
            "source_type": health_status.source_type.value,
            "status": health_status.status.value,
            "last_success_time": health_status.last_success_time,
            "last_failure_time": health_status.last_failure_time,
            "consecutive_failures": health_status.consecutive_failures,
            "total_requests": health_status.total_requests,
            "successful_requests": health_status.successful_requests,
            "success_rate": health_status.success_rate,
            "average_response_time": health_status.average_response_time,
            "circuit_breaker_active": health_status.is_circuit_open,
            "circuit_breaker_until": health_status.circuit_breaker_until,
            "error_message": health_status.error_message
        }
        
        return ApiResponse(
            success=True,
            data=status_data,
            message=f"{source_type} 状态获取成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据源状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.get("/summary", response_model=ApiResponse)  
async def get_data_source_summary():
    """
    获取数据源状态摘要
    适用于仪表板显示
    """
    try:
        manager = get_data_source_manager()
        health_status = await manager.health_check()
        
        # 构建摘要数据
        summary = {
            "timestamp": time.time(),
            "overall_status": health_status.get("overall_status", "unknown"),
            "active_source": health_status.get("active_source", "unknown"),
            "total_sources": len(DataSourceType),
            "healthy_sources": 0,
            "degraded_sources": 0,
            "unhealthy_sources": 0,
            "circuit_breakers_active": len(health_status.get("circuit_breakers", [])),
            "source_details": []
        }
        
        # 统计各状态的数据源数量
        for source_name, source_data in health_status.get("data_sources", {}).items():
            status = source_data.get("status", "unknown")
            
            if status == "healthy":
                summary["healthy_sources"] += 1
            elif status == "degraded":
                summary["degraded_sources"] += 1
            else:
                summary["unhealthy_sources"] += 1
            
            # 添加详细信息
            summary["source_details"].append({
                "name": source_name,
                "status": status,
                "success_rate": source_data.get("success_rate", 0),
                "average_response_time": source_data.get("average_response_time", 0),
                "circuit_breaker_active": source_data.get("circuit_breaker_active", False)
            })
        
        return ApiResponse(
            success=True,
            data=summary,
            message="数据源摘要获取成功"
        )
    except Exception as e:
        logger.error(f"获取数据源摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取摘要失败: {str(e)}")


@router.get("/config", response_model=ApiResponse)
async def get_data_source_config():
    """
    获取数据源配置信息
    """
    try:
        manager = get_data_source_manager()
        
        config_data = {
            "source_priority": [source.value for source in manager.source_priority],
            "retry_config": {
                "max_attempts": manager.retry_config.max_attempts,
                "base_wait_time": manager.retry_config.base_wait_time,
                "max_wait_time": manager.retry_config.max_wait_time,
                "exponential_base": manager.retry_config.exponential_base,
                "jitter": manager.retry_config.jitter
            },
            "circuit_breaker_config": {
                "failure_threshold": manager.circuit_breaker_config.failure_threshold,
                "success_threshold": manager.circuit_breaker_config.success_threshold,
                "timeout_seconds": manager.circuit_breaker_config.timeout_seconds
            },
            "available_sources": [source.value for source in DataSourceType]
        }
        
        return ApiResponse(
            success=True,
            data=config_data,
            message="数据源配置获取成功"
        )
    except Exception as e:
        logger.error(f"获取数据源配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.post("/test", response_model=ApiResponse)
async def test_data_source_functions():
    """
    测试数据源功能
    执行基本的数据获取测试
    """
    try:
        manager = get_data_source_manager()
        
        test_results = {
            "timestamp": time.time(),
            "tests": {}
        }
        
        # 测试股票基础信息获取
        try:
            stock_basic = await manager.get_stock_basic(exchange="", list_status="L")
            test_results["tests"]["stock_basic"] = {
                "status": "success",
                "count": len(stock_basic) if stock_basic is not None else 0,
                "message": "股票基础信息获取成功"
            }
        except Exception as e:
            test_results["tests"]["stock_basic"] = {
                "status": "failed",
                "error": str(e),
                "message": "股票基础信息获取失败"
            }
        
        # 测试日线数据获取
        try:
            daily_data = await manager.get_daily_data("000001.SZ", limit=5)
            test_results["tests"]["daily_data"] = {
                "status": "success",
                "count": len(daily_data) if daily_data is not None else 0,
                "message": "日线数据获取成功"
            }
        except Exception as e:
            test_results["tests"]["daily_data"] = {
                "status": "failed",
                "error": str(e),
                "message": "日线数据获取失败"
            }
        
        # 测试实时行情获取
        try:
            quotes = await manager.get_realtime_quotes(["000001.SZ"])
            test_results["tests"]["realtime_quotes"] = {
                "status": "success",
                "count": len(quotes) if quotes else 0,
                "message": "实时行情获取成功"
            }
        except Exception as e:
            test_results["tests"]["realtime_quotes"] = {
                "status": "failed",
                "error": str(e),
                "message": "实时行情获取失败"
            }
        
        return ApiResponse(
            success=True,
            data=test_results,
            message="数据源功能测试完成"
        )
    except Exception as e:
        logger.error(f"数据源功能测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"功能测试失败: {str(e)}")


# Prometheus格式的指标端点
@router.get("/metrics/prometheus")
async def get_prometheus_metrics():
    """
    Prometheus格式的指标端点
    """
    try:
        manager = get_data_source_manager()
        metrics = manager.get_health_metrics()
        
        prometheus_output = []
        
        # 添加指标定义
        prometheus_output.append("# HELP data_source_requests_total Total number of requests to data source")
        prometheus_output.append("# TYPE data_source_requests_total counter")
        
        prometheus_output.append("# HELP data_source_success_rate Success rate of data source requests")
        prometheus_output.append("# TYPE data_source_success_rate gauge")
        
        prometheus_output.append("# HELP data_source_response_time_seconds Average response time in seconds")
        prometheus_output.append("# TYPE data_source_response_time_seconds gauge")
        
        prometheus_output.append("# HELP data_source_circuit_breaker_active Circuit breaker status")
        prometheus_output.append("# TYPE data_source_circuit_breaker_active gauge")
        
        # 生成指标数据
        for source_name, source_metrics in metrics.get("sources", {}).items():
            labels = f'source="{source_name}"'
            
            prometheus_output.append(
                f'data_source_requests_total{{{labels}}} {source_metrics.get("total_requests", 0)}'
            )
            prometheus_output.append(
                f'data_source_success_rate{{{labels}}} {source_metrics.get("success_rate", 0)}'
            )
            prometheus_output.append(
                f'data_source_response_time_seconds{{{labels}}} {source_metrics.get("average_response_time_ms", 0) / 1000}'
            )
            prometheus_output.append(
                f'data_source_circuit_breaker_active{{{labels}}} {1 if source_metrics.get("circuit_breaker_active", False) else 0}'
            )
        
        return "\n".join(prometheus_output)
        
    except Exception as e:
        logger.error(f"生成Prometheus指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成指标失败: {str(e)}")