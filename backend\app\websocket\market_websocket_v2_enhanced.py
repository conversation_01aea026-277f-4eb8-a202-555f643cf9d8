"""
市场数据WebSocket V2增强版本
提供实时行情数据推送的简化实现
"""

import asyncio
import json
import logging
import time
import random
from datetime import datetime
from typing import Dict, Set, Optional, Any

from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

# 活跃连接管理
active_connections: Dict[str, WebSocket] = {}
client_subscriptions: Dict[str, Set[str]] = {}

class MarketWebSocketV2Enhanced:
    """增强的市场WebSocket服务"""
    
    def __init__(self):
        self.running = False
        self.push_task: Optional[asyncio.Task] = None
        
    async def start_market_push(self):
        """启动市场数据推送任务"""
        if self.running:
            return
            
        self.running = True
        self.push_task = asyncio.create_task(self._market_data_push_loop())
        logger.info("市场数据推送任务已启动")
    
    async def stop_market_push(self):
        """停止市场数据推送任务"""
        if not self.running:
            return
            
        self.running = False
        if self.push_task:
            self.push_task.cancel()
            try:
                await self.push_task
            except asyncio.CancelledError:
                pass
        logger.info("市场数据推送任务已停止")
    
    async def _market_data_push_loop(self):
        """市场数据推送循环"""
        try:
            while self.running:
                await self._broadcast_market_data()
                await asyncio.sleep(2)  # 每2秒推送一次
        except asyncio.CancelledError:
            logger.info("市场数据推送循环被取消")
        except Exception as e:
            logger.error(f"市场数据推送循环错误: {e}")
    
    async def _broadcast_market_data(self):
        """广播市场数据到所有连接的客户端"""
        if not active_connections:
            return
        
        # 获取所有订阅的股票代码
        all_symbols = set()
        for symbols in client_subscriptions.values():
            all_symbols.update(symbols)
        
        if not all_symbols:
            return
        
        # 生成模拟行情数据
        quotes = []
        for symbol in all_symbols:
            quote_data = self._generate_mock_quote(symbol)
            quotes.append(quote_data)
        
        # 广播给所有客户端
        message = {
            "type": "quotes",
            "data": quotes,
            "timestamp": datetime.now().isoformat()
        }
        
        # 发送给每个连接的客户端
        disconnected_clients = []
        for client_id, websocket in active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.warning(f"发送数据给客户端 {client_id} 失败: {e}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            await self._cleanup_client(client_id)
    
    def _generate_mock_quote(self, symbol: str) -> Dict[str, Any]:
        """生成模拟行情数据"""
        base_price = 10.0
        if symbol == "000001":
            base_price = 12.5
        elif symbol == "600036":
            base_price = 38.2
        elif symbol == "600519":
            base_price = 1680.0
        
        # 随机价格波动
        change_pct = random.uniform(-0.02, 0.02)  # ±2%
        current_price = base_price * (1 + change_pct)
        
        return {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "currentPrice": round(current_price, 2),
            "change": round(current_price - base_price, 2),
            "changePercent": round(change_pct * 100, 2),
            "volume": random.randint(100000, 1000000),
            "amount": random.randint(1000000, 10000000),
            "timestamp": datetime.now().isoformat(),
            "market": "SZ" if symbol.startswith("0") else "SH"
        }
    
    async def _cleanup_client(self, client_id: str):
        """清理客户端连接"""
        if client_id in active_connections:
            del active_connections[client_id]
        if client_id in client_subscriptions:
            del client_subscriptions[client_id]
        logger.info(f"客户端 {client_id} 连接已清理")

# 全局实例
market_ws_v2_enhanced = MarketWebSocketV2Enhanced()

async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket端点处理函数"""
    await websocket.accept()
    
    # 注册客户端连接
    active_connections[client_id] = websocket
    client_subscriptions[client_id] = set()
    
    logger.info(f"客户端 {client_id} 已连接")
    
    # 启动市场数据推送（如果还没启动）
    await market_ws_v2_enhanced.start_market_push()
    
    try:
        # 发送欢迎消息
        welcome_message = {
            "type": "welcome",
            "message": "市场数据WebSocket连接成功",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # 处理客户端消息
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_client_message(client_id, message, websocket)
            except asyncio.TimeoutError:
                # 发送心跳
                await websocket.send_text(json.dumps({
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }))
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"处理客户端 {client_id} 消息错误: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"消息处理错误: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }))
    
    except WebSocketDisconnect:
        logger.info(f"客户端 {client_id} 主动断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    finally:
        # 清理连接
        await market_ws_v2_enhanced._cleanup_client(client_id)

async def handle_client_message(client_id: str, message: Dict[str, Any], websocket: WebSocket):
    """处理客户端消息"""
    msg_type = message.get("type")
    
    if msg_type == "subscribe":
        # 订阅股票
        symbols = message.get("symbols", [])
        if isinstance(symbols, str):
            symbols = [symbols]
        
        client_subscriptions[client_id].update(symbols)
        
        response = {
            "type": "subscribed",
            "symbols": symbols,
            "message": f"已订阅 {len(symbols)} 只股票",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(response))
        logger.info(f"客户端 {client_id} 订阅股票: {symbols}")
        
    elif msg_type == "unsubscribe":
        # 取消订阅
        symbols = message.get("symbols", [])
        if isinstance(symbols, str):
            symbols = [symbols]
        
        for symbol in symbols:
            client_subscriptions[client_id].discard(symbol)
        
        response = {
            "type": "unsubscribed",
            "symbols": symbols,
            "message": f"已取消订阅 {len(symbols)} 只股票",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(response))
        logger.info(f"客户端 {client_id} 取消订阅股票: {symbols}")
        
    elif msg_type == "ping":
        # 心跳响应
        response = {
            "type": "pong",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(response))
        
    else:
        # 未知消息类型
        response = {
            "type": "error",
            "message": f"未知消息类型: {msg_type}",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(response))