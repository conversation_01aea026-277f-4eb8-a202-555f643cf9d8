"""
API限流与重试机制
提供第三方API调用的限流、重试、熔断和降级策略
"""
import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import aiohttp
from functools import wraps

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class RateLimitConfig:
    """限流配置"""
    max_calls: int = 100           # 最大调用次数
    time_window: int = 60          # 时间窗口(秒)
    burst_limit: int = 10          # 突发限制
    
    
@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3           # 最大重试次数
    base_delay: float = 1.0        # 基础延迟(秒)
    max_delay: float = 60.0        # 最大延迟(秒)
    exponential_base: float = 2.0  # 指数退避基数
    jitter: bool = True            # 是否添加随机抖动


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5     # 失败阈值
    recovery_timeout: int = 60     # 恢复超时(秒)
    success_threshold: int = 3     # 半开状态成功阈值


@dataclass
class ApiCallStats:
    """API调用统计"""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    rate_limited_calls: int = 0
    circuit_breaker_calls: int = 0
    last_call_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None


class RateLimiter:
    """令牌桶限流器"""
    
    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.tokens = config.max_calls
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """获取令牌"""
        async with self._lock:
            now = time.time()
            
            # 计算需要补充的令牌数
            time_passed = now - self.last_refill
            tokens_to_add = int(time_passed * self.config.max_calls / self.config.time_window)
            
            if tokens_to_add > 0:
                self.tokens = min(self.config.max_calls, self.tokens + tokens_to_add)
                self.last_refill = now
            
            # 检查是否有可用令牌
            if self.tokens > 0:
                self.tokens -= 1
                return True
            
            return False


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """执行函数调用"""
        async with self._lock:
            if self.state == CircuitState.OPEN:
                # 检查是否可以进入半开状态
                if (self.last_failure_time and 
                    datetime.now() - self.last_failure_time > timedelta(seconds=self.config.recovery_timeout)):
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info("Circuit breaker entering HALF_OPEN state")
                else:
                    raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs)
            await self._on_success()
            return result
        except Exception as e:
            await self._on_failure()
            raise
    
    async def _on_success(self):
        """成功回调"""
        async with self._lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    logger.info("Circuit breaker entering CLOSED state")
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    async def _on_failure(self):
        """失败回调"""
        async with self._lock:
            self.failure_count += 1
            self.last_failure_time = datetime.now()
            
            if self.state == CircuitState.CLOSED and self.failure_count >= self.config.failure_threshold:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker entering OPEN state")
            elif self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker returning to OPEN state")


class ApiClient:
    """带限流和重试的API客户端"""
    
    def __init__(
        self,
        name: str,
        rate_limit_config: Optional[RateLimitConfig] = None,
        retry_config: Optional[RetryConfig] = None,
        circuit_breaker_config: Optional[CircuitBreakerConfig] = None
    ):
        self.name = name
        self.rate_limiter = RateLimiter(rate_limit_config or RateLimitConfig())
        self.retry_config = retry_config or RetryConfig()
        self.circuit_breaker = CircuitBreaker(circuit_breaker_config or CircuitBreakerConfig())
        self.stats = ApiCallStats()
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100, limit_per_host=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def call_api(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Dict[str, Any]:
        """调用API"""
        self.stats.total_calls += 1
        self.stats.last_call_time = datetime.now()
        
        # 检查限流
        if not await self.rate_limiter.acquire():
            self.stats.rate_limited_calls += 1
            raise Exception(f"Rate limit exceeded for {self.name}")
        
        # 使用熔断器和重试机制
        return await self._call_with_retry(method, url, **kwargs)
    
    async def _call_with_retry(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Dict[str, Any]:
        """带重试的API调用"""
        last_exception = None
        
        for attempt in range(self.retry_config.max_retries + 1):
            try:
                return await self.circuit_breaker.call(self._make_request, method, url, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.retry_config.max_retries:
                    delay = self._calculate_delay(attempt)
                    logger.warning(f"API call failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    self.stats.failed_calls += 1
                    self.stats.last_failure_time = datetime.now()
                    logger.error(f"API call failed after {self.retry_config.max_retries + 1} attempts: {e}")
        
        raise last_exception
    
    async def _make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Dict[str, Any]:
        """发起HTTP请求"""
        if not self.session:
            raise Exception("Session not initialized. Use async context manager.")
        
        async with self.session.request(method, url, **kwargs) as response:
            if response.status >= 400:
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"HTTP {response.status}"
                )
            
            result = await response.json()
            self.stats.successful_calls += 1
            self.stats.last_success_time = datetime.now()
            return result
    
    def _calculate_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        delay = self.retry_config.base_delay * (self.retry_config.exponential_base ** attempt)
        delay = min(delay, self.retry_config.max_delay)
        
        if self.retry_config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # 添加50%的随机抖动
        
        return delay
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'name': self.name,
            'stats': {
                'total_calls': self.stats.total_calls,
                'successful_calls': self.stats.successful_calls,
                'failed_calls': self.stats.failed_calls,
                'rate_limited_calls': self.stats.rate_limited_calls,
                'circuit_breaker_calls': self.stats.circuit_breaker_calls,
                'success_rate': (
                    self.stats.successful_calls / self.stats.total_calls 
                    if self.stats.total_calls > 0 else 0
                ),
                'last_call_time': self.stats.last_call_time.isoformat() if self.stats.last_call_time else None,
                'last_success_time': self.stats.last_success_time.isoformat() if self.stats.last_success_time else None,
                'last_failure_time': self.stats.last_failure_time.isoformat() if self.stats.last_failure_time else None,
            },
            'circuit_breaker': {
                'state': self.circuit_breaker.state.value,
                'failure_count': self.circuit_breaker.failure_count,
                'success_count': self.circuit_breaker.success_count,
            },
            'rate_limiter': {
                'tokens': self.rate_limiter.tokens,
                'max_calls': self.rate_limiter.config.max_calls,
                'time_window': self.rate_limiter.config.time_window,
            }
        }


def with_rate_limit(
    rate_limit_config: Optional[RateLimitConfig] = None,
    retry_config: Optional[RetryConfig] = None,
    circuit_breaker_config: Optional[CircuitBreakerConfig] = None
):
    """装饰器：为函数添加限流和重试机制"""
    def decorator(func):
        client = ApiClient(
            name=func.__name__,
            rate_limit_config=rate_limit_config,
            retry_config=retry_config,
            circuit_breaker_config=circuit_breaker_config
        )
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await client.circuit_breaker.call(func, *args, **kwargs)
        
        wrapper.get_stats = client.get_stats
        return wrapper
    
    return decorator
