@echo off
REM 行情中心依赖安装脚本 (Windows版本)
REM 用途: 一键安装行情中心所需的所有依赖

echo 🚀 开始安装行情中心依赖...

REM 检查Python环境
echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装Python
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python版本: %python_version%

REM 检查pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip 未安装，请先安装pip
    pause
    exit /b 1
)

REM 安装后端依赖
echo 📦 安装后端Python依赖...
cd backend

REM 升级pip
python -m pip install --upgrade pip

REM 安装基础依赖
echo 📦 安装基础依赖...
pip install -r requirements.txt

REM 安装行情数据源依赖
echo 📈 安装行情数据源依赖...
pip install tushare akshare pandas-ta

REM 安装可选依赖
echo 🔧 安装可选依赖...
pip install redis aioredis

echo ✅ 后端依赖安装完成

REM 返回根目录
cd ..

REM 检查Node.js环境
echo 📋 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装Node.js
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('node --version') do set node_version=%%i
echo ✅ Node.js版本: %node_version%

REM 检查pnpm
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo 📦 安装pnpm...
    npm install -g pnpm
)

for /f "tokens=1" %%i in ('pnpm --version') do set pnpm_version=%%i
echo ✅ pnpm版本: %pnpm_version%

REM 安装前端依赖
echo 📦 安装前端依赖...
cd frontend
pnpm install

echo ✅ 前端依赖安装完成

REM 返回根目录
cd ..

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist "backend\data" mkdir backend\data
if not exist "backend\logs" mkdir backend\logs
if not exist "backend\uploads" mkdir backend\uploads

echo ✅ 目录创建完成

REM 检查环境配置文件
echo 📋 检查环境配置...
if not exist "backend\.env" (
    echo ⚠️  后端.env文件不存在，请配置环境变量
    echo    参考: backend\.env.example
)

if not exist "frontend\.env.development" (
    echo ⚠️  前端.env.development文件不存在，请配置环境变量
    echo    参考: frontend\.env.example
)

REM 显示下一步操作
echo.
echo 🎉 依赖安装完成！
echo.
echo 📋 下一步操作:
echo 1. 配置环境变量:
echo    - 编辑 backend\.env
echo    - 设置 TUSHARE_TOKEN=your_actual_token
echo    - 设置 USE_REAL_DATA=true
echo.
echo 2. 启动Redis服务 (可选，用于缓存):
echo    - 下载并安装Redis for Windows
echo    - 或使用Docker: docker run -d -p 6379:6379 redis
echo.
echo 3. 启动后端服务:
echo    cd backend
echo    python main.py
echo.
echo 4. 启动前端服务:
echo    cd frontend
echo    pnpm dev
echo.
echo 5. 验证安装:
echo    访问 http://localhost:8000/docs (后端API文档)
echo    访问 http://localhost:5173 (前端页面)
echo.
echo 📖 详细文档: docs\market-api-spec.md
echo ✅ 验收清单: docs\market-verification-checklist.md

pause
