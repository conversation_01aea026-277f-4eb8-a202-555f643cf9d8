# 🟡 P1 - 短期优化计划

## 1. 数据存储升级方案

### 1.1 SQLite 迁移方案

#### 方案概述
将CSV文件迁移到SQLite数据库，提升查询性能和数据完整性。

#### 实施步骤

**第一步：创建数据库结构**
```sql
-- 股票基础信息表
CREATE TABLE stocks (
    symbol TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    market TEXT NOT NULL,
    industry TEXT,
    listing_date DATE,
    status TEXT DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 股票日线数据表 (分表存储)
CREATE TABLE daily_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    trade_date DATE NOT NULL,
    open_price REAL,
    close_price REAL,
    high_price REAL,
    low_price REAL,
    volume INTEGER,
    amount REAL,
    change_percent REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_date)
);

-- 创建索引提升查询性能
CREATE INDEX idx_daily_symbol_date ON daily_data(symbol, trade_date);
CREATE INDEX idx_daily_date ON daily_data(trade_date);
CREATE INDEX idx_stocks_market ON stocks(market);
CREATE INDEX idx_stocks_industry ON stocks(industry);
```

**第二步：数据迁移脚本**
```python
async def migrate_csv_to_sqlite(csv_dir: Path, db_path: Path):
    """CSV到SQLite的迁移脚本"""
    import sqlite3
    import pandas as pd
    
    conn = sqlite3.connect(db_path)
    
    # 处理每个CSV文件
    for csv_file in csv_dir.glob("*.csv"):
        symbol = csv_file.stem
        
        try:
            # 读取CSV数据
            df = pd.read_csv(csv_file)
            
            # 数据清洗和标准化
            df = clean_and_standardize(df, symbol)
            
            # 插入到数据库
            df.to_sql('daily_data', conn, if_exists='append', index=False)
            
            # 插入股票基础信息
            stock_info = extract_stock_info(symbol, df)
            conn.execute("""
                INSERT OR REPLACE INTO stocks 
                (symbol, name, market, industry) 
                VALUES (?, ?, ?, ?)
            """, stock_info)
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"迁移失败 {symbol}: {e}")
            
    conn.close()
```

**第三步：增量更新机制**
```python
async def incremental_update():
    """增量更新机制"""
    # 检查新文件
    # 只处理修改时间晚于上次更新的文件
    # 使用事务确保数据一致性
    pass
```

### 1.2 PostgreSQL 高级方案 (可选)

适用于大规模数据和高并发场景：

```sql
-- 使用时间分区表
CREATE TABLE daily_data (
    symbol TEXT NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    -- ... 其他字段
) PARTITION BY RANGE (trade_date);

-- 创建年度分区
CREATE TABLE daily_data_2023 PARTITION OF daily_data
    FOR VALUES FROM ('2023-01-01') TO ('2024-01-01');

CREATE TABLE daily_data_2024 PARTITION OF daily_data
    FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

## 2. 缓存机制优化

### 2.1 分片缓存实现

```python
class ShardedCache:
    """分片缓存实现"""
    
    def __init__(self, shard_count: int = 16):
        self.shard_count = shard_count
        self.shards = [LRUCache(maxsize=1000) for _ in range(shard_count)]
    
    def _get_shard(self, key: str) -> LRUCache:
        """根据key获取对应分片"""
        hash_value = hash(key)
        return self.shards[hash_value % self.shard_count]
    
    async def get(self, key: str):
        shard = self._get_shard(key)
        return shard.get(key)
    
    async def set(self, key: str, value: Any, ttl: int = 3600):
        shard = self._get_shard(key)
        shard.set(key, value, ttl)
```

### 2.2 LRU淘汰策略

```python
from collections import OrderedDict
import time

class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, maxsize: int = 1000):
        self.maxsize = maxsize
        self.cache = OrderedDict()
        self.timestamps = {}
    
    def get(self, key: str):
        if key in self.cache:
            # 更新访问时间
            self.cache.move_to_end(key)
            return self.cache[key]
        return None
    
    def set(self, key: str, value: Any, ttl: int = 3600):
        # 检查TTL
        if key in self.timestamps:
            if time.time() - self.timestamps[key] > ttl:
                del self.cache[key]
                del self.timestamps[key]
        
        # 添加或更新
        self.cache[key] = value
        self.timestamps[key] = time.time()
        self.cache.move_to_end(key)
        
        # 检查容量限制
        while len(self.cache) > self.maxsize:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            del self.timestamps[oldest_key]
```

### 2.3 热数据预加载

```python
class HotDataPreloader:
    """热数据预加载器"""
    
    def __init__(self, cache: ShardedCache):
        self.cache = cache
        self.hot_symbols = []
    
    async def identify_hot_symbols(self):
        """识别热门股票"""
        # 基于访问频率统计
        # 基于交易量排序
        # 基于关注度排序
        pass
    
    async def preload_hot_data(self):
        """预加载热门数据"""
        for symbol in self.hot_symbols:
            # 预加载最近30天的数据
            data = await self.load_recent_data(symbol, days=30)
            await self.cache.set(f"hot:{symbol}", data)
```

## 3. 性能监控和优化

### 3.1 性能指标收集

```python
import time
from functools import wraps

def performance_monitor(operation_type: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                success = True
                error = None
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                duration = time.time() - start_time
                
                # 记录性能指标
                metrics.record({
                    'operation': operation_type,
                    'function': func.__name__,
                    'duration': duration,
                    'success': success,
                    'error': error,
                    'timestamp': time.time()
                })
                
                # 性能告警
                if duration > 5.0:  # 超过5秒告警
                    logger.warning(f"慢查询告警: {func.__name__} 耗时 {duration:.2f}s")
            
            return result
        return wrapper
    return decorator
```

### 3.2 数据库查询优化

```python
class QueryOptimizer:
    """查询优化器"""
    
    @staticmethod
    def optimize_date_range_query(symbol: str, start_date: str, end_date: str):
        """优化日期范围查询"""
        # 使用索引提示
        # 限制返回字段
        # 添加LIMIT保护
        sql = """
        SELECT symbol, trade_date, close_price, volume
        FROM daily_data 
        WHERE symbol = ? 
          AND trade_date BETWEEN ? AND ?
        ORDER BY trade_date DESC
        LIMIT 10000
        """
        return sql
    
    @staticmethod
    def optimize_aggregation_query(symbols: List[str]):
        """优化聚合查询"""
        # 使用子查询优化
        # 避免全表扫描
        sql = """
        SELECT symbol, 
               AVG(close_price) as avg_price,
               MAX(high_price) as max_price,
               MIN(low_price) as min_price
        FROM (
            SELECT * FROM daily_data 
            WHERE symbol IN ({})
              AND trade_date >= date('now', '-30 days')
        ) recent_data
        GROUP BY symbol
        """.format(','.join(['?' for _ in symbols]))
        return sql
```

## 4. 内存优化策略

### 4.1 数据分页加载

```python
class PaginatedDataLoader:
    """分页数据加载器"""
    
    def __init__(self, page_size: int = 1000):
        self.page_size = page_size
    
    async def load_paginated(self, query, total_count: int):
        """分页加载大量数据"""
        pages = (total_count + self.page_size - 1) // self.page_size
        
        for page in range(pages):
            offset = page * self.page_size
            chunk = await self.load_chunk(query, offset, self.page_size)
            yield chunk
```

### 4.2 内存使用监控

```python
import psutil
import gc

class MemoryMonitor:
    """内存监控器"""
    
    @staticmethod
    def get_memory_usage():
        """获取当前内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent()
        }
    
    @staticmethod
    def force_gc_if_needed(threshold_mb: int = 500):
        """内存压力时强制垃圾回收"""
        memory_usage = MemoryMonitor.get_memory_usage()
        
        if memory_usage['rss'] > threshold_mb:
            logger.info(f"内存使用过高 ({memory_usage['rss']:.1f}MB)，执行垃圾回收")
            gc.collect()
```

## 5. 实施时间表

### 第1周：数据存储升级
- [ ] 设计SQLite数据库结构
- [ ] 实现数据迁移脚本
- [ ] 测试数据完整性

### 第2周：缓存机制优化
- [ ] 实现分片缓存
- [ ] 添加LRU淘汰策略
- [ ] 实现热数据预加载

### 第3周：性能优化
- [ ] 添加性能监控
- [ ] 优化数据库查询
- [ ] 内存使用优化

### 第4周：测试和调优
- [ ] 性能基准测试
- [ ] 压力测试
- [ ] 生产环境部署

## 6. 成功指标

### 性能指标
- [ ] 查询响应时间 < 2秒
- [ ] 并发查询支持 > 10个
- [ ] 内存使用 < 1GB
- [ ] 缓存命中率 > 80%

### 稳定性指标
- [ ] 服务可用性 > 99.5%
- [ ] 错误率 < 1%
- [ ] 数据一致性 100%

## 7. 风险评估

### 高风险
- 数据迁移过程中的数据丢失
- 性能优化后的功能回归

### 中风险
- 缓存不一致导致的数据错误
- 内存泄漏问题

### 低风险
- 查询性能不达预期
- 监控指标不准确

## 8. 回滚计划

- 保留原CSV文件作为备份
- 实现快速切换机制
- 准备回滚脚本和文档