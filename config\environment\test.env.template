# 测试环境配置文件
# 复制为 .env.test 并根据需要修改

# === 数据库配置 ===
DATABASE_URL=sqlite:///./test_quant_platform.db
DATABASE_ECHO=false

# === Redis配置 ===
REDIS_URL=redis://localhost:6379/1
REDIS_PASSWORD=

# === API配置 ===
API_V1_PREFIX=/api/v1
API_HOST=127.0.0.1
API_PORT=8001
API_DEBUG=true

# === 安全配置 ===
SECRET_KEY=test-secret-key-not-for-production
ACCESS_TOKEN_EXPIRE_MINUTES=5
ALGORITHM=HS256

# === 外部数据源 ===
TUSHARE_TOKEN=
AKSHARE_ENABLED=false
USE_MOCK_DATA=true

# === 日志配置 ===
LOG_LEVEL=DEBUG
LOG_FORMAT=simple
LOG_FILE=logs/test.log

# === 前端配置 ===
FRONTEND_URL=http://localhost:5174
CORS_ORIGINS=["http://localhost:5174"]

# === 测试配置 ===
TESTING=true
DISABLE_AUTH=false
MOCK_EXTERNAL_APIS=true
