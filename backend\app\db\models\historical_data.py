"""
历史数据模型
支持高效的时序数据存储和查询
"""

from sqlalchemy import Column, String, Float, BigInteger, DateTime, Integer, Index, Text
from sqlalchemy.sql import func
from app.core.database import Base


class HistoricalKLineData(Base):
    """
    历史K线数据表
    按日期分区，支持高效的时间范围查询
    """
    __tablename__ = "historical_kline_data"
    
    # 主键：股票代码 + 日期
    symbol = Column(String(20), primary_key=True, comment="股票代码")
    trade_date = Column(DateTime, primary_key=True, comment="交易日期")
    
    # OHLCV数据
    open_price = Column(Float, nullable=False, comment="开盘价")
    close_price = Column(Float, nullable=False, comment="收盘价")
    high_price = Column(Float, nullable=False, comment="最高价")
    low_price = Column(Float, nullable=False, comment="最低价")
    volume = Column(BigInteger, nullable=False, default=0, comment="成交量(手)")
    amount = Column(Float, nullable=False, default=0, comment="成交额(元)")
    
    # 计算字段
    change_amount = Column(Float, comment="涨跌额")
    change_percent = Column(Float, comment="涨跌幅(%)")
    amplitude = Column(Float, comment="振幅(%)")
    turnover_rate = Column(Float, comment="换手率(%)")
    
    # 技术指标（可选）
    ma5 = Column(Float, comment="5日均线")
    ma10 = Column(Float, comment="10日均线")
    ma20 = Column(Float, comment="20日均线")
    ma60 = Column(Float, comment="60日均线")
    
    # 元数据
    data_source = Column(String(50), default="csv_import", comment="数据来源")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 创建索引
    __table_args__ = (
        # 按股票代码和日期的复合索引（主要查询模式）
        Index('idx_symbol_date', 'symbol', 'trade_date'),
        # 按日期的索引（全市场查询）
        Index('idx_trade_date', 'trade_date'),
        # 按股票代码的索引（单股票历史查询）
        Index('idx_symbol', 'symbol'),
        # 支持涨跌幅查询
        Index('idx_change_percent', 'change_percent'),
        # 支持成交额查询
        Index('idx_amount', 'amount'),
        {'comment': '历史K线数据表，支持高效时序查询'}
    )


class StockBasicInfo(Base):
    """
    股票基本信息表
    """
    __tablename__ = "stock_basic_info"
    
    symbol = Column(String(20), primary_key=True, comment="股票代码")
    name = Column(String(100), nullable=False, comment="股票名称")
    market = Column(String(10), nullable=False, comment="市场代码 SH/SZ/BJ")
    industry = Column(String(50), comment="所属行业")
    sector = Column(String(50), comment="所属板块")
    
    # 上市信息
    list_date = Column(DateTime, comment="上市日期")
    delist_date = Column(DateTime, comment="退市日期")
    status = Column(String(20), default="L", comment="状态 L-正常 D-退市 P-暂停")
    
    # 基本财务信息
    total_share = Column(Float, comment="总股本")
    float_share = Column(Float, comment="流通股本")
    market_cap = Column(Float, comment="总市值")
    
    # 数据统计信息
    first_trade_date = Column(DateTime, comment="首个交易日期")
    last_trade_date = Column(DateTime, comment="最后交易日期")
    total_records = Column(Integer, default=0, comment="历史数据条数")
    
    # 元数据
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_market', 'market'),
        Index('idx_industry', 'industry'),
        Index('idx_name', 'name'),
        Index('idx_status', 'status'),
        {'comment': '股票基本信息表'}
    )


class DataImportLog(Base):
    """
    数据导入日志表
    """
    __tablename__ = "data_import_log"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, comment="股票代码")
    import_type = Column(String(20), nullable=False, comment="导入类型 csv/api/manual")
    file_path = Column(Text, comment="原始文件路径")
    
    # 导入统计
    total_records = Column(Integer, default=0, comment="总记录数")
    success_records = Column(Integer, default=0, comment="成功记录数")
    error_records = Column(Integer, default=0, comment="错误记录数")
    
    # 数据范围
    start_date = Column(DateTime, comment="数据开始日期")
    end_date = Column(DateTime, comment="数据结束日期")
    
    # 状态信息
    status = Column(String(20), default="processing", comment="状态 processing/completed/failed")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    import_started_at = Column(DateTime, default=func.now(), comment="导入开始时间")
    import_completed_at = Column(DateTime, comment="导入完成时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_symbol_import', 'symbol', 'import_started_at'),
        Index('idx_status', 'status'),
        Index('idx_import_date', 'import_started_at'),
        {'comment': '数据导入日志表'}
    )


class MarketCalendar(Base):
    """
    交易日历表
    """
    __tablename__ = "market_calendar"
    
    trade_date = Column(DateTime, primary_key=True, comment="交易日期")
    is_trading_day = Column(Integer, default=1, comment="是否交易日 1-是 0-否")
    market = Column(String(10), default="ALL", comment="市场 SH/SZ/ALL")
    
    # 节假日信息
    holiday_name = Column(String(50), comment="节假日名称")
    week_day = Column(Integer, comment="星期几 1-7")
    
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    __table_args__ = (
        Index('idx_trade_date_market', 'trade_date', 'market'),
        Index('idx_is_trading', 'is_trading_day'),
        {'comment': '交易日历表'}
    )