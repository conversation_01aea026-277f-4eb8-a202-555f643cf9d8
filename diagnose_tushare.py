#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare连接诊断脚本
"""

import requests
import json
import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

from backend.app.core.config import settings

def test_network():
    """测试网络连接"""
    print("测试网络连接...")
    
    try:
        # 测试基础网络
        response = requests.get('https://www.baidu.com', timeout=10)
        if response.status_code == 200:
            print("OK: 基础网络连接正常")
        else:
            print(f"WARNING: 网络连接异常，状态码: {response.status_code}")
    except Exception as e:
        print(f"ERROR: 网络连接失败 - {e}")
        return False
        
    return True

def test_tushare_api():
    """测试Tushare API连接"""
    print("测试Tushare API连接...")
    
    if not settings.TUSHARE_API_TOKEN:
        print("ERROR: Token未配置")
        return False
    
    print(f"Token: {settings.TUSHARE_API_TOKEN[:10]}...")
    
    # 构造请求
    url = "http://api.tushare.pro"
    data = {
        "api_name": "stock_basic",
        "token": settings.TUSHARE_API_TOKEN,
        "params": {
            "exchange": "SSE",
            "list_status": "L"
        },
        "fields": "ts_code,symbol,name"
    }
    
    try:
        print("发送API请求...")
        response = requests.post(url, json=data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)[:500]}...")
            
            if result.get('code') == 0:
                items = result.get('data', {}).get('items', [])
                print(f"SUCCESS: 获取到 {len(items)} 条数据")
                if items:
                    print(f"示例数据: {items[0]}")
                return True
            else:
                print(f"ERROR: API返回错误 - {result.get('msg')}")
                return False
        else:
            print(f"ERROR: HTTP错误 {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"ERROR: 请求失败 - {e}")
        return False

def check_config():
    """检查配置"""
    print("检查配置...")
    
    print(f"TUSHARE_API_TOKEN: {'已配置' if settings.TUSHARE_API_TOKEN else '未配置'}")
    print(f"USE_REAL_DATA: {settings.USE_REAL_DATA}")
    print(f"PREFERRED_DATA_SOURCE: {settings.PREFERRED_DATA_SOURCE}")
    
    if hasattr(settings, 'data_source_priority'):
        print(f"数据源优先级: {settings.data_source_priority}")

if __name__ == "__main__":
    print("Tushare诊断工具")
    print("="*30)
    
    # 检查配置
    check_config()
    print()
    
    # 测试网络
    network_ok = test_network()
    print()
    
    if network_ok:
        # 测试API
        api_ok = test_tushare_api()
        print()
        
        if api_ok:
            print("结论: Tushare配置正常，可以使用")
        else:
            print("结论: Tushare API有问题，请检查Token或网络")
    else:
        print("结论: 网络连接有问题")