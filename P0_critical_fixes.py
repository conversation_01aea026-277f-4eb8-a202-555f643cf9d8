#!/usr/bin/env python3
"""
P0 - 立即修复脚本
修复服务启动和关键依赖问题
"""
import os
import sys
import subprocess
import json
from pathlib import Path

def main():
    print("🔴 P0 - 立即修复开始")
    
    # 1. 修复后端依赖
    fix_backend_dependencies()
    
    # 2. 检查前端依赖
    check_frontend_dependencies()
    
    # 3. 完善错误处理
    add_error_handling()
    
    print("✅ P0 修复完成")

def fix_backend_dependencies():
    """修复后端依赖问题"""
    print("\n📦 修复后端依赖...")
    
    backend_dir = Path("backend")
    requirements_file = backend_dir / "requirements.txt"
    
    # 读取现有依赖
    with open(requirements_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加缺失的依赖
    missing_deps = [
        "# ===== 异步文件操作 =====",
        "aiofiles==23.2.0  # 异步文件读写支持"
    ]
    
    # 在工具库部分添加 aiofiles
    if "aiofiles" not in content:
        # 在工具库部分添加
        content = content.replace(
            "# ===== 工具库 =====",
            "# ===== 工具库 =====\naiofiles==23.2.0  # 异步文件读写支持"
        )
        
        # 写回文件
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已添加 aiofiles 依赖")
    else:
        print("✅ aiofiles 依赖已存在")

def check_frontend_dependencies():
    """检查前端依赖"""
    print("\n📦 检查前端依赖...")
    
    frontend_dir = Path("frontend")
    if not (frontend_dir / "node_modules").exists():
        print("⚠️ 前端依赖未安装，请运行: cd frontend && npm install")
    else:
        print("✅ 前端依赖已安装")

def add_error_handling():
    """添加错误处理改进"""
    print("\n🛠️ 完善错误处理...")
    
    # 创建错误处理工具类
    error_handler_content = '''"""
统一错误处理工具
"""
import logging
import traceback
from typing import Any, Dict, Optional
from fastapi import HTTPException
from datetime import datetime

logger = logging.getLogger(__name__)

class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_file_error(error: Exception, context: str) -> Dict[str, Any]:
        """处理文件访问错误"""
        error_msg = f"文件操作失败: {str(error)}"
        logger.error(f"{context} - {error_msg}", exc_info=True)
        
        return {
            "success": False,
            "error": error_msg,
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def handle_memory_error(error: Exception, context: str) -> Dict[str, Any]:
        """处理内存不足错误"""
        error_msg = f"内存不足: {str(error)}"
        logger.error(f"{context} - {error_msg}", exc_info=True)
        
        return {
            "success": False,
            "error": "数据量过大，请缩小查询范围",
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def handle_timeout_error(error: Exception, context: str) -> Dict[str, Any]:
        """处理超时错误"""
        error_msg = f"操作超时: {str(error)}"
        logger.error(f"{context} - {error_msg}", exc_info=True)
        
        return {
            "success": False,
            "error": "操作超时，请稍后重试",
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def handle_api_error(error: Exception, context: str) -> HTTPException:
        """处理API错误"""
        if isinstance(error, FileNotFoundError):
            return HTTPException(status_code=404, detail="资源不存在")
        elif isinstance(error, MemoryError):
            return HTTPException(status_code=413, detail="数据量过大")
        elif isinstance(error, TimeoutError):
            return HTTPException(status_code=408, detail="请求超时")
        else:
            logger.error(f"{context} - 未知错误: {str(error)}", exc_info=True)
            return HTTPException(status_code=500, detail="服务器内部错误")

class MemoryLimiter:
    """内存使用限制器"""
    
    def __init__(self, max_rows: int = 100000):
        self.max_rows = max_rows
    
    def check_data_size(self, data_size: int, context: str = "数据查询"):
        """检查数据大小是否超限"""
        if data_size > self.max_rows:
            raise MemoryError(f"{context}: 数据量 {data_size} 超过限制 {self.max_rows}")
    
    def safe_slice(self, data: Any, max_size: Optional[int] = None):
        """安全切片数据"""
        limit = max_size or self.max_rows
        if hasattr(data, '__len__') and len(data) > limit:
            return data[:limit]
        return data

class TimeoutManager:
    """超时管理器"""
    
    @staticmethod
    def get_timeout(operation_type: str) -> int:
        """获取操作超时时间(秒)"""
        timeouts = {
            "file_read": 30,
            "data_query": 60,
            "api_request": 15,
            "cache_operation": 5
        }
        return timeouts.get(operation_type, 30)
'''
    
    # 写入错误处理文件
    error_handler_file = Path("backend/app/core/error_handler.py")
    error_handler_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(error_handler_file, 'w', encoding='utf-8') as f:
        f.write(error_handler_content)
    
    print("✅ 已创建统一错误处理器")

if __name__ == "__main__":
    main()