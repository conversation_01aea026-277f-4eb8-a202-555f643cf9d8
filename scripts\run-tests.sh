#!/bin/bash
# 测试运行脚本

set -e

echo "🧪 开始运行测试套件..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否安装了依赖
check_dependencies() {
    echo "📦 检查依赖..."
    
    # 检查后端依赖
    if [ ! -d "backend/venv" ]; then
        echo -e "${YELLOW}警告: 后端虚拟环境不存在，尝试创建...${NC}"
        cd backend
        python -m venv venv
        source venv/Scripts/activate || source venv/bin/activate
        pip install -r requirements.txt
        cd ..
    fi
    
    # 检查前端依赖
    if [ ! -d "frontend/node_modules" ]; then
        echo -e "${YELLOW}警告: 前端依赖未安装，正在安装...${NC}"
        cd frontend
        pnpm install
        cd ..
    fi
}

# 运行后端测试
run_backend_tests() {
    echo "🐍 运行后端测试..."
    cd backend
    
    # 激活虚拟环境
    source venv/Scripts/activate || source venv/bin/activate
    
    # 运行测试
    pytest app/tests/ \
        --cov=app \
        --cov-report=html:htmlcov \
        --cov-report=term-missing \
        --cov-report=xml \
        --cov-fail-under=80 \
        -v
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 后端测试通过${NC}"
    else
        echo -e "${RED}❌ 后端测试失败${NC}"
        exit 1
    fi
    
    cd ..
}

# 运行前端测试
run_frontend_tests() {
    echo "🎨 运行前端测试..."
    cd frontend
    
    # 运行测试
    pnpm test:run --coverage
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 前端测试通过${NC}"
    else
        echo -e "${RED}❌ 前端测试失败${NC}"
        exit 1
    fi
    
    cd ..
}

# 生成测试报告
generate_report() {
    echo "📊 生成测试报告..."
    
    # 创建报告目录
    mkdir -p test-reports
    
    # 合并覆盖率报告
    echo "# 测试覆盖率报告" > test-reports/coverage-summary.md
    echo "" >> test-reports/coverage-summary.md
    echo "## 后端覆盖率" >> test-reports/coverage-summary.md
    echo "详见: backend/htmlcov/index.html" >> test-reports/coverage-summary.md
    echo "" >> test-reports/coverage-summary.md
    echo "## 前端覆盖率" >> test-reports/coverage-summary.md
    echo "详见: frontend/coverage/index.html" >> test-reports/coverage-summary.md
    
    echo -e "${GREEN}📊 测试报告已生成到 test-reports/ 目录${NC}"
}

# 主函数
main() {
    echo "🚀 量化投资平台测试套件"
    echo "=========================="
    
    # 检查依赖
    check_dependencies
    
    # 运行测试
    run_backend_tests
    run_frontend_tests
    
    # 生成报告
    generate_report
    
    echo ""
    echo -e "${GREEN}🎉 所有测试都通过了！${NC}"
    echo -e "${GREEN}📊 覆盖率目标: 80%+${NC}"
    echo ""
    echo "查看详细报告:"
    echo "- 后端: backend/htmlcov/index.html"
    echo "- 前端: frontend/coverage/index.html"
}

# 运行主函数
main "$@"