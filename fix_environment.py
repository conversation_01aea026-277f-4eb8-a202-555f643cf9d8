#!/usr/bin/env python3
"""
环境配置修复脚本
根据确认的方案修复.env配置
"""

import os
from pathlib import Path

def fix_backend_env():
    """修复后端.env配置"""
    print("🔧 修复后端环境配置...")
    
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("❌ backend/.env 文件不存在")
        return False
    
    # 读取当前配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复关键配置
    fixes = [
        ("USE_REAL_DATA=false", "USE_REAL_DATA=true"),
        ("USE_MOCK_DATA=false", "USE_MOCK_DATA=true"),  # 保持Mock作为fallback
        ("PREFERRED_DATA_SOURCE=tushare", "PREFERRED_DATA_SOURCE=akshare"),  # 暂时用AkShare，因为Tushare需要token
    ]
    
    modified = False
    for old, new in fixes:
        if old in content:
            content = content.replace(old, new)
            modified = True
            print(f"  ✅ 修复: {old} → {new}")
    
    # 如果没有USE_REAL_DATA配置，添加它
    if "USE_REAL_DATA=" not in content:
        content += "\n# 启用真实数据源\nUSE_REAL_DATA=true\n"
        modified = True
        print("  ✅ 添加: USE_REAL_DATA=true")
    
    if modified:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 后端环境配置已修复")
    else:
        print("ℹ️ 后端环境配置无需修复")
    
    return True

def fix_frontend_env():
    """修复前端.env配置"""
    print("\n🎨 修复前端环境配置...")
    
    frontend_env = Path("frontend/.env.development")
    
    # 确保前端环境配置正确
    env_content = """# 前端开发环境配置
VITE_APP_TITLE=量化投资平台
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/api/v1/ws
VITE_ENVIRONMENT=development

# API路径配置
VITE_MARKET_API_PREFIX=/market
VITE_ENHANCED_MARKET_API_PREFIX=/market/enhanced-market

# WebSocket主题配置
VITE_WS_TOPIC_MARKET_ALL=market:all
VITE_WS_TOPIC_MARKET_SYMBOL=market:{symbol}
VITE_WS_TOPIC_KLINE=market:{symbol}:kline:{interval}
"""
    
    with open(frontend_env, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ 前端环境配置已修复")
    return True

def create_api_path_reference():
    """创建API路径参考文档"""
    print("\n📋 创建API路径参考文档...")
    
    doc_content = """# 行情中心API路径参考

## 🎯 权威路径表（增强版为主）

### 增强版API（主要使用）
**前缀**: `/api/v1/market/enhanced-market`

| 功能 | 方法 | 路径 | 参数 |
|------|------|------|------|
| 股票详情 | GET | `/stock/{symbol}` | symbol: 股票代码 |
| K线数据 | GET | `/kline/{symbol}` | period, limit |
| 批量行情 | GET | `/quotes` | symbols: 逗号分隔 |
| 股票搜索 | GET | `/search` | query, limit |
| 自选股列表 | GET | `/watchlist` | - |
| 添加自选股 | POST | `/watchlist` | symbol |
| 移除自选股 | DELETE | `/watchlist/{symbol}` | symbol |
| 股票分析 | GET | `/analysis/{symbol}` | symbol, date |
| 健康检查 | GET | `/health` | - |

### 基础版API（fallback）
**前缀**: `/api/v1/market`

| 功能 | 方法 | 路径 | 参数 |
|------|------|------|------|
| 股票列表 | GET | `/stocks/list` | market, skip, limit |
| 实时行情 | GET | `/quotes/realtime` | symbols |
| 单股行情 | GET | `/quotes/{symbol}` | symbol |
| K线数据 | GET | `/kline/{symbol}` | period, start_date, end_date |
| 市场概览 | GET | `/overview` | - |
| 板块表现 | GET | `/sectors/performance` | - |
| 股票搜索 | GET | `/search` | keyword, limit |
| 市场深度 | GET | `/depth/{symbol}` | symbol |
| 逐笔数据 | GET | `/ticks/{symbol}` | limit |

## 🔌 WebSocket主题规范

### 订阅消息格式
```json
{
  "type": "subscribe",
  "data": {
    "topics": ["market:all", "market:000001", "market:000001:kline:1d"]
  }
}
```

### 推送消息格式
```json
{
  "type": "tick|kline",
  "symbol": "000001",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": { ... }
}
```

### 主题命名规范
- 全量行情: `market:all`
- 单股tick: `market:{symbol}`
- K线推送: `market:{symbol}:kline:{interval}`

## 🔄 数据源降级策略

1. **增强版API优先**: 使用真实数据源
2. **基础版API备选**: 增强版失败时降级
3. **Mock数据兜底**: 所有真实数据源不可用时

## 📝 前端适配说明

### API调用优先级
```javascript
// 1. 尝试增强版API
try {
  const response = await api.get('/market/enhanced-market/stock/000001')
  return response.data
} catch (error) {
  // 2. 降级到基础版API
  try {
    const response = await api.get('/market/quotes/000001')
    return response.data
  } catch (fallbackError) {
    // 3. 使用Mock数据
    return mockData
  }
}
```

### WebSocket连接
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8000/api/v1/ws')

// 订阅主题
ws.send(JSON.stringify({
  type: 'subscribe',
  data: { topics: ['market:all'] }
}))
```

## 🚀 部署检查清单

- [ ] 后端.env配置: `USE_REAL_DATA=true`
- [ ] 前端.env配置: API_BASE_URL正确
- [ ] WebSocket服务启动
- [ ] 数据源依赖安装: `pip install tushare akshare`
- [ ] API路径测试: 手动验证关键接口
- [ ] WebSocket测试: 验证订阅和推送
"""
    
    doc_path = Path("docs/api-path-reference.md")
    doc_path.parent.mkdir(exist_ok=True)
    
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("✅ API路径参考文档已创建: docs/api-path-reference.md")
    return True

def create_websocket_test_script():
    """创建WebSocket测试脚本"""
    print("\n🔌 创建WebSocket测试脚本...")
    
    test_script = """#!/usr/bin/env python3
\"\"\"
WebSocket连接测试脚本
验证WebSocket订阅和推送功能
\"\"\"

import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket():
    \"\"\"测试WebSocket连接\"\"\"
    uri = "ws://localhost:8000/api/v1/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info(f"✅ 连接成功: {uri}")
            
            # 发送订阅消息
            subscribe_msg = {
                "type": "subscribe",
                "data": {
                    "topics": ["market:all", "market:000001"]
                }
            }
            
            await websocket.send(json.dumps(subscribe_msg))
            logger.info(f"📤 发送订阅: {subscribe_msg}")
            
            # 监听消息
            timeout_count = 0
            while timeout_count < 10:  # 最多等待10次
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(message)
                    logger.info(f"📥 收到消息: {data}")
                    timeout_count = 0  # 重置计数器
                except asyncio.TimeoutError:
                    timeout_count += 1
                    logger.info(f"⏰ 等待消息... ({timeout_count}/10)")
                except Exception as e:
                    logger.error(f"❌ 消息处理错误: {e}")
                    break
            
            logger.info("🔚 测试完成")
            
    except Exception as e:
        logger.error(f"❌ WebSocket连接失败: {e}")
        logger.info("💡 请确保后端服务已启动: python -m uvicorn app.main:app --reload")

if __name__ == "__main__":
    asyncio.run(test_websocket())
"""
    
    script_path = Path("test_websocket.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ WebSocket测试脚本已创建: test_websocket.py")
    return True

def main():
    """主函数"""
    print("🚀 开始环境配置修复...\n")
    
    success_count = 0
    
    # 1. 修复后端环境
    if fix_backend_env():
        success_count += 1
    
    # 2. 修复前端环境
    if fix_frontend_env():
        success_count += 1
    
    # 3. 创建API参考文档
    if create_api_path_reference():
        success_count += 1
    
    # 4. 创建WebSocket测试脚本
    if create_websocket_test_script():
        success_count += 1
    
    print(f"\n📊 修复结果: {success_count}/4 完成")
    
    if success_count == 4:
        print("🎉 环境配置修复完成！")
        print("\n📋 下一步操作:")
        print("1. 重启后端服务: cd backend && python -m uvicorn app.main:app --reload")
        print("2. 重启前端服务: cd frontend && npm run dev")
        print("3. 测试WebSocket: python test_websocket.py")
        print("4. 查看API文档: docs/api-path-reference.md")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
