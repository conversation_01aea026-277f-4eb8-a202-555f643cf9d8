"""
增强的WebSocket服务
提供高效的实时数据推送、连接管理和错误处理
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Any, Callable
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from enum import Enum
import weakref

from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from app.core.enhanced_error_handler import <PERSON>rrorHandlerService, ErrorCode, create_success_response

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """消息类型枚举"""
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    TICK = "tick"
    KLINE = "kline"
    ORDER_BOOK = "orderbook"
    SYSTEM = "system"
    ERROR = "error"
    HEARTBEAT = "heartbeat"
    AUTHENTICATION = "auth"

@dataclass
class SubscriptionInfo:
    """订阅信息"""
    topic: str
    symbol: str
    client_id: str
    subscribe_time: datetime
    last_push_time: Optional[datetime] = None
    push_count: int = 0

@dataclass
class ClientInfo:
    """客户端信息"""
    client_id: str
    websocket: WebSocket
    connect_time: datetime
    last_heartbeat: datetime
    subscriptions: Set[str]
    is_authenticated: bool = False
    user_id: Optional[str] = None
    
    def __post_init__(self):
        if not self.subscriptions:
            self.subscriptions = set()

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 客户端连接管理
        self.clients: Dict[str, ClientInfo] = {}
        self.websocket_to_client: weakref.WeakKeyDictionary = weakref.WeakKeyDictionary()
        
        # 订阅管理
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)  # topic -> client_ids
        self.client_subscriptions: Dict[str, Dict[str, SubscriptionInfo]] = defaultdict(dict)  # client_id -> subscriptions
        
        # 数据缓存和去重
        self.data_cache: Dict[str, Any] = {}
        self.last_push_data: Dict[str, Any] = {}
        
        # 推送队列和批处理
        self.push_queue: deque = deque()
        self.batch_size = 100
        self.batch_interval = 0.1  # 100ms批处理间隔
        
        # 性能监控
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "total_messages_sent": 0,
            "total_messages_received": 0,
            "errors": 0,
            "last_reset_time": datetime.now()
        }
        
        # 心跳检查
        self.heartbeat_interval = 30  # 秒
        self.heartbeat_timeout = 60   # 秒
        
        # 启动后台任务
        self._background_tasks: List[asyncio.Task] = []
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台任务"""
        self._background_tasks = [
            asyncio.create_task(self._heartbeat_checker()),
            asyncio.create_task(self._batch_processor()),
            asyncio.create_task(self._stats_logger())
        ]
    
    async def connect(self, websocket: WebSocket, client_id: str) -> bool:
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            
            client_info = ClientInfo(
                client_id=client_id,
                websocket=websocket,
                connect_time=datetime.now(),
                last_heartbeat=datetime.now(),
                subscriptions=set()
            )
            
            self.clients[client_id] = client_info
            self.websocket_to_client[websocket] = client_id
            
            self.stats["total_connections"] += 1
            self.stats["active_connections"] = len(self.clients)
            
            logger.info(f"客户端 {client_id} 已连接，当前活跃连接数: {self.stats['active_connections']}")
            
            # 发送欢迎消息
            await self.send_system_message(client_id, "连接成功", {"client_id": client_id})
            
            return True
            
        except Exception as e:
            logger.error(f"建立连接失败: {e}")
            return False
    
    async def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        client_id = self.websocket_to_client.get(websocket)
        if not client_id:
            return
        
        # 清理订阅
        if client_id in self.client_subscriptions:
            for topic in list(self.client_subscriptions[client_id].keys()):
                self.subscriptions[topic].discard(client_id)
                if not self.subscriptions[topic]:
                    del self.subscriptions[topic]
            
            del self.client_subscriptions[client_id]
        
        # 清理客户端信息
        if client_id in self.clients:
            del self.clients[client_id]
        
        if websocket in self.websocket_to_client:
            del self.websocket_to_client[websocket]
        
        self.stats["active_connections"] = len(self.clients)
        logger.info(f"客户端 {client_id} 已断开，当前活跃连接数: {self.stats['active_connections']}")
    
    async def subscribe(self, client_id: str, topic: str, symbol: str) -> bool:
        """订阅主题"""
        if client_id not in self.clients:
            logger.warning(f"客户端 {client_id} 不存在")
            return False
        
        subscription_key = f"{topic}:{symbol}"
        
        # 添加订阅
        self.subscriptions[subscription_key].add(client_id)
        self.client_subscriptions[client_id][subscription_key] = SubscriptionInfo(
            topic=topic,
            symbol=symbol,
            client_id=client_id,
            subscribe_time=datetime.now()
        )
        
        logger.info(f"客户端 {client_id} 订阅了 {subscription_key}")
        
        # 发送确认消息
        await self.send_system_message(
            client_id, 
            f"订阅成功: {subscription_key}",
            {"topic": topic, "symbol": symbol}
        )
        
        # 如果有缓存数据，立即推送
        if subscription_key in self.data_cache:
            await self.send_to_client(client_id, MessageType.TICK, self.data_cache[subscription_key])
        
        return True
    
    async def unsubscribe(self, client_id: str, topic: str, symbol: str) -> bool:
        """取消订阅"""
        if client_id not in self.clients:
            return False
        
        subscription_key = f"{topic}:{symbol}"
        
        # 移除订阅
        self.subscriptions[subscription_key].discard(client_id)
        if not self.subscriptions[subscription_key]:
            del self.subscriptions[subscription_key]
        
        if subscription_key in self.client_subscriptions[client_id]:
            del self.client_subscriptions[client_id][subscription_key]
        
        logger.info(f"客户端 {client_id} 取消订阅 {subscription_key}")
        
        await self.send_system_message(
            client_id,
            f"取消订阅成功: {subscription_key}",
            {"topic": topic, "symbol": symbol}
        )
        
        return True
    
    async def broadcast_data(self, topic: str, symbol: str, data: Any):
        """广播数据给订阅者"""
        subscription_key = f"{topic}:{symbol}"
        
        if subscription_key not in self.subscriptions:
            return
        
        # 数据去重检查
        data_hash = hash(json.dumps(data, sort_keys=True))
        if self.last_push_data.get(subscription_key) == data_hash:
            return  # 数据未变化，跳过推送
        
        self.last_push_data[subscription_key] = data_hash
        self.data_cache[subscription_key] = data
        
        # 批量推送
        client_ids = list(self.subscriptions[subscription_key])
        for client_id in client_ids:
            self.push_queue.append({
                'client_id': client_id,
                'message_type': MessageType.TICK,
                'data': data
            })
    
    async def send_to_client(self, client_id: str, message_type: MessageType, data: Any):
        """发送消息给特定客户端"""
        if client_id not in self.clients:
            return False
        
        client = self.clients[client_id]
        
        try:
            if client.websocket.client_state != WebSocketState.CONNECTED:
                logger.warning(f"客户端 {client_id} 连接已断开")
                await self.disconnect(client.websocket)
                return False
            
            message = {
                "type": message_type.value,
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            
            await client.websocket.send_text(json.dumps(message, ensure_ascii=False))
            self.stats["total_messages_sent"] += 1
            
            return True
            
        except WebSocketDisconnect:
            logger.info(f"客户端 {client_id} 主动断开连接")
            await self.disconnect(client.websocket)
            return False
        except Exception as e:
            logger.error(f"发送消息给客户端 {client_id} 失败: {e}")
            self.stats["errors"] += 1
            return False
    
    async def send_system_message(self, client_id: str, message: str, details: Optional[Dict] = None):
        """发送系统消息"""
        data = {
            "message": message,
            "details": details or {}
        }
        await self.send_to_client(client_id, MessageType.SYSTEM, data)
    
    async def send_error_message(self, client_id: str, error: Exception):
        """发送错误消息"""
        error_response = ErrorHandlerService.handle_api_error(error)
        await self.send_to_client(client_id, MessageType.ERROR, error_response.dict())
    
    async def handle_client_message(self, websocket: WebSocket, message: str):
        """处理客户端消息"""
        client_id = self.websocket_to_client.get(websocket)
        if not client_id:
            return
        
        try:
            data = json.loads(message)
            message_type = data.get("type")
            payload = data.get("data", {})
            
            self.stats["total_messages_received"] += 1
            
            if message_type == MessageType.SUBSCRIBE.value:
                topic = payload.get("topic")
                symbol = payload.get("symbol")
                if topic and symbol:
                    await self.subscribe(client_id, topic, symbol)
            
            elif message_type == MessageType.UNSUBSCRIBE.value:
                topic = payload.get("topic")
                symbol = payload.get("symbol")
                if topic and symbol:
                    await self.unsubscribe(client_id, topic, symbol)
            
            elif message_type == MessageType.HEARTBEAT.value:
                self.clients[client_id].last_heartbeat = datetime.now()
                await self.send_to_client(client_id, MessageType.HEARTBEAT, {"status": "ok"})
            
            else:
                logger.warning(f"未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            await self.send_error_message(client_id, ValueError("无效的JSON格式"))
        except Exception as e:
            logger.error(f"处理客户端消息失败: {e}")
            await self.send_error_message(client_id, e)
    
    async def _heartbeat_checker(self):
        """心跳检查任务"""
        while True:
            try:
                now = datetime.now()
                timeout_clients = []
                
                for client_id, client in self.clients.items():
                    if (now - client.last_heartbeat).seconds > self.heartbeat_timeout:
                        timeout_clients.append(client)
                
                # 清理超时客户端
                for client in timeout_clients:
                    logger.info(f"客户端 {client.client_id} 心跳超时，断开连接")
                    await self.disconnect(client.websocket)
                
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"心跳检查异常: {e}")
                await asyncio.sleep(5)
    
    async def _batch_processor(self):
        """批处理推送任务"""
        while True:
            try:
                if len(self.push_queue) >= self.batch_size:
                    # 立即处理
                    await self._process_push_batch()
                else:
                    # 等待批处理间隔
                    await asyncio.sleep(self.batch_interval)
                    if self.push_queue:
                        await self._process_push_batch()
                        
            except Exception as e:
                logger.error(f"批处理异常: {e}")
                await asyncio.sleep(1)
    
    async def _process_push_batch(self):
        """处理推送批次"""
        if not self.push_queue:
            return
        
        # 取出一批消息
        batch = []
        batch_count = min(self.batch_size, len(self.push_queue))
        
        for _ in range(batch_count):
            if self.push_queue:
                batch.append(self.push_queue.popleft())
        
        # 并发发送
        tasks = [
            self.send_to_client(msg['client_id'], msg['message_type'], msg['data'])
            for msg in batch
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _stats_logger(self):
        """统计日志任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟记录一次
                
                logger.info(
                    f"WebSocket统计 - "
                    f"活跃连接: {self.stats['active_connections']}, "
                    f"总连接数: {self.stats['total_connections']}, "
                    f"发送消息: {self.stats['total_messages_sent']}, "
                    f"接收消息: {self.stats['total_messages_received']}, "
                    f"错误数: {self.stats['errors']}"
                )
                
            except Exception as e:
                logger.error(f"统计记录异常: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "subscriptions_count": len(self.subscriptions),
            "cache_size": len(self.data_cache),
            "queue_size": len(self.push_queue)
        }
    
    async def cleanup(self):
        """清理资源"""
        # 取消后台任务
        for task in self._background_tasks:
            if not task.done():
                task.cancel()
        
        # 关闭所有连接
        for client in list(self.clients.values()):
            try:
                await client.websocket.close()
            except:
                pass
        
        self.clients.clear()
        self.subscriptions.clear()
        self.client_subscriptions.clear()

# 全局连接管理器
connection_manager = ConnectionManager()

# WebSocket路由处理函数
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket端点处理函数"""
    if await connection_manager.connect(websocket, client_id):
        try:
            while True:
                message = await websocket.receive_text()
                await connection_manager.handle_client_message(websocket, message)
        except WebSocketDisconnect:
            pass
        except Exception as e:
            logger.error(f"WebSocket异常: {e}")
        finally:
            await connection_manager.disconnect(websocket)
    else:
        await websocket.close(code=1000, reason="连接失败")

# 便捷函数
async def broadcast_tick_data(symbol: str, tick_data: Dict[str, Any]):
    """广播tick数据"""
    await connection_manager.broadcast_data("tick", symbol, tick_data)

async def broadcast_kline_data(symbol: str, period: str, kline_data: Dict[str, Any]):
    """广播K线数据"""
    await connection_manager.broadcast_data("kline", f"{symbol}_{period}", kline_data)

def get_websocket_stats() -> Dict[str, Any]:
    """获取WebSocket统计信息"""
    return connection_manager.get_stats()

async def shutdown_websocket_service():
    """关闭WebSocket服务"""
    await connection_manager.cleanup()