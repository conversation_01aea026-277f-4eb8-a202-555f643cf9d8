"""
增强的错误处理和用户友好提示系统
提供标准化的错误响应格式和用户友好的错误信息
"""

import traceback
from typing import Dict, Any, Optional, Union
from enum import Enum
import logging
from datetime import datetime
from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class ErrorCode(Enum):
    """错误代码枚举"""
    # 系统级错误 (1000-1999)
    SYSTEM_ERROR = "1000"
    DATABASE_ERROR = "1001"
    NETWORK_ERROR = "1002"
    CONFIG_ERROR = "1003"
    SERVICE_UNAVAILABLE = "1004"
    
    # 数据源错误 (2000-2999)
    DATA_SOURCE_ERROR = "2000"
    DATA_SOURCE_UNAVAILABLE = "2001"
    DATA_SOURCE_TIMEOUT = "2002"
    DATA_SOURCE_AUTH_FAILED = "2003"
    DATA_SOURCE_RATE_LIMITED = "2004"
    DATA_QUALITY_ERROR = "2005"
    
    # 业务逻辑错误 (3000-3999)
    VALIDATION_ERROR = "3000"
    BUSINESS_RULE_VIOLATION = "3001"
    INSUFFICIENT_PERMISSIONS = "3002"
    RESOURCE_NOT_FOUND = "3003"
    DUPLICATE_RESOURCE = "3004"
    
    # 用户认证错误 (4000-4999)
    AUTH_ERROR = "4000"
    TOKEN_EXPIRED = "4001"
    INVALID_CREDENTIALS = "4002"
    ACCESS_DENIED = "4003"
    
    # API使用错误 (5000-5999)
    INVALID_REQUEST = "5000"
    MISSING_PARAMETER = "5001"
    INVALID_PARAMETER = "5002"
    API_RATE_LIMITED = "5003"

class ErrorResponse(BaseModel):
    """标准错误响应格式"""
    success: bool = False
    error_code: str
    error_message: str
    user_message: str
    timestamp: str
    request_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    suggestions: Optional[list] = None

class ErrorMessage:
    """错误信息映射"""
    
    # 用户友好的错误信息映射
    MESSAGES = {
        # 系统错误
        ErrorCode.SYSTEM_ERROR: {
            "tech": "系统内部错误",
            "user": "系统暂时出现问题，请稍后重试",
            "suggestions": ["请稍后重新尝试", "如果问题持续存在，请联系客服"]
        },
        ErrorCode.DATABASE_ERROR: {
            "tech": "数据库连接或查询错误",
            "user": "数据服务暂时不可用，请稍后重试",
            "suggestions": ["检查网络连接", "稍后重新尝试"]
        },
        ErrorCode.NETWORK_ERROR: {
            "tech": "网络连接错误",
            "user": "网络连接不稳定，请检查网络后重试",
            "suggestions": ["检查网络连接", "稍后重新尝试"]
        },
        
        # 数据源错误
        ErrorCode.DATA_SOURCE_UNAVAILABLE: {
            "tech": "数据源服务不可用",
            "user": "行情数据暂时无法获取，系统正在尝试使用备用数据源",
            "suggestions": ["系统将自动切换到备用数据源", "数据可能有轻微延迟"]
        },
        ErrorCode.DATA_SOURCE_TIMEOUT: {
            "tech": "数据源请求超时",
            "user": "获取数据超时，请稍后重试",
            "suggestions": ["请稍后重新尝试", "检查网络连接状态"]
        },
        ErrorCode.DATA_SOURCE_AUTH_FAILED: {
            "tech": "数据源认证失败",
            "user": "数据访问授权失败，请联系管理员",
            "suggestions": ["联系系统管理员检查数据源配置", "确认订阅服务是否有效"]
        },
        ErrorCode.DATA_SOURCE_RATE_LIMITED: {
            "tech": "数据源请求频率限制",
            "user": "请求过于频繁，请稍后重试",
            "suggestions": ["请等待1-2分钟后重试", "避免频繁刷新页面"]
        },
        ErrorCode.DATA_QUALITY_ERROR: {
            "tech": "数据质量异常",
            "user": "数据质量检查失败，已自动使用历史数据",
            "suggestions": ["当前显示的可能是历史数据", "稍后重试获取最新数据"]
        },
        
        # 业务错误
        ErrorCode.VALIDATION_ERROR: {
            "tech": "数据验证失败",
            "user": "输入的数据格式不正确，请检查后重试",
            "suggestions": ["检查输入数据的格式", "参考帮助文档中的示例"]
        },
        ErrorCode.RESOURCE_NOT_FOUND: {
            "tech": "请求的资源不存在",
            "user": "找不到请求的数据，请检查输入参数",
            "suggestions": ["检查股票代码是否正确", "确认查询的时间范围"]
        },
        
        # 认证错误
        ErrorCode.AUTH_ERROR: {
            "tech": "身份认证失败",
            "user": "身份验证失败，请重新登录",
            "suggestions": ["请重新登录", "检查账号密码是否正确"]
        },
        ErrorCode.TOKEN_EXPIRED: {
            "tech": "访问令牌已过期",
            "user": "登录状态已过期，请重新登录",
            "suggestions": ["请重新登录", "下次可以选择记住登录状态"]
        },
        
        # API错误
        ErrorCode.INVALID_REQUEST: {
            "tech": "无效的API请求",
            "user": "请求格式不正确",
            "suggestions": ["检查请求参数", "参考API文档"]
        },
        ErrorCode.API_RATE_LIMITED: {
            "tech": "API请求频率限制",
            "user": "请求过于频繁，请稍后重试",
            "suggestions": ["请等待片刻后重试", "避免过于频繁的操作"]
        }
    }
    
    @classmethod
    def get_message(cls, error_code: ErrorCode) -> Dict[str, Any]:
        """获取错误信息"""
        return cls.MESSAGES.get(error_code, {
            "tech": "未知错误",
            "user": "系统出现未知问题，请稍后重试",
            "suggestions": ["稍后重新尝试", "如果问题持续存在，请联系客服"]
        })

class ErrorHandlerService:
    """错误处理服务"""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        tech_message: Optional[str] = None,
        user_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> ErrorResponse:
        """创建标准错误响应"""
        
        message_info = ErrorMessage.get_message(error_code)
        
        return ErrorResponse(
            error_code=error_code.value,
            error_message=tech_message or message_info["tech"],
            user_message=user_message or message_info["user"],
            timestamp=datetime.now().isoformat(),
            request_id=request_id,
            details=details,
            suggestions=message_info.get("suggestions", [])
        )
    
    @staticmethod
    def handle_data_source_error(
        source_name: str,
        error: Exception,
        request_id: Optional[str] = None
    ) -> ErrorResponse:
        """处理数据源错误"""
        
        error_str = str(error).lower()
        
        # 根据错误类型选择合适的错误代码
        if "timeout" in error_str or "timed out" in error_str:
            error_code = ErrorCode.DATA_SOURCE_TIMEOUT
        elif "auth" in error_str or "token" in error_str or "permission" in error_str:
            error_code = ErrorCode.DATA_SOURCE_AUTH_FAILED
        elif "rate" in error_str or "limit" in error_str:
            error_code = ErrorCode.DATA_SOURCE_RATE_LIMITED
        elif "connection" in error_str or "network" in error_str:
            error_code = ErrorCode.NETWORK_ERROR
        else:
            error_code = ErrorCode.DATA_SOURCE_UNAVAILABLE
        
        details = {
            "source": source_name,
            "error_type": type(error).__name__,
            "original_error": str(error)
        }
        
        return ErrorHandlerService.create_error_response(
            error_code=error_code,
            details=details,
            request_id=request_id
        )
    
    @staticmethod
    def handle_api_error(
        error: Exception,
        request_id: Optional[str] = None
    ) -> ErrorResponse:
        """处理API错误"""
        
        if isinstance(error, HTTPException):
            if error.status_code == 401:
                error_code = ErrorCode.AUTH_ERROR
            elif error.status_code == 403:
                error_code = ErrorCode.ACCESS_DENIED
            elif error.status_code == 404:
                error_code = ErrorCode.RESOURCE_NOT_FOUND
            elif error.status_code == 422:
                error_code = ErrorCode.VALIDATION_ERROR
            elif error.status_code == 429:
                error_code = ErrorCode.API_RATE_LIMITED
            else:
                error_code = ErrorCode.SYSTEM_ERROR
        else:
            error_code = ErrorCode.SYSTEM_ERROR
        
        details = {
            "error_type": type(error).__name__,
            "original_error": str(error)
        }
        
        return ErrorHandlerService.create_error_response(
            error_code=error_code,
            details=details,
            request_id=request_id
        )

# 便捷函数
def create_data_source_error(
    source_name: str,
    error: Exception,
    request_id: Optional[str] = None
) -> JSONResponse:
    """创建数据源错误响应"""
    error_response = ErrorHandlerService.handle_data_source_error(source_name, error, request_id)
    
    return JSONResponse(
        status_code=503,  # Service Unavailable
        content=error_response.dict()
    )

def create_api_error(
    error: Exception,
    request_id: Optional[str] = None,
    status_code: int = 500
) -> JSONResponse:
    """创建API错误响应"""
    error_response = ErrorHandlerService.handle_api_error(error, request_id)
    
    return JSONResponse(
        status_code=status_code,
        content=error_response.dict()
    )

def create_success_response(
    data: Any,
    message: str = "操作成功",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """创建成功响应"""
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "request_id": request_id
    }