#!/usr/bin/env python3
"""
MCP 自动化质量检查机制
建立项目质量监控和自动化检查

检查项目：
1. 代码质量检查
2. 文档完整性检查
3. 配置一致性检查
4. 安全性检查
5. 项目结构检查
"""

import os
import re
import json
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime

class MCPQualityChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.checks_passed = []
        self.checks_failed = []
        self.warnings = []
        
    def log_pass(self, check: str, message: str):
        """记录通过的检查"""
        self.checks_passed.append({
            "check": check,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[PASS] {check}: {message}")
        
    def log_fail(self, check: str, message: str):
        """记录失败的检查"""
        self.checks_failed.append({
            "check": check,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[FAIL] {check}: {message}")
        
    def log_warning(self, check: str, message: str):
        """记录警告"""
        self.warnings.append({
            "check": check,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[WARN] {check}: {message}")

    def check_python_code_quality(self):
        """检查Python代码质量"""
        backend_dir = self.project_root / "backend"
        if not backend_dir.exists():
            self.log_fail("Python-Code-Quality", "Backend目录不存在")
            return
            
        # 检查Python文件数量
        python_files = list(backend_dir.glob("**/*.py"))
        if len(python_files) < 10:
            self.log_warning("Python-Code-Quality", f"Python文件数量较少: {len(python_files)}个")
        else:
            self.log_pass("Python-Code-Quality", f"发现{len(python_files)}个Python文件")
        
        # 检查是否有requirements.txt
        requirements_file = backend_dir / "requirements.txt"
        if requirements_file.exists():
            self.log_pass("Python-Dependencies", "requirements.txt存在")
            
            # 检查依赖版本是否锁定
            content = requirements_file.read_text(encoding='utf-8')
            unlocked_deps = []
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and '==' not in line and '>=' not in line:
                    unlocked_deps.append(line)
            
            if unlocked_deps:
                self.log_warning("Python-Dependencies", f"发现未锁定版本的依赖: {', '.join(unlocked_deps[:5])}")
            else:
                self.log_pass("Python-Dependencies", "所有依赖版本已锁定")
        else:
            self.log_fail("Python-Dependencies", "缺少requirements.txt文件")
        
        # 检查主要模块结构
        expected_dirs = ["app/api", "app/core", "app/services", "app/models"]
        for dir_path in expected_dirs:
            full_path = backend_dir / dir_path
            if full_path.exists():
                py_files = list(full_path.glob("*.py"))
                self.log_pass("Python-Structure", f"{dir_path} 目录存在，包含{len(py_files)}个文件")
            else:
                self.log_warning("Python-Structure", f"缺少{dir_path}目录")

    def check_frontend_code_quality(self):
        """检查前端代码质量"""
        frontend_dir = self.project_root / "frontend"
        if not frontend_dir.exists():
            self.log_fail("Frontend-Code-Quality", "Frontend目录不存在")
            return
            
        # 检查package.json
        package_json = frontend_dir / "package.json"
        if package_json.exists():
            self.log_pass("Frontend-Package", "package.json存在")
            try:
                with open(package_json, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                
                # 检查必要的脚本
                required_scripts = ["dev", "build", "test", "lint"]
                scripts = package_data.get("scripts", {})
                missing_scripts = [s for s in required_scripts if s not in scripts]
                
                if missing_scripts:
                    self.log_warning("Frontend-Scripts", f"缺少脚本: {', '.join(missing_scripts)}")
                else:
                    self.log_pass("Frontend-Scripts", "包含所有必要脚本")
                    
                # 检查TypeScript配置
                if "typescript" in package_data.get("devDependencies", {}):
                    self.log_pass("Frontend-TypeScript", "已配置TypeScript")
                else:
                    self.log_warning("Frontend-TypeScript", "未配置TypeScript")
                    
            except Exception as e:
                self.log_fail("Frontend-Package", f"package.json格式错误: {e}")
        else:
            self.log_fail("Frontend-Package", "缺少package.json文件")
        
        # 检查Vue文件
        vue_files = list(frontend_dir.glob("**/*.vue"))
        if len(vue_files) < 5:
            self.log_warning("Frontend-Components", f"Vue组件数量较少: {len(vue_files)}个")
        else:
            self.log_pass("Frontend-Components", f"发现{len(vue_files)}个Vue组件")
        
        # 检查前端目录结构
        expected_dirs = ["src/api", "src/components", "src/views", "src/stores"]
        for dir_path in expected_dirs:
            full_path = frontend_dir / dir_path
            if full_path.exists():
                files = list(full_path.glob("*"))
                self.log_pass("Frontend-Structure", f"{dir_path} 目录存在，包含{len(files)}个文件")
            else:
                self.log_warning("Frontend-Structure", f"缺少{dir_path}目录")

    def check_documentation_completeness(self):
        """检查文档完整性"""
        docs_dir = self.project_root / "docs"
        
        # 检查根目录README
        readme_files = list(self.project_root.glob("README*.md"))
        if readme_files:
            self.log_pass("Documentation-README", f"发现{len(readme_files)}个README文件")
        else:
            self.log_fail("Documentation-README", "缺少README.md文件")
        
        # 检查docs目录
        if docs_dir.exists():
            doc_files = list(docs_dir.glob("**/*.md"))
            if len(doc_files) >= 10:
                self.log_pass("Documentation-Coverage", f"文档较为完整，包含{len(doc_files)}个文档")
            elif len(doc_files) >= 5:
                self.log_warning("Documentation-Coverage", f"文档基本完整，包含{len(doc_files)}个文档")
            else:
                self.log_fail("Documentation-Coverage", f"文档不足，仅有{len(doc_files)}个文档")
        else:
            self.log_fail("Documentation-Structure", "缺少docs目录")
        
        # 检查API文档
        api_docs = list(self.project_root.glob("**/API*.md")) + list(self.project_root.glob("**/api*.md"))
        if api_docs:
            self.log_pass("Documentation-API", f"发现{len(api_docs)}个API文档")
        else:
            self.log_warning("Documentation-API", "缺少API文档")

    def check_configuration_consistency(self):
        """检查配置一致性"""
        # 检查gitignore
        gitignore = self.project_root / ".gitignore"
        if gitignore.exists():
            content = gitignore.read_text(encoding='utf-8')
            required_patterns = [
                "*.pyc", "__pycache__", "node_modules", "dist", ".env"
            ]
            missing_patterns = [p for p in required_patterns if p not in content]
            
            if missing_patterns:
                self.log_warning("Config-Gitignore", f"gitignore缺少模式: {', '.join(missing_patterns)}")
            else:
                self.log_pass("Config-Gitignore", "gitignore配置完整")
        else:
            self.log_fail("Config-Gitignore", "缺少.gitignore文件")
        
        # 检查环境配置模板
        config_dir = self.project_root / "config" / "environment"
        if config_dir.exists():
            template_files = list(config_dir.glob("*.template"))
            if len(template_files) >= 3:
                self.log_pass("Config-Templates", f"环境配置模板完整，包含{len(template_files)}个模板")
            else:
                self.log_warning("Config-Templates", f"环境配置模板不足，仅有{len(template_files)}个模板")
        else:
            self.log_warning("Config-Templates", "缺少环境配置模板目录")
        
        # 检查Docker配置
        docker_files = list(self.project_root.glob("**/Dockerfile*")) + list(self.project_root.glob("**/docker-compose*.yml"))
        if docker_files:
            self.log_pass("Config-Docker", f"发现{len(docker_files)}个Docker配置文件")
        else:
            self.log_warning("Config-Docker", "缺少Docker配置文件")

    def check_security_basics(self):
        """检查基本安全性"""
        # 检查是否有敏感文件被提交
        sensitive_patterns = [
            "*.key", "*.pem", "*.p12", ".env", "secret*", "*password*"
        ]
        
        found_sensitive = []
        for pattern in sensitive_patterns:
            files = list(self.project_root.glob(f"**/{pattern}"))
            # 排除模板文件
            files = [f for f in files if not f.name.endswith('.template') and not f.name.endswith('.example')]
            found_sensitive.extend(files)
        
        if found_sensitive:
            self.log_fail("Security-Sensitive", f"发现可能的敏感文件: {[f.name for f in found_sensitive[:5]]}")
        else:
            self.log_pass("Security-Sensitive", "未发现敏感文件")
        
        # 检查requirements.txt中的安全依赖
        requirements_file = self.project_root / "backend" / "requirements.txt"
        if requirements_file.exists():
            content = requirements_file.read_text(encoding='utf-8').lower()
            security_packages = ["cryptography", "passlib", "python-jose"]
            found_security = [pkg for pkg in security_packages if pkg in content]
            
            if found_security:
                self.log_pass("Security-Dependencies", f"发现安全相关依赖: {', '.join(found_security)}")
            else:
                self.log_warning("Security-Dependencies", "缺少安全相关依赖")

    def check_project_structure(self):
        """检查项目结构"""
        expected_structure = {
            "backend": "后端代码目录",
            "frontend": "前端代码目录", 
            "docs": "文档目录",
            "scripts": "脚本目录"
        }
        
        for dir_name, description in expected_structure.items():
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                files_count = len(list(dir_path.glob("**/*")))
                self.log_pass("Project-Structure", f"{description}存在，包含{files_count}个文件")
            else:
                self.log_warning("Project-Structure", f"缺少{description}: {dir_name}")
        
        # 检查空目录
        empty_dirs = []
        for dir_path in self.project_root.rglob("*"):
            if (dir_path.is_dir() and 
                not any(dir_path.iterdir()) and 
                dir_path.name not in ['.git', '__pycache__', 'node_modules']):
                empty_dirs.append(dir_path)
        
        if empty_dirs:
            self.log_warning("Project-Structure", f"发现{len(empty_dirs)}个空目录")
        else:
            self.log_pass("Project-Structure", "无空目录")

    def create_quality_report(self):
        """创建质量检查报告"""
        report_content = f"""# 项目质量检查报告

## 检查概述
- **检查时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **通过检查**: {len(self.checks_passed)}项
- **失败检查**: {len(self.checks_failed)}项  
- **警告项目**: {len(self.warnings)}项

## 质量评分
"""
        
        total_checks = len(self.checks_passed) + len(self.checks_failed) + len(self.warnings)
        if total_checks > 0:
            score = (len(self.checks_passed) + len(self.warnings) * 0.5) / total_checks * 100
            report_content += f"**总体评分**: {score:.1f}/100\n\n"
            
            if score >= 90:
                report_content += "**质量等级**: 优秀 ⭐⭐⭐⭐⭐\n\n"
            elif score >= 80:
                report_content += "**质量等级**: 良好 ⭐⭐⭐⭐\n\n"
            elif score >= 70:
                report_content += "**质量等级**: 一般 ⭐⭐⭐\n\n"
            elif score >= 60:
                report_content += "**质量等级**: 需改进 ⭐⭐\n\n"
            else:
                report_content += "**质量等级**: 较差 ⭐\n\n"
        
        # 通过的检查
        if self.checks_passed:
            report_content += "## ✅ 通过的检查\n\n"
            for check in self.checks_passed:
                report_content += f"- **{check['check']}**: {check['message']}\n"
            report_content += "\n"
        
        # 失败的检查
        if self.checks_failed:
            report_content += "## ❌ 失败的检查\n\n"
            for check in self.checks_failed:
                report_content += f"- **{check['check']}**: {check['message']}\n"
            report_content += "\n"
        
        # 警告项目
        if self.warnings:
            report_content += "## ⚠️ 警告项目\n\n"
            for warning in self.warnings:
                report_content += f"- **{warning['check']}**: {warning['message']}\n"
            report_content += "\n"
        
        report_content += """## 改进建议

### 立即修复（高优先级）
"""
        for check in self.checks_failed:
            report_content += f"- 修复 {check['check']}: {check['message']}\n"
        
        report_content += """
### 优化改进（中优先级）
"""
        for warning in self.warnings:
            report_content += f"- 改进 {warning['check']}: {warning['message']}\n"
        
        report_content += """
### 下次检查
建议每周运行一次质量检查，确保项目质量持续改进。

```bash
# 运行质量检查
python scripts/mcp_quality_checker.py

# 查看详细报告
cat reports/quality_check_report.md
```
"""
        
        # 保存报告
        report_file = self.project_root / "reports" / "quality_check_report.md"
        report_file.parent.mkdir(exist_ok=True)
        
        try:
            report_file.write_text(report_content, encoding='utf-8')
            self.log_pass("Quality-Report", f"生成质量检查报告: {report_file}")
        except Exception as e:
            self.log_fail("Quality-Report", f"生成报告失败: {e}")

    def run_all_checks(self):
        """运行所有质量检查"""
        print("开始MCP自动化质量检查...")
        print("=" * 50)
        
        # 执行所有检查
        self.check_python_code_quality()
        self.check_frontend_code_quality()
        self.check_documentation_completeness()
        self.check_configuration_consistency()
        self.check_security_basics()
        self.check_project_structure()
        
        # 生成报告
        self.create_quality_report()
        self.generate_summary()

    def generate_summary(self):
        """生成检查摘要"""
        total_checks = len(self.checks_passed) + len(self.checks_failed) + len(self.warnings)
        
        print("\n" + "=" * 50)
        print("MCP质量检查摘要")
        print("=" * 50)
        print(f"总检查项目: {total_checks}")
        print(f"通过检查: {len(self.checks_passed)}")
        print(f"失败检查: {len(self.checks_failed)}")
        print(f"警告项目: {len(self.warnings)}")
        
        if total_checks > 0:
            score = (len(self.checks_passed) + len(self.warnings) * 0.5) / total_checks * 100
            print(f"质量评分: {score:.1f}/100")
        
        # 保存JSON报告
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_checks": total_checks,
                "passed": len(self.checks_passed),
                "failed": len(self.checks_failed),
                "warnings": len(self.warnings),
                "score": score if total_checks > 0 else 0
            },
            "checks_passed": self.checks_passed,
            "checks_failed": self.checks_failed,
            "warnings": self.warnings
        }
        
        json_report_file = self.project_root / "reports" / "mcp_quality_check_report.json"
        try:
            with open(json_report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            print(f"\nJSON报告: {json_report_file}")
        except Exception as e:
            print(f"保存JSON报告失败: {e}")

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    checker = MCPQualityChecker(project_root)
    checker.run_all_checks()

if __name__ == "__main__":
    main()