# 服务层重构方案

## 🎯 重构目标
解决后端服务层60+文件的命名混乱和职责重叠问题，建立清晰的分层架构。

## 📊 现状分析

### 问题文件统计
- **总服务文件数**: 67个
- **命名冲突模式**: service/source/enhanced/integrated
- **重复功能**: 多个文件实现相似功能

### 主要问题
1. **命名模式混乱**:
   - `market_service.py` vs `market_data_service.py` vs `enhanced_market_service.py`
   - `real_data_source.py` vs `real_data_sources.py`
   - `trading_service.py` vs `trading_service_impl.py`

2. **功能重复**:
   - 5个市场数据相关服务
   - 4个回测引擎实现
   - 3个认证服务变体

## 🏗️ 新架构设计

### 分层原则
```
backend/app/services/
├── core/           # 核心基础服务
├── adapters/       # 外部数据源适配器
├── engines/        # 业务引擎
├── handlers/       # 请求处理器
└── utilities/      # 工具服务
```

### 重构映射表
```
原文件 → 新位置

# 核心服务层
auth_service.py → core/auth_service.py
user_service.py → core/user_service.py
monitoring_service.py → core/monitoring_service.py

# 数据适配器层
akshare_data_source.py → adapters/akshare_adapter.py
tushare_service.py → adapters/tushare_adapter.py
real_data_source.py → adapters/market_data_adapter.py

# 业务引擎层
backtest_engine.py → engines/backtest_engine.py
strategy_execution_engine.py → engines/strategy_engine.py
simulated_trading_engine.py → engines/trading_engine.py

# 删除重复文件
enhanced_auth_service.py → DELETE (功能合并到core/auth_service.py)
integrated_market_service.py → DELETE (功能合并到core/market_service.py)
```

## 📋 重构执行计划

### Phase 1: 创建新目录结构
1. 创建分层目录
2. 移动核心服务文件
3. 更新导入路径

### Phase 2: 合并重复功能
1. 分析重复代码
2. 合并到主文件
3. 删除冗余文件

### Phase 3: 更新引用
1. 更新API路由导入
2. 更新测试文件
3. 更新文档

## ⚠️ 风险控制
- 渐进式重构，避免破坏现有功能
- 每步都进行测试验证
- 保留备份文件直到验证完成