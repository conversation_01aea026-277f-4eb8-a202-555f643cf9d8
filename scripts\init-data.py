#!/usr/bin/env python3
"""
数据初始化脚本
创建示例股票数据和索引，解决股票列表空数据问题
"""

import os
import sys
import pandas as pd
import sqlite3
import json
from pathlib import Path
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

def create_sample_stock_data():
    """创建示例股票数据"""
    
    # 示例股票列表
    sample_stocks = [
        {"symbol": "000001", "name": "平安银行", "market": "SZ"},
        {"symbol": "000002", "name": "万科A", "market": "SZ"},
        {"symbol": "000858", "name": "五粮液", "market": "SZ"},
        {"symbol": "600000", "name": "浦发银行", "market": "SH"},
        {"symbol": "600036", "name": "招商银行", "market": "SH"},
        {"symbol": "600519", "name": "贵州茅台", "market": "SH"},
        {"symbol": "600887", "name": "伊利股份", "market": "SH"},
        {"symbol": "000858", "name": "五粮液", "market": "SZ"},
        {"symbol": "002415", "name": "海康威视", "market": "SZ"},
        {"symbol": "300059", "name": "东方财富", "market": "SZ"},
    ]
    
    # 创建数据目录
    data_dir = project_root / "data" / "historical" / "stocks"
    data_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 创建数据目录: {data_dir}")
    
    # 为每只股票生成示例数据
    for stock in sample_stocks:
        symbol = stock["symbol"]
        name = stock["name"]
        
        # 生成过去一年的日K线数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        # 过滤掉周末
        dates = [d for d in dates if d.weekday() < 5]
        
        # 生成模拟价格数据
        base_price = random.uniform(10, 100)
        data = []
        
        for i, date in enumerate(dates):
            # 简单的随机游走模型
            if i == 0:
                open_price = base_price
            else:
                open_price = data[-1]['close']
            
            # 生成当日价格
            change_pct = random.uniform(-0.05, 0.05)  # ±5%变化
            close_price = open_price * (1 + change_pct)
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.03)
            low_price = min(open_price, close_price) * random.uniform(0.97, 1.0)
            volume = random.randint(1000000, 50000000)
            
            data.append({
                'date': date.strftime('%Y-%m-%d'),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(volume * close_price, 2)
            })
        
        # 保存为CSV文件
        df = pd.DataFrame(data)
        file_path = data_dir / f"{symbol}.csv"
        df.to_csv(file_path, index=False, encoding='utf-8')
        
        print(f"✅ 创建股票数据: {symbol} ({name}) - {len(data)} 条记录")
    
    return sample_stocks, data_dir

def create_stock_index(sample_stocks, data_dir):
    """创建股票索引数据库"""
    
    index_dir = project_root / "data" / "index"
    index_dir.mkdir(parents=True, exist_ok=True)
    
    index_file = index_dir / "stock_index.db"
    
    # 创建SQLite索引数据库
    conn = sqlite3.connect(str(index_file))
    cursor = conn.cursor()
    
    # 创建索引表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_index (
            symbol TEXT PRIMARY KEY,
            name TEXT,
            market TEXT,
            file_path TEXT,
            file_size INTEGER,
            last_updated TEXT,
            status TEXT DEFAULT 'active'
        )
    ''')
    
    # 插入股票数据
    for stock in sample_stocks:
        symbol = stock["symbol"]
        name = stock["name"]
        market = stock["market"]
        
        file_path = data_dir / f"{symbol}.csv"
        file_size = file_path.stat().st_size if file_path.exists() else 0
        
        cursor.execute('''
            INSERT OR REPLACE INTO stock_index 
            (symbol, name, market, file_path, file_size, last_updated, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            symbol, name, market, str(file_path), 
            file_size, datetime.now().isoformat(), 'active'
        ))
    
    conn.commit()
    conn.close()
    
    print(f"✅ 创建股票索引: {index_file}")
    return index_file

def create_metadata(sample_stocks):
    """创建元数据文件"""
    
    metadata_dir = project_root / "data" / "index"
    metadata_file = metadata_dir / "metadata.json"
    
    metadata = {
        "total_stocks": len(sample_stocks),
        "markets": {
            "SH": len([s for s in sample_stocks if s["market"] == "SH"]),
            "SZ": len([s for s in sample_stocks if s["market"] == "SZ"]),
            "BJ": 0
        },
        "industries": {
            "银行": 3,
            "房地产": 1,
            "食品饮料": 2,
            "电子": 1,
            "计算机": 1,
            "医药生物": 1,
            "其他": 1
        },
        "data_range": {
            "start_date": (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'),
            "end_date": datetime.now().strftime('%Y-%m-%d')
        },
        "total_records": len(sample_stocks) * 250,  # 估算记录数
        "last_updated": datetime.now().isoformat()
    }
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建元数据: {metadata_file}")
    return metadata_file

def main():
    """主函数"""
    print("🚀 开始初始化示例数据...")
    
    try:
        # 1. 创建示例股票数据
        sample_stocks, data_dir = create_sample_stock_data()
        
        # 2. 创建股票索引
        index_file = create_stock_index(sample_stocks, data_dir)
        
        # 3. 创建元数据
        metadata_file = create_metadata(sample_stocks)
        
        print("\n✅ 数据初始化完成!")
        print(f"   - 创建了 {len(sample_stocks)} 只股票的示例数据")
        print(f"   - 数据目录: {data_dir}")
        print(f"   - 索引文件: {index_file}")
        print(f"   - 元数据文件: {metadata_file}")
        
        print("\n💡 现在可以重启后端服务，股票列表应该有数据了")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
