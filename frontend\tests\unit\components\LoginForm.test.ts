/**
 * 登录表单组件测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElForm, ElFormItem, ElInput, ElButton } from 'element-plus'
import LoginForm from '@/components/auth/LoginForm.vue'

// Mock API
vi.mock('@/api/auth', () => ({
  login: vi.fn()
}))

describe('LoginForm', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(LoginForm, {
      global: {
        components: {
          ElForm,
          ElFormItem,
          ElInput,
          ElButton
        }
      }
    })
  })

  it('应该正确渲染登录表单', () => {
    expect(wrapper.find('form').exists()).toBe(true)
    expect(wrapper.find('input[type="text"]').exists()).toBe(true)
    expect(wrapper.find('input[type="password"]').exists()).toBe(true)
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
  })

  it('应该验证必填字段', async () => {
    const submitButton = wrapper.find('button[type="submit"]')
    await submitButton.trigger('click')

    // 检查表单验证
    expect(wrapper.vm.formRef.validate).toHaveBeenCalled()
  })

  it('应该在输入有效数据时启用提交按钮', async () => {
    const usernameInput = wrapper.find('input[type="text"]')
    const passwordInput = wrapper.find('input[type="password"]')

    await usernameInput.setValue('testuser')
    await passwordInput.setValue('password123')

    const submitButton = wrapper.find('button[type="submit"]')
    expect(submitButton.attributes('disabled')).toBeUndefined()
  })

  it('应该显示加载状态', async () => {
    await wrapper.setData({ loading: true })
    
    const submitButton = wrapper.find('button[type="submit"]')
    expect(submitButton.attributes('loading')).toBeDefined()
  })

  it('应该处理登录成功', async () => {
    const { login } = await import('@/api/auth')
    vi.mocked(login).mockResolvedValue({
      access_token: 'test_token',
      token_type: 'bearer'
    })

    const usernameInput = wrapper.find('input[type="text"]')
    const passwordInput = wrapper.find('input[type="password"]')

    await usernameInput.setValue('testuser')
    await passwordInput.setValue('password123')

    await wrapper.find('form').trigger('submit.prevent')

    expect(login).toHaveBeenCalledWith({
      username: 'testuser',
      password: 'password123'
    })
  })

  it('应该处理登录错误', async () => {
    const { login } = await import('@/api/auth')
    vi.mocked(login).mockRejectedValue(new Error('Invalid credentials'))

    const usernameInput = wrapper.find('input[type="text"]')
    const passwordInput = wrapper.find('input[type="password"]')

    await usernameInput.setValue('testuser')
    await passwordInput.setValue('wrongpassword')

    await wrapper.find('form').trigger('submit.prevent')

    expect(wrapper.vm.error).toBeTruthy()
  })

  it('应该显示演示登录选项', () => {
    const demoButton = wrapper.find('[data-test="demo-login"]')
    expect(demoButton.exists()).toBe(true)
  })

  it('应该处理演示登录', async () => {
    const demoButton = wrapper.find('[data-test="demo-login"]')
    await demoButton.trigger('click')

    expect(wrapper.vm.form.username).toBe('demo')
    expect(wrapper.vm.form.password).toBe('demo')
  })

  it('应该显示忘记密码链接', () => {
    const forgotLink = wrapper.find('[data-test="forgot-password"]')
    expect(forgotLink.exists()).toBe(true)
  })

  it('应该验证用户名格式', async () => {
    const usernameInput = wrapper.find('input[type="text"]')
    await usernameInput.setValue('')
    await usernameInput.trigger('blur')

    expect(wrapper.vm.errors.username).toBeTruthy()
  })

  it('应该验证密码长度', async () => {
    const passwordInput = wrapper.find('input[type="password"]')
    await passwordInput.setValue('123')
    await passwordInput.trigger('blur')

    expect(wrapper.vm.errors.password).toBeTruthy()
  })
})