# 包管理器使用指南

## 当前状态
项目统一使用 **pnpm** 作为前端包管理器

## 为什么选择pnpm？
1. **性能优势**: 安装速度比npm快2-3倍
2. **磁盘效率**: 通过硬链接节省磁盘空间
3. **严格依赖**: 避免幽灵依赖问题
4. **Monorepo支持**: 更好的工作空间支持

## 使用指南

### 安装pnpm
```bash
# 通过npm安装
npm install -g pnpm

# 或使用winget (Windows)
winget install pnpm

# 验证安装
pnpm --version
```

### 项目命令
```bash
# 安装依赖
cd frontend && pnpm install

# 开发模式
pnpm dev

# 构建项目
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint
```

### 禁用其他包管理器
项目已配置为仅使用pnpm：
- `.gitignore` 忽略 `package-lock.json` 和 `yarn.lock`
- `package.json` 中配置了engines限制

### 故障排除
如果遇到依赖问题：
```bash
# 清理缓存
pnpm store prune

# 重新安装
rm -rf node_modules
pnpm install

# 更新依赖
pnpm update
```
