# 行情中心快速配置指南

## 🎯 目标

将行情中心从Demo级别提升到生产级别，激活真实数据源，实现完整的实时行情功能。

## 📋 当前状态

- ✅ 前端UI完整 (95%完成度)
- ✅ 后端API框架完整 (92%完成度)  
- ✅ WebSocket实时推送框架完整 (85%完成度)
- ❌ 真实数据源未激活 (主要使用Mock数据)
- ❌ 部分API路径不一致导致404错误

## 🚀 一键配置 (推荐)

### Windows用户
```bash
scripts\install-dependencies.bat
```

### Linux/macOS用户
```bash
chmod +x scripts/install-dependencies.sh
./scripts/install-dependencies.sh
```

## 📝 手动配置步骤

### 1. 环境配置

#### 后端配置 (backend/.env)
```bash
# 数据源配置 - 关键配置项
USE_REAL_DATA=true
TUSHARE_TOKEN=your_actual_token_here  # 🔑 需要申请
AKSHARE_ENABLED=true
MARKET_DATA_CACHE_TTL=300

# Redis缓存 (推荐启用)
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=300
```

#### 前端配置 (frontend/.env.development)
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/api/v1/ws
VITE_USE_MOCK=false
VITE_USE_ENHANCED_API=true
```

### 2. 依赖安装

#### 后端依赖
```bash
cd backend
pip install tushare akshare pandas-ta redis aioredis
```

#### 前端依赖 (已完整)
```bash
cd frontend
pnpm install
```

### 3. 获取Tushare Token

1. 访问 [Tushare官网](https://tushare.pro/)
2. 注册账号并实名认证
3. 获取API Token
4. 将Token配置到 `backend/.env` 文件中

### 4. 启动服务

#### 启动Redis (可选但推荐)
```bash
# Windows (需要先安装Redis)
redis-server

# Linux/macOS
redis-server --port 6379

# Docker方式
docker run -d -p 6379:6379 redis
```

#### 启动后端
```bash
cd backend
python main.py
```

#### 启动前端
```bash
cd frontend
pnpm dev
```

## ✅ 验证安装

### 1. 快速验证
- 访问 http://localhost:8000/docs (后端API文档)
- 访问 http://localhost:5173 (前端页面)
- 检查前端控制台无404错误
- 确认显示真实行情数据 (非Mock)

### 2. 详细验证
参考 [验收检查清单](docs/market-verification-checklist.md) 进行完整验证。

## 🔧 常见问题解决

### 问题1: 前端显示Mock数据
**原因**: Tushare Token未配置或无效
**解决**: 
1. 检查 `backend/.env` 中的 `TUSHARE_TOKEN`
2. 确认Token有效性
3. 重启后端服务

### 问题2: 前端控制台404错误
**原因**: API路径不一致
**解决**: 
1. 确认 `VITE_USE_ENHANCED_API=true`
2. 检查后端服务是否正常启动
3. 查看后端日志确认路由注册

### 问题3: WebSocket连接失败
**原因**: WebSocket服务未启动或端口冲突
**解决**:
1. 检查后端日志中的WebSocket启动信息
2. 确认端口8000未被占用
3. 检查防火墙设置

### 问题4: 数据更新不及时
**原因**: 缓存配置或数据源限流
**解决**:
1. 启用Redis缓存
2. 调整 `MARKET_DATA_CACHE_TTL` 参数
3. 检查Tushare API调用频率限制

## 📊 数据源说明

### 优先级顺序
1. **Tushare** (主数据源)
   - 高质量、低延迟
   - 需要付费token
   - 限流：200次/分钟

2. **AkShare** (备用数据源)
   - 免费使用
   - 可能存在延迟
   - 稳定性一般

3. **Mock** (兜底数据源)
   - 模拟数据
   - 保证服务可用
   - 仅用于开发/演示

### 数据源切换
系统会自动根据配置和可用性进行数据源切换，无需手动干预。

## 🎯 预期效果

配置完成后，你将获得：

- ✅ 真实的股票行情数据
- ✅ 实时的价格更新 (WebSocket推送)
- ✅ 完整的K线历史数据
- ✅ 准确的市场指数和板块数据
- ✅ 高性能的缓存机制
- ✅ 稳定的服务可用性

## 📖 相关文档

- [API规范文档](docs/market-api-spec.md) - 完整的接口说明
- [验收检查清单](docs/market-verification-checklist.md) - 详细的验证步骤
- [架构设计文档](docs/architecture.md) - 系统架构说明

## 🆘 技术支持

如果遇到问题，请：

1. 查看相关日志文件
2. 参考常见问题解决方案
3. 检查环境配置是否正确
4. 确认依赖是否完整安装

## 📈 性能优化建议

1. **启用Redis缓存** - 显著提升响应速度
2. **配置CDN** - 加速静态资源加载
3. **调整缓存TTL** - 平衡数据实时性和性能
4. **监控API调用频率** - 避免触发限流

---

**预计配置时间**: 15-30分钟  
**技术难度**: 初级  
**完成后效果**: 生产级行情中心
