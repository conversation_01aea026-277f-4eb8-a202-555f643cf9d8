# 行情中心API规范 v1.0

## 概述

本文档定义了量化投资平台行情中心的完整API规范，包括REST接口、WebSocket实时推送、数据结构和错误处理机制。

## 数据源优先级

系统采用多数据源降级机制，确保服务可用性：

1. **Tushare** (主数据源，需TOKEN)
   - 高质量、低延迟
   - 需要付费token
   - 限流：200次/分钟

2. **AkShare** (备用数据源，免费)
   - 免费使用
   - 可能存在延迟
   - 稳定性一般

3. **Mock** (兜底数据源)
   - 模拟数据
   - 保证服务可用
   - 仅用于开发/演示

## 环境配置

### 后端配置 (.env)
```bash
# 数据源配置
USE_REAL_DATA=true
TUSHARE_TOKEN=your_actual_token_here
AKSHARE_ENABLED=true
MARKET_DATA_CACHE_TTL=300

# Redis缓存
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=300
```

### 前端配置 (.env.development)
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/api/v1/ws
VITE_USE_MOCK=false
VITE_USE_ENHANCED_API=true
```

## REST接口规范

### 基础路径
- 基础版API: `/api/v1/market/`
- 增强版API: `/api/v1/market/enhanced-market/`

**推荐使用增强版API作为主要数据源**

### 1. 股票列表

#### 获取股票列表
```http
GET /api/v1/market/stocks/list
```

**查询参数:**
- `market`: 市场代码 (SH/SZ/BJ)
- `industry`: 行业代码
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (默认20)

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "symbol": "000001",
        "name": "平安银行",
        "market": "SZ",
        "industry": "银行",
        "current_price": 12.50,
        "change": 0.15,
        "change_percent": 1.22,
        "volume": 1234567,
        "turnover": 15432100.50
      }
    ],
    "total": 4500,
    "page": 1,
    "page_size": 20
  }
}
```

### 2. 实时行情

#### 单只股票行情
```http
GET /api/v1/market/quotes/{symbol}
```

#### 批量股票行情
```http
GET /api/v1/market/quotes/realtime?symbols=000001,000002,600036
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "symbol": "000001",
      "name": "平安银行",
      "current_price": 12.50,
      "open_price": 12.35,
      "high_price": 12.68,
      "low_price": 12.30,
      "prev_close": 12.35,
      "change": 0.15,
      "change_percent": 1.22,
      "volume": 1234567,
      "turnover": 15432100.50,
      "timestamp": "2024-01-15T14:30:00Z"
    }
  ]
}
```

### 3. K线数据

#### 获取K线数据
```http
GET /api/v1/market/kline/{symbol}
```

**查询参数:**
- `period`: 周期 (1m/5m/15m/30m/1h/4h/1d/1w/1M)
- `limit`: 数量限制 (默认500)
- `start_time`: 开始时间戳
- `end_time`: 结束时间戳

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "timestamp": 1705305600,
      "open": 12.35,
      "high": 12.68,
      "low": 12.30,
      "close": 12.50,
      "volume": 1234567,
      "turnover": 15432100.50
    }
  ]
}
```

### 4. 市场概览

#### 获取市场概览
```http
GET /api/v1/market/overview
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "indices": [
      {
        "code": "000001",
        "name": "上证指数",
        "current": 3245.68,
        "change": 12.45,
        "change_percent": 0.38,
        "volume": 245680000,
        "turnover": 3456789000
      }
    ],
    "stats": {
      "total_stocks": 4500,
      "advancers": 2100,
      "decliners": 1800,
      "unchanged": 600
    }
  }
}
```

### 5. 板块数据

#### 获取板块表现
```http
GET /api/v1/market/sectors/performance
```

### 6. 排行榜

#### 获取排行榜
```http
GET /api/v1/market/rankings?type=change_percent&limit=50
```

**查询参数:**
- `type`: 排行类型 (change_percent/turnover/volume)
- `limit`: 数量限制
- `market`: 市场筛选

### 7. 股票搜索

#### 搜索股票
```http
GET /api/v1/market/enhanced-market/search?query=平安&limit=20
```

### 8. 自选股管理

#### 获取自选股
```http
GET /api/v1/market/watchlist
```

#### 添加自选股
```http
POST /api/v1/market/watchlist/{symbol}
```

#### 删除自选股
```http
DELETE /api/v1/market/watchlist/{symbol}
```

## WebSocket实时推送规范

### 连接地址
```
ws://localhost:8000/api/v1/ws?token=your_jwt_token
```

### 消息格式

#### 客户端订阅消息
```json
{
  "type": "subscribe",
  "data": {
    "topics": ["market:all", "market:000001", "market:000001:kline:1d"]
  }
}
```

#### 服务端推送消息
```json
{
  "type": "tick",
  "topic": "market:000001",
  "data": {
    "symbol": "000001",
    "current_price": 12.50,
    "change": 0.15,
    "change_percent": 1.22,
    "volume": 1234567,
    "timestamp": "2024-01-15T14:30:00Z"
  }
}
```

### 主题规范

1. **全量行情**: `market:all`
   - 推送频率: 3秒
   - 内容: 主要指数和热门股票简要信息

2. **单只股票**: `market:{symbol}`
   - 推送频率: 实时
   - 内容: 完整tick数据

3. **K线数据**: `market:{symbol}:kline:{interval}`
   - 推送频率: 按周期更新
   - 内容: 最新K线数据

### 心跳机制
- 客户端发送: `{"type": "ping"}`
- 服务端响应: `{"type": "pong", "timestamp": "..."}`
- 间隔: 30秒

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {...},
  "timestamp": "2024-01-15T14:30:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "symbol参数不能为空",
  "timestamp": "2024-01-15T14:30:00Z"
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误

## 缓存策略

### Redis缓存
- **实时行情**: TTL 5秒
- **K线数据**: TTL 60秒
- **股票列表**: TTL 300秒
- **板块数据**: TTL 300秒

### 客户端缓存
- **GET请求**: 30秒缓存
- **市场数据**: 10秒缓存
- **交易数据**: 不缓存

## 限流策略

### API限流
- **普通用户**: 100次/分钟
- **VIP用户**: 500次/分钟
- **系统内部**: 无限制

### WebSocket限流
- **连接数**: 最大1000个
- **消息频率**: 10条/秒
- **订阅主题**: 最大50个

## 数据质量保证

### 数据校验
1. 价格数据合理性检查
2. 成交量异常检测
3. 时间戳有效性验证

### 降级策略
1. 主数据源失败 → 备用数据源
2. 实时数据失败 → 缓存数据
3. 所有数据源失败 → 模拟数据

### 监控指标
- 数据源可用性
- 接口响应时间
- 错误率统计
- 缓存命中率

## 开发调试

### Swagger文档
访问: `http://localhost:8000/docs`

### 健康检查
```http
GET /api/v1/health
```

### 数据源状态
```http
GET /api/v1/market/enhanced-market/health
```

## 版本历史

- **v1.0** (2024-01-15): 初始版本，支持基础行情功能
