/**
 * 统一缓存管理器 - 前端版本
 * 与后端缓存策略保持一致
 */

export enum CacheType {
  REALTIME = 'realtime',
  QUOTE = 'quote', 
  KLINE = 'kline',
  STOCK_INFO = 'stock_info',
  HISTORICAL = 'historical',
  SEARCH = 'search'
}

interface CacheItem {
  data: any
  timestamp: number
  expiry: number
}

/**
 * 缓存配置 - 与后端保持一致
 */
export class CacheConfig {
  // TTL配置 (毫秒) - 与后端unified_cache.py保持一致
  static readonly DEFAULT_TTL = {
    [CacheType.REALTIME]: 30 * 1000,      // 30秒
    [CacheType.QUOTE]: 5 * 60 * 1000,     // 5分钟
    [CacheType.KLINE]: 15 * 60 * 1000,    // 15分钟
    [CacheType.STOCK_INFO]: 60 * 60 * 1000, // 1小时
    [CacheType.HISTORICAL]: 24 * 60 * 60 * 1000, // 1天
    [CacheType.SEARCH]: 10 * 60 * 1000    // 10分钟
  }

  // Key前缀配置
  static readonly KEY_PREFIXES = {
    [CacheType.REALTIME]: 'rt',
    [CacheType.QUOTE]: 'quote',
    [CacheType.KLINE]: 'kline',
    [CacheType.STOCK_INFO]: 'stock',
    [CacheType.HISTORICAL]: 'hist',
    [CacheType.SEARCH]: 'search'
  }

  static getTTL(cacheType: CacheType): number {
    return this.DEFAULT_TTL[cacheType] || 5 * 60 * 1000
  }

  static getKeyPrefix(cacheType: CacheType): string {
    return this.KEY_PREFIXES[cacheType] || 'cache'
  }

  static buildKey(cacheType: CacheType, ...parts: string[]): string {
    const prefix = this.getKeyPrefix(cacheType)
    const keyParts = [prefix, ...parts]
    return keyParts.join(':')
  }
}

/**
 * 统一缓存管理器
 */
export class UnifiedCacheManager {
  private cache = new Map<string, CacheItem>()
  private maxSize = 1000 // 最大缓存项数
  private cleanupInterval: number | null = null

  constructor() {
    this.startCleanup()
  }

  /**
   * 获取缓存值
   */
  async get(cacheType: CacheType, ...keyParts: string[]): Promise<any> {
    const key = CacheConfig.buildKey(cacheType, ...keyParts)
    const item = this.cache.get(key)

    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * 设置缓存值
   */
  async set(
    cacheType: CacheType, 
    data: any, 
    ...keyParts: string[]
  ): Promise<void> {
    const key = CacheConfig.buildKey(cacheType, ...keyParts)
    const ttl = CacheConfig.getTTL(cacheType)
    
    const item: CacheItem = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    }

    // 检查缓存大小限制
    if (this.cache.size >= this.maxSize) {
      this.evictOldestItems()
    }

    this.cache.set(key, item)
  }

  /**
   * 删除缓存
   */
  async delete(cacheType: CacheType, ...keyParts: string[]): Promise<boolean> {
    const key = CacheConfig.buildKey(cacheType, ...keyParts)
    return this.cache.delete(key)
  }

  /**
   * 检查缓存是否存在
   */
  async exists(cacheType: CacheType, ...keyParts: string[]): Promise<boolean> {
    const key = CacheConfig.buildKey(cacheType, ...keyParts)
    const item = this.cache.get(key)
    
    if (!item) {
      return false
    }

    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * 清除缓存
   */
  async clear(cacheType?: CacheType): Promise<number> {
    if (!cacheType) {
      const count = this.cache.size
      this.cache.clear()
      return count
    }

    const prefix = CacheConfig.getKeyPrefix(cacheType)
    let count = 0

    for (const key of this.cache.keys()) {
      if (key.startsWith(`${prefix}:`)) {
        this.cache.delete(key)
        count++
      }
    }

    return count
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const now = Date.now()
    let expiredCount = 0
    let validCount = 0

    for (const item of this.cache.values()) {
      if (now > item.expiry) {
        expiredCount++
      } else {
        validCount++
      }
    }

    return {
      totalKeys: this.cache.size,
      validKeys: validCount,
      expiredKeys: expiredCount,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate()
    }
  }

  /**
   * 淘汰最旧的缓存项
   */
  private evictOldestItems() {
    const itemsToEvict = Math.floor(this.maxSize * 0.1) // 淘汰10%
    const sortedEntries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)

    for (let i = 0; i < itemsToEvict && i < sortedEntries.length; i++) {
      this.cache.delete(sortedEntries[i][0])
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 启动定期清理
   */
  private startCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }

    // 每5分钟清理一次过期缓存
    this.cleanupInterval = window.setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)
  }

  /**
   * 计算缓存命中率
   */
  private calculateHitRate(): number {
    // 这里可以实现更复杂的命中率计算
    // 暂时返回一个估算值
    return 0.85
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.cache.clear()
  }
}

// 全局缓存管理器实例
export const unifiedCache = new UnifiedCacheManager()

// 在页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    unifiedCache.destroy()
  })
}