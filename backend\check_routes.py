#!/usr/bin/env python3
"""
检查后端路由状态
"""

import requests
import json

def check_backend_status():
    """检查后端服务状态"""
    print("🔍 检查后端服务状态...")
    
    try:
        # 检查健康状态
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正在运行")
        else:
            print(f"⚠️ 健康检查异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False
    
    return True

def check_routes():
    """检查可用路由"""
    print("\n📋 检查可用路由...")
    
    try:
        response = requests.get('http://localhost:8000/api/v1/routes', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"📊 总路由数: {data['total_routes']}")
            
            # 查找市场相关路由
            market_routes = []
            for route in data['routes']:
                if 'market' in route['path']:
                    market_routes.append(route)
            
            if market_routes:
                print("📈 市场相关路由:")
                for route in market_routes:
                    methods = ', '.join(route['methods'])
                    print(f"  {methods} {route['path']}")
            else:
                print("⚠️ 未找到市场相关路由")
                
            return market_routes
        else:
            print(f"❌ 路由查询失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 路由查询异常: {e}")
    
    return []

def test_market_apis():
    """测试市场API"""
    print("\n🧪 测试市场API...")
    
    # 测试市场概览
    try:
        response = requests.get('http://localhost:8000/api/v1/market/overview', timeout=5)
        print(f"市场概览API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 成功获取数据: {data.get('success', False)}")
        else:
            print(f"  ❌ 失败: {response.text[:100]}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试股票列表
    try:
        response = requests.get('http://localhost:8000/api/v1/market/stocks', timeout=5)
        print(f"股票列表API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 成功获取数据: {data.get('success', False)}")
        else:
            print(f"  ❌ 失败: {response.text[:100]}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")

def main():
    """主函数"""
    print("🚀 开始检查后端API状态\n")
    
    # 检查后端状态
    if not check_backend_status():
        return
    
    # 检查路由
    market_routes = check_routes()
    
    # 测试API
    test_market_apis()
    
    print("\n📊 检查完成!")

if __name__ == "__main__":
    main()
