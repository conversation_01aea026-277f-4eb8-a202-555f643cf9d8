/**
 * 增强的WebSocket服务
 * 与后端enhanced_websocket_service.py匹配的前端实现
 * 提供自动重连、心跳检测、错误处理和性能优化
 */

import { ElMessage, ElNotification } from 'element-plus'
import mitt from 'mitt'
import type { Emitter } from 'mitt'

// 消息类型枚举
export enum MessageType {
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe', 
  TICK = 'tick',
  KLINE = 'kline',
  ORDER_BOOK = 'orderbook',
  SYSTEM = 'system',
  ERROR = 'error',
  HEARTBEAT = 'heartbeat',
  AUTHENTICATION = 'auth'
}

// 连接状态枚举
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

export interface WebSocketMessage {
  type: MessageType
  data: any
  timestamp: string
}

export interface SubscriptionInfo {
  topic: string
  symbol: string
  subscribeTime: number
  isActive: boolean
}

export interface WebSocketEvents {
  connected: void
  disconnected: void
  reconnecting: number  // 重连次数
  error: { error: Error; critical: boolean }
  message: WebSocketMessage
  tick: any
  kline: any
  system: any
  heartbeat: any
}

export interface WebSocketStats {
  connectionState: ConnectionState
  connectTime?: number
  reconnectCount: number
  messagesSent: number
  messagesReceived: number
  subscriptionsCount: number
  lastError?: string
  uptime: number
  latency: number
}

export class EnhancedWebSocketService {
  private ws: WebSocket | null = null
  private eventEmitter: Emitter<WebSocketEvents>
  
  // 连接配置
  private readonly clientId: string
  private readonly baseUrl: string
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED
  
  // 重连配置
  private reconnectAttempts = 0
  private readonly maxReconnectAttempts = 10
  private readonly baseReconnectDelay = 1000
  private readonly maxReconnectDelay = 30000
  private reconnectTimer: NodeJS.Timeout | null = null
  
  // 心跳配置
  private heartbeatTimer: NodeJS.Timeout | null = null
  private readonly heartbeatInterval = 30000  // 30秒
  private lastHeartbeat = 0
  private heartbeatTimeouts = 0
  private readonly maxHeartbeatTimeouts = 3
  
  // 订阅管理
  private subscriptions = new Map<string, SubscriptionInfo>()
  private pendingSubscriptions = new Set<string>()
  
  // 统计信息
  private stats: WebSocketStats = {
    connectionState: ConnectionState.DISCONNECTED,
    reconnectCount: 0,
    messagesSent: 0,
    messagesReceived: 0,
    subscriptionsCount: 0,
    uptime: 0,
    latency: 0
  }
  
  // 消息队列（离线时缓存）
  private messageQueue: Array<{ type: MessageType; data: any }> = []
  private readonly maxQueueSize = 100
  
  // 性能监控
  private connectStartTime = 0
  private lastMessageTime = 0
  private latencyBuffer: number[] = []
  
  constructor(baseUrl = 'ws://localhost:8000/ws/market') {
    this.eventEmitter = mitt<WebSocketEvents>()
    this.clientId = `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    this.baseUrl = baseUrl
    
    // 启动统计更新定时器
    setInterval(() => this.updateStats(), 1000)
  }
  
  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTING || 
        this.connectionState === ConnectionState.CONNECTED) {
      return
    }
    
    this.setConnectionState(ConnectionState.CONNECTING)
    this.connectStartTime = Date.now()
    
    try {
      const wsUrl = `${this.baseUrl}?client_id=${this.clientId}`
      console.log(`[WebSocket] 连接中... (尝试 ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`, wsUrl)
      
      this.ws = new WebSocket(wsUrl)
      this.setupWebSocketHandlers()
      
    } catch (error) {
      console.error('[WebSocket] 连接失败:', error)
      this.handleConnectionError(error as Error)
    }
  }
  
  /**
   * 断开连接
   */
  disconnect(): void {
    console.log('[WebSocket] 主动断开连接')
    
    this.clearTimers()
    this.setConnectionState(ConnectionState.DISCONNECTED)
    
    if (this.ws) {
      this.ws.close(1000, '用户主动断开')
      this.ws = null
    }
    
    this.subscriptions.clear()
    this.pendingSubscriptions.clear()
    this.messageQueue = []
  }
  
  /**
   * 订阅数据
   */
  async subscribe(topic: string, symbol: string): Promise<boolean> {
    const subscriptionKey = `${topic}:${symbol}`
    
    if (this.subscriptions.has(subscriptionKey)) {
      return true // 已经订阅
    }
    
    const subscriptionInfo: SubscriptionInfo = {
      topic,
      symbol,
      subscribeTime: Date.now(),
      isActive: false
    }
    
    this.subscriptions.set(subscriptionKey, subscriptionInfo)
    this.pendingSubscriptions.add(subscriptionKey)
    
    // 发送订阅消息
    const success = await this.sendMessage(MessageType.SUBSCRIBE, { topic, symbol })
    
    if (success) {
      console.log(`[WebSocket] 订阅成功: ${subscriptionKey}`)
    } else {
      console.warn(`[WebSocket] 订阅失败: ${subscriptionKey}`)
      this.subscriptions.delete(subscriptionKey)
      this.pendingSubscriptions.delete(subscriptionKey)
    }
    
    return success
  }
  
  /**
   * 取消订阅
   */
  async unsubscribe(topic: string, symbol: string): Promise<boolean> {
    const subscriptionKey = `${topic}:${symbol}`
    
    if (!this.subscriptions.has(subscriptionKey)) {
      return true // 本来就没订阅
    }
    
    const success = await this.sendMessage(MessageType.UNSUBSCRIBE, { topic, symbol })
    
    if (success) {
      this.subscriptions.delete(subscriptionKey)
      this.pendingSubscriptions.delete(subscriptionKey)
      console.log(`[WebSocket] 取消订阅成功: ${subscriptionKey}`)
    }
    
    return success
  }
  
  /**
   * 发送消息
   */
  private async sendMessage(type: MessageType, data: any): Promise<boolean> {
    if (!this.isConnected()) {
      // 离线时加入队列
      if (this.messageQueue.length < this.maxQueueSize) {
        this.messageQueue.push({ type, data })
      }
      return false
    }
    
    try {
      const message = {
        type: type,
        data: data,
        timestamp: new Date().toISOString()
      }
      
      this.ws!.send(JSON.stringify(message))
      this.stats.messagesSent++
      
      return true
    } catch (error) {
      console.error('[WebSocket] 发送消息失败:', error)
      return false
    }
  }
  
  /**
   * 设置WebSocket事件处理器
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return
    
    this.ws.onopen = () => {
      const connectTime = Date.now() - this.connectStartTime
      console.log(`[WebSocket] 连接成功 (耗时: ${connectTime}ms)`)
      
      this.setConnectionState(ConnectionState.CONNECTED)
      this.stats.connectTime = Date.now()
      this.reconnectAttempts = 0
      
      this.startHeartbeat()
      this.processMessageQueue()
      this.resubscribeAll()
      
      this.eventEmitter.emit('connected')
      
      // 显示连接恢复通知
      if (this.stats.reconnectCount > 0) {
        ElMessage.success('连接已恢复')
      }
    }
    
    this.ws.onmessage = (event) => {
      this.handleMessage(event.data)
    }
    
    this.ws.onclose = (event) => {
      console.log(`[WebSocket] 连接关闭 (code: ${event.code}, reason: ${event.reason})`)
      
      this.clearTimers()
      this.setConnectionState(ConnectionState.DISCONNECTED)
      
      this.eventEmitter.emit('disconnected')
      
      // 自动重连（非正常关闭且未超过最大重连次数）
      if (event.code !== 1000 && this.shouldReconnect()) {
        this.scheduleReconnect()
      }
    }
    
    this.ws.onerror = (error) => {
      console.error('[WebSocket] 连接错误:', error)
      this.handleConnectionError(new Error('WebSocket连接错误'))
    }
  }
  
  /**
   * 处理接收到的消息
   */
  private handleMessage(rawData: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(rawData)
      this.stats.messagesReceived++
      this.lastMessageTime = Date.now()
      
      // 计算延迟
      if (message.timestamp) {
        const serverTime = new Date(message.timestamp).getTime()
        const latency = Date.now() - serverTime
        this.updateLatency(latency)
      }
      
      // 分发消息
      this.eventEmitter.emit('message', message)
      
      switch (message.type) {
        case MessageType.TICK:
          this.eventEmitter.emit('tick', message.data)
          break
          
        case MessageType.KLINE:
          this.eventEmitter.emit('kline', message.data)
          break
          
        case MessageType.SYSTEM:
          this.handleSystemMessage(message.data)
          break
          
        case MessageType.ERROR:
          this.handleErrorMessage(message.data)
          break
          
        case MessageType.HEARTBEAT:
          this.handleHeartbeat(message.data)
          break
      }
      
    } catch (error) {
      console.error('[WebSocket] 解析消息失败:', error)
    }
  }
  
  /**
   * 处理系统消息
   */
  private handleSystemMessage(data: any): void {
    console.log('[WebSocket] 系统消息:', data)
    
    if (data.message?.includes('订阅成功')) {
      // 更新订阅状态
      const details = data.details || {}
      const subscriptionKey = `${details.topic}:${details.symbol}`
      
      if (this.subscriptions.has(subscriptionKey)) {
        this.subscriptions.get(subscriptionKey)!.isActive = true
        this.pendingSubscriptions.delete(subscriptionKey)
      }
    }
    
    this.eventEmitter.emit('system', data)
  }
  
  /**
   * 处理错误消息
   */
  private handleErrorMessage(data: any): void {
    console.error('[WebSocket] 服务器错误:', data)
    
    const error = new Error(data.user_message || data.error_message || '未知错误')
    this.eventEmitter.emit('error', { error, critical: false })
    
    // 显示用户友好的错误提示
    if (data.user_message) {
      ElNotification.error({
        title: '数据服务异常',
        message: data.user_message,
        duration: 5000
      })
    }
  }
  
  /**
   * 处理心跳响应
   */
  private handleHeartbeat(data: any): void {
    this.lastHeartbeat = Date.now()
    this.heartbeatTimeouts = 0
    this.eventEmitter.emit('heartbeat', data)
  }
  
  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.clearHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.sendMessage(MessageType.HEARTBEAT, { status: 'ping' })
        
        // 检查心跳超时
        if (Date.now() - this.lastHeartbeat > this.heartbeatInterval * 2) {
          this.heartbeatTimeouts++
          
          if (this.heartbeatTimeouts >= this.maxHeartbeatTimeouts) {
            console.warn('[WebSocket] 心跳超时，重新连接')
            this.reconnect()
          }
        }
      }
    }, this.heartbeatInterval)
  }
  
  /**
   * 重新订阅所有内容
   */
  private async resubscribeAll(): Promise<void> {
    const subscriptions = Array.from(this.subscriptions.values())
    
    for (const sub of subscriptions) {
      await this.sendMessage(MessageType.SUBSCRIBE, {
        topic: sub.topic,
        symbol: sub.symbol
      })
      
      this.pendingSubscriptions.add(`${sub.topic}:${sub.symbol}`)
    }
    
    console.log(`[WebSocket] 重新订阅 ${subscriptions.length} 个主题`)
  }
  
  /**
   * 处理离线消息队列
   */
  private processMessageQueue(): void {
    if (this.messageQueue.length === 0) return
    
    console.log(`[WebSocket] 处理离线消息队列 (${this.messageQueue.length} 条)`)
    
    for (const { type, data } of this.messageQueue) {
      this.sendMessage(type, data)
    }
    
    this.messageQueue = []
  }
  
  /**
   * 处理连接错误
   */
  private handleConnectionError(error: Error): void {
    console.error('[WebSocket] 连接错误:', error)
    
    this.setConnectionState(ConnectionState.ERROR)
    this.stats.lastError = error.message
    
    this.eventEmitter.emit('error', { error, critical: true })
    
    if (this.shouldReconnect()) {
      this.scheduleReconnect()
    } else {
      ElNotification.error({
        title: '连接失败',
        message: '无法连接到数据服务器，请检查网络或稍后重试',
        duration: 0
      })
    }
  }
  
  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) return
    
    this.setConnectionState(ConnectionState.RECONNECTING)
    this.stats.reconnectCount++
    
    const delay = Math.min(
      this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.maxReconnectDelay
    )
    
    console.log(`[WebSocket] ${delay}ms 后重连 (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`)
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null
      this.reconnectAttempts++
      this.eventEmitter.emit('reconnecting', this.reconnectAttempts)
      this.connect()
    }, delay)
  }
  
  /**
   * 立即重连
   */
  private reconnect(): void {
    if (this.ws) {
      this.ws.close()
    }
    
    setTimeout(() => {
      this.connect()
    }, 1000)
  }
  
  /**
   * 判断是否应该重连
   */
  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.maxReconnectAttempts
  }
  
  /**
   * 设置连接状态
   */
  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state
      this.stats.connectionState = state
    }
  }
  
  /**
   * 清理定时器
   */
  private clearTimers(): void {
    this.clearHeartbeat()
    this.clearReconnectTimer()
  }
  
  private clearHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  /**
   * 更新延迟统计
   */
  private updateLatency(latency: number): void {
    this.latencyBuffer.push(latency)
    
    if (this.latencyBuffer.length > 10) {
      this.latencyBuffer.shift()
    }
    
    this.stats.latency = this.latencyBuffer.reduce((a, b) => a + b, 0) / this.latencyBuffer.length
  }
  
  /**
   * 更新统计信息
   */
  private updateStats(): void {
    if (this.stats.connectTime) {
      this.stats.uptime = Date.now() - this.stats.connectTime
    }
    
    this.stats.subscriptionsCount = this.subscriptions.size
  }
  
  /**
   * 公共方法
   */
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
  
  public getConnectionState(): ConnectionState {
    return this.connectionState
  }
  
  public getStats(): WebSocketStats {
    return { ...this.stats }
  }
  
  public getSubscriptions(): Array<SubscriptionInfo> {
    return Array.from(this.subscriptions.values())
  }
  
  public on<T extends keyof WebSocketEvents>(
    event: T,
    handler: (data: WebSocketEvents[T]) => void
  ): void {
    this.eventEmitter.on(event, handler)
  }
  
  public off<T extends keyof WebSocketEvents>(
    event: T,
    handler: (data: WebSocketEvents[T]) => void
  ): void {
    this.eventEmitter.off(event, handler)
  }
  
  /**
   * 清理资源
   */
  public destroy(): void {
    this.disconnect()
    this.eventEmitter.all.clear()
  }
}

// 全局实例
export const enhancedWebSocketService = new EnhancedWebSocketService()