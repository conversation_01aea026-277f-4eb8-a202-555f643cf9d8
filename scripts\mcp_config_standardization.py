#!/usr/bin/env python3
"""
MCP 配置和文档管理规范化脚本
建立统一的配置管理和文档标准

任务：
1. 创建统一的配置管理结构
2. 建立文档模板和标准
3. 创建环境配置模板
4. 设置自动化文档检查
"""

import os
import shutil
import json
import sys
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

class MCPConfigStandardizer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues_fixed = []
        self.errors = []
        
    def log_fix(self, issue: str, action: str, status: str = "success"):
        """记录修复操作"""
        self.issues_fixed.append({
            "issue": issue,
            "action": action,
            "status": status,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[SUCCESS] {issue}: {action}")
        
    def log_error(self, issue: str, error: str):
        """记录错误"""
        self.errors.append({
            "issue": issue,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[ERROR] {issue}: {error}")

    def create_environment_config_templates(self):
        """创建环境配置模板"""
        config_dir = self.project_root / "config" / "environment"
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # 开发环境配置模板
        dev_config = """# 开发环境配置文件
# 复制为 .env.development 并根据需要修改

# === 数据库配置 ===
DATABASE_URL=sqlite:///./quant_platform.db
# 对于PostgreSQL使用：
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/quant_dev

# === Redis配置 ===
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# === API配置 ===
API_V1_PREFIX=/api/v1
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true

# === 安全配置 ===
SECRET_KEY=your-development-secret-key-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# === 外部数据源 ===
TUSHARE_TOKEN=your-tushare-token
AKSHARE_ENABLED=true

# === 日志配置 ===
LOG_LEVEL=DEBUG
LOG_FORMAT=structured
LOG_FILE=logs/app.log

# === 前端配置 ===
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=["http://localhost:5173", "http://127.0.0.1:5173"]

# === 监控配置 ===
ENABLE_MONITORING=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090
"""
        
        # 生产环境配置模板
        prod_config = """# 生产环境配置文件
# 复制为 .env.production 并根据需要修改

# === 数据库配置 ===
DATABASE_URL=postgresql+asyncpg://username:password@db_host:5432/quant_prod
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# === Redis配置 ===
REDIS_URL=redis://redis_host:6379/0
REDIS_PASSWORD=your-redis-password
REDIS_POOL_SIZE=10

# === API配置 ===
API_V1_PREFIX=/api/v1
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false

# === 安全配置 ===
SECRET_KEY=your-production-secret-key-must-be-secure
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256

# === 外部数据源 ===
TUSHARE_TOKEN=your-production-tushare-token
AKSHARE_ENABLED=true

# === 日志配置 ===
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/quant/app.log

# === 前端配置 ===
FRONTEND_URL=https://your-domain.com
CORS_ORIGINS=["https://your-domain.com"]

# === 监控配置 ===
ENABLE_MONITORING=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_URL=http://grafana:3000

# === 性能配置 ===
WORKER_PROCESSES=4
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65
"""
        
        # 测试环境配置模板
        test_config = """# 测试环境配置文件
# 复制为 .env.test 并根据需要修改

# === 数据库配置 ===
DATABASE_URL=sqlite:///./test_quant_platform.db
DATABASE_ECHO=false

# === Redis配置 ===
REDIS_URL=redis://localhost:6379/1
REDIS_PASSWORD=

# === API配置 ===
API_V1_PREFIX=/api/v1
API_HOST=127.0.0.1
API_PORT=8001
API_DEBUG=true

# === 安全配置 ===
SECRET_KEY=test-secret-key-not-for-production
ACCESS_TOKEN_EXPIRE_MINUTES=5
ALGORITHM=HS256

# === 外部数据源 ===
TUSHARE_TOKEN=
AKSHARE_ENABLED=false
USE_MOCK_DATA=true

# === 日志配置 ===
LOG_LEVEL=DEBUG
LOG_FORMAT=simple
LOG_FILE=logs/test.log

# === 前端配置 ===
FRONTEND_URL=http://localhost:5174
CORS_ORIGINS=["http://localhost:5174"]

# === 测试配置 ===
TESTING=true
DISABLE_AUTH=false
MOCK_EXTERNAL_APIS=true
"""
        
        configs = {
            "development.env.template": dev_config,
            "production.env.template": prod_config,
            "test.env.template": test_config
        }
        
        for filename, content in configs.items():
            try:
                config_file = config_dir / filename
                config_file.write_text(content, encoding='utf-8')
                self.log_fix("Config-Templates", f"创建环境配置模板: {filename}")
            except Exception as e:
                self.log_error("Config-Templates", f"创建模板失败 {filename}: {e}")

    def create_documentation_templates(self):
        """创建文档模板"""
        docs_dir = self.project_root / "docs" / "templates"
        docs_dir.mkdir(parents=True, exist_ok=True)
        
        # API文档模板
        api_doc_template = """# API接口文档模板

## 接口概述
- **接口名称**: [接口名称]
- **接口路径**: [HTTP方法] /api/v1/[路径]
- **接口描述**: [简要描述接口功能]
- **开发者**: [开发者姓名]
- **创建时间**: [YYYY-MM-DD]
- **更新时间**: [YYYY-MM-DD]

## 请求参数

### 路径参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | int | 是 | 资源ID | 123 |

### 查询参数
| 参数名 | 类型 | 必填 | 描述 | 示例 | 默认值 |
|--------|------|------|------|------|--------|
| page | int | 否 | 页码 | 1 | 1 |
| size | int | 否 | 每页数量 | 20 | 10 |

### 请求体
```json
{
  "field1": "value1",
  "field2": "value2"
}
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "detail": "detailed error information"
}
```

## 状态码说明
| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### cURL
```bash
curl -X GET "http://localhost:8000/api/v1/example" \\
  -H "Authorization: Bearer your-token" \\
  -H "Content-Type: application/json"
```

### Python
```python
import requests

response = requests.get(
    "http://localhost:8000/api/v1/example",
    headers={"Authorization": "Bearer your-token"}
)
print(response.json())
```

### JavaScript
```javascript
fetch('/api/v1/example', {
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项
- [特殊说明]
- [使用限制]
- [性能考虑]
"""
        
        # 功能设计文档模板
        feature_doc_template = """# 功能设计文档模板

## 功能概述
- **功能名称**: [功能名称]
- **功能模块**: [所属模块]
- **优先级**: [High/Medium/Low]
- **开发者**: [开发者姓名]
- **设计时间**: [YYYY-MM-DD]
- **预计完成**: [YYYY-MM-DD]

## 需求描述

### 业务背景
[描述业务背景和需求来源]

### 功能目标
- [目标1]
- [目标2]
- [目标3]

### 用户故事
作为[用户角色]，我希望[功能描述]，以便[价值体现]。

## 技术设计

### 架构设计
[描述技术架构和设计思路]

### 数据模型
```sql
-- 数据表设计
CREATE TABLE example_table (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API设计
- GET /api/v1/resource - 获取资源列表
- POST /api/v1/resource - 创建资源
- PUT /api/v1/resource/{id} - 更新资源
- DELETE /api/v1/resource/{id} - 删除资源

### 前端组件
- [组件1]: [描述]
- [组件2]: [描述]

## 实现计划

### 开发阶段
1. **阶段1**: [描述] - [时间]
2. **阶段2**: [描述] - [时间]
3. **阶段3**: [描述] - [时间]

### 测试计划
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户验收测试

### 部署计划
- [ ] 开发环境部署
- [ ] 测试环境验证
- [ ] 生产环境发布

## 风险评估
| 风险项 | 影响 | 概率 | 应对措施 |
|--------|------|------|----------|
| [风险1] | High | Medium | [应对措施] |

## 验收标准
- [ ] [标准1]
- [ ] [标准2]
- [ ] [标准3]
"""
        
        # 问题修复文档模板
        bugfix_doc_template = """# 问题修复文档模板

## 问题概述
- **问题标题**: [问题简要描述]
- **问题ID**: [Bug ID或Issue编号]
- **优先级**: [Critical/High/Medium/Low]
- **发现时间**: [YYYY-MM-DD]
- **修复时间**: [YYYY-MM-DD]
- **修复人员**: [开发者姓名]

## 问题描述

### 问题现象
[详细描述问题的表现]

### 影响范围
- **影响功能**: [受影响的功能模块]
- **影响用户**: [受影响的用户群体]
- **严重程度**: [描述问题的严重程度]

### 复现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

### 预期结果
[描述预期的正确行为]

### 实际结果
[描述实际观察到的错误行为]

## 问题分析

### 根本原因
[分析问题的根本原因]

### 技术细节
```python
# 问题代码示例
def problematic_function():
    # 有问题的实现
    pass
```

### 相关日志
```
[错误日志内容]
```

## 解决方案

### 修复方法
[描述采用的修复方法]

### 代码变更
```python
# 修复后的代码
def fixed_function():
    # 正确的实现
    pass
```

### 配置变更
[如果涉及配置变更，详细说明]

## 测试验证

### 测试用例
- [ ] [测试用例1]
- [ ] [测试用例2]
- [ ] [测试用例3]

### 回归测试
- [ ] [相关功能测试]
- [ ] [集成测试]

## 预防措施
- [措施1]
- [措施2]
- [措施3]

## 相关文档
- [相关文档链接]
- [参考资料]
"""
        
        templates = {
            "api_doc_template.md": api_doc_template,
            "feature_doc_template.md": feature_doc_template,
            "bugfix_doc_template.md": bugfix_doc_template
        }
        
        for filename, content in templates.items():
            try:
                template_file = docs_dir / filename
                template_file.write_text(content, encoding='utf-8')
                self.log_fix("Doc-Templates", f"创建文档模板: {filename}")
            except Exception as e:
                self.log_error("Doc-Templates", f"创建模板失败 {filename}: {e}")

    def create_config_management_guide(self):
        """创建配置管理指南"""
        guide_content = """# 配置管理指南

## 配置层级

### 1. 环境配置
```
config/environment/
├── development.env.template    # 开发环境模板
├── production.env.template     # 生产环境模板
├── test.env.template          # 测试环境模板
└── README.md                  # 配置说明
```

### 2. 应用配置
```
backend/app/core/config.py     # 后端配置管理
frontend/src/config/           # 前端配置管理
```

### 3. 基础设施配置
```
docker/                        # 容器配置
monitoring/                    # 监控配置
scripts/                       # 部署脚本
```

## 配置使用流程

### 1. 首次部署
```bash
# 1. 复制环境配置模板
cp config/environment/development.env.template .env.development

# 2. 修改配置参数
vim .env.development

# 3. 加载配置
source .env.development

# 4. 启动服务
./scripts/start.sh
```

### 2. 环境切换
```bash
# 切换到生产环境
export ENVIRONMENT=production
source .env.production

# 切换到测试环境
export ENVIRONMENT=test
source .env.test
```

### 3. 配置验证
```bash
# 验证配置完整性
python scripts/validate-config.py

# 检查敏感信息
python scripts/check-secrets.py
```

## 配置管理最佳实践

### 1. 敏感信息管理
- **禁止**: 敏感信息写入代码
- **禁止**: 敏感信息提交到版本控制
- **推荐**: 使用环境变量
- **推荐**: 使用密钥管理服务

### 2. 配置层次化
```python
# 配置优先级（从高到低）
1. 环境变量
2. .env文件
3. 配置文件
4. 默认值
```

### 3. 配置验证
```python
# 配置模式验证
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    database_url: str
    redis_url: str
    secret_key: str
    
    @validator('secret_key')
    def secret_key_must_be_secure(cls, v):
        if len(v) < 32:
            raise ValueError('Secret key must be at least 32 characters')
        return v
```

## 环境差异管理

### 开发环境特点
- SQLite数据库
- Debug模式开启
- 详细日志输出
- Mock外部服务

### 测试环境特点
- 内存数据库
- 快速测试数据
- Mock所有外部依赖
- 覆盖率收集

### 生产环境特点
- PostgreSQL数据库
- 性能优化配置
- 结构化日志
- 监控告警

## 配置安全检查清单

### 部署前检查
- [ ] 所有密钥已更新
- [ ] 数据库连接已验证
- [ ] 外部服务连接已测试
- [ ] 日志级别已设置
- [ ] 监控配置已启用
- [ ] 备份策略已配置

### 运行时监控
- [ ] 配置加载成功
- [ ] 服务连接正常
- [ ] 性能指标正常
- [ ] 错误率在阈值内
"""
        
        guide_file = self.project_root / "docs" / "config_management_guide.md"
        
        try:
            guide_file.write_text(guide_content, encoding='utf-8')
            self.log_fix("Config-Guide", f"创建配置管理指南: {guide_file}")
        except Exception as e:
            self.log_error("Config-Guide", f"创建指南失败: {e}")

    def create_documentation_standards(self):
        """创建文档标准"""
        standards_content = """# 文档标准规范

## 文档分类

### 1. 技术文档
- **API文档**: 接口定义和使用说明
- **架构文档**: 系统架构和设计说明
- **开发文档**: 开发环境搭建和开发指南
- **部署文档**: 部署流程和运维指南

### 2. 业务文档
- **需求文档**: 业务需求和功能规格
- **用户文档**: 用户使用手册和帮助文档
- **培训文档**: 培训材料和操作指南

### 3. 项目文档
- **项目计划**: 项目规划和里程碑
- **会议纪要**: 会议记录和决策
- **变更日志**: 版本变更和发布说明

## 文档命名规范

### 文件命名
```
[类型]_[模块]_[功能]_[版本].md

示例:
- API_market_data_v1.0.md      # API文档
- ARCH_trading_system_v2.0.md  # 架构文档
- GUIDE_deployment_prod.md     # 部署指南
- REQ_risk_management_v1.1.md  # 需求文档
```

### 目录结构
```
docs/
├── api/                    # API文档
│   ├── market/            # 市场数据API
│   ├── trading/           # 交易API
│   └── user/              # 用户API
├── architecture/          # 架构文档
├── deployment/           # 部署文档
├── guides/               # 操作指南
├── requirements/         # 需求文档
├── templates/            # 文档模板
└── README.md            # 文档索引
```

## 文档格式标准

### Markdown规范
```markdown
# 一级标题（文档标题）

## 二级标题（主要章节）

### 三级标题（子章节）

#### 四级标题（细节说明）

- 无序列表项
  - 子项目
  
1. 有序列表项
2. 第二项

`内联代码`

```语言
代码块
```

| 表格 | 列1 | 列2 |
|------|-----|-----|
| 行1  | 值1 | 值2 |

> 引用内容

**粗体文本**
*斜体文本*
```

### 文档结构模板
```markdown
# 文档标题

## 概述
[简要说明文档内容和目的]

## 目标读者
[说明文档的目标读者]

## 前置条件
[使用文档前需要了解的内容]

## 主要内容
[详细内容章节]

## 示例
[实际使用示例]

## 常见问题
[FAQ部分]

## 相关文档
[关联文档链接]

## 更新日志
| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|---------|
| 1.0 | 2025-01-01 | 初始版本 | 作者 |
```

## 文档质量标准

### 内容要求
- **完整性**: 覆盖所有必要信息
- **准确性**: 信息真实可靠
- **时效性**: 及时更新维护
- **可读性**: 语言清晰易懂

### 格式要求
- **一致性**: 遵循统一格式
- **结构化**: 逻辑清晰有序
- **可导航**: 提供目录和链接
- **可搜索**: 使用合适的关键词

### 维护要求
- **版本控制**: 记录变更历史
- **责任人**: 明确维护责任
- **审核流程**: 建立审核机制
- **反馈渠道**: 收集用户反馈

## 文档审核流程

### 1. 创建阶段
- 作者根据模板创建文档
- 填写必要的元信息
- 完成初始内容编写

### 2. 内部审核
- 技术审核（技术准确性）
- 内容审核（完整性和逻辑性）
- 格式审核（格式规范性）

### 3. 发布准备
- 最终校对和修正
- 版本标记和归档
- 发布权限设置

### 4. 持续维护
- 定期内容更新
- 用户反馈处理
- 版本升级维护

## 工具和资源

### 推荐工具
- **编辑器**: VS Code + Markdown插件
- **预览器**: Typora, MarkText
- **协作平台**: GitLab/GitHub Wiki
- **图表工具**: Draw.io, PlantUML

### 资源链接
- [Markdown语法指南](https://markdown.com.cn/)
- [技术写作最佳实践](https://developers.google.com/tech-writing)
- [API文档最佳实践](https://swagger.io/resources/articles/best-practices-in-api-documentation/)
"""
        
        standards_file = self.project_root / "docs" / "documentation_standards.md"
        
        try:
            standards_file.write_text(standards_content, encoding='utf-8')
            self.log_fix("Doc-Standards", f"创建文档标准: {standards_file}")
        except Exception as e:
            self.log_error("Doc-Standards", f"创建标准失败: {e}")

    def run_all_fixes(self):
        """执行所有配置规范化"""
        print("开始MCP配置和文档管理规范化...")
        print("=" * 50)
        
        # 执行所有操作
        self.create_environment_config_templates()
        self.create_documentation_templates()
        self.create_config_management_guide()
        self.create_documentation_standards()
        
        # 生成报告
        self.generate_report()

    def generate_report(self):
        """生成报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_fixes": len(self.issues_fixed),
            "total_errors": len(self.errors),
            "fixes": self.issues_fixed,
            "errors": self.errors
        }
        
        # 保存JSON报告
        report_file = self.project_root / "reports" / "mcp_config_standardization_report.json"
        report_file.parent.mkdir(exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存报告失败: {e}")
        
        # 打印摘要
        print("\n" + "=" * 50)
        print("MCP配置规范化摘要")
        print("=" * 50)
        print(f"成功完成: {len(self.issues_fixed)}个任务")
        print(f"处理失败: {len(self.errors)}个任务")
        
        if self.issues_fixed:
            print("\n完成的任务:")
            for fix in self.issues_fixed:
                print(f"  - {fix['issue']}: {fix['action']}")
        
        if self.errors:
            print("\n需要手动处理的任务:")
            for error in self.errors:
                print(f"  - {error['issue']}: {error['error']}")
        
        print(f"\n详细报告: {report_file}")

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    standardizer = MCPConfigStandardizer(project_root)
    standardizer.run_all_fixes()

if __name__ == "__main__":
    main()