"""
缓存预热服务
在应用启动时预加载热门数据到缓存中，提升用户体验
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.core.unified_cache import unified_cache, CacheType
from app.services.data_source_manager import data_source_manager

logger = logging.getLogger(__name__)


class CacheWarmupService:
    """缓存预热服务"""
    
    def __init__(self):
        self.is_warming_up = False
        self.warmup_stats = {
            'started_at': None,
            'completed_at': None,
            'total_items': 0,
            'successful_items': 0,
            'failed_items': 0,
            'errors': []
        }
    
    async def warmup_cache(self, symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        执行缓存预热
        
        Args:
            symbols: 要预热的股票代码列表，如果为None则使用默认热门股票
        
        Returns:
            预热统计信息
        """
        if self.is_warming_up:
            logger.warning("Cache warmup already in progress")
            return self.warmup_stats
        
        self.is_warming_up = True
        self.warmup_stats = {
            'started_at': datetime.now().isoformat(),
            'completed_at': None,
            'total_items': 0,
            'successful_items': 0,
            'failed_items': 0,
            'errors': []
        }
        
        try:
            # 确保缓存已初始化
            await unified_cache.initialize()
            
            # 使用默认热门股票列表
            if symbols is None:
                symbols = self._get_popular_symbols()
            
            self.warmup_stats['total_items'] = len(symbols)
            logger.info(f"Starting cache warmup for {len(symbols)} symbols")
            
            # 预热实时行情数据
            await self._warmup_realtime_quotes(symbols)
            
            # 预热K线数据
            await self._warmup_kline_data(symbols)
            
            # 预热市场概览
            await self._warmup_market_overview()
            
            self.warmup_stats['completed_at'] = datetime.now().isoformat()
            logger.info(f"Cache warmup completed: {self.warmup_stats['successful_items']}/{self.warmup_stats['total_items']} successful")
            
        except Exception as e:
            logger.error(f"Cache warmup failed: {e}")
            self.warmup_stats['errors'].append(str(e))
        finally:
            self.is_warming_up = False
        
        return self.warmup_stats
    
    async def _warmup_realtime_quotes(self, symbols: List[str]):
        """预热实时行情数据"""
        logger.info("Warming up realtime quotes")
        
        for symbol in symbols:
            try:
                # 尝试从数据源获取数据
                quote_data = await data_source_manager.get_realtime_quote(symbol)
                if quote_data:
                    # 缓存数据
                    cache_key = f"realtime_quotes:{symbol}"
                    await unified_cache.set(
                        CacheType.REALTIME, 
                        quote_data, 
                        cache_key, 
                        expire=30
                    )
                    self.warmup_stats['successful_items'] += 1
                    logger.debug(f"Warmed up quote for {symbol}")
                else:
                    self.warmup_stats['failed_items'] += 1
                    logger.warning(f"No quote data available for {symbol}")
                
                # 避免过快请求
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.warmup_stats['failed_items'] += 1
                error_msg = f"Failed to warmup quote for {symbol}: {e}"
                self.warmup_stats['errors'].append(error_msg)
                logger.error(error_msg)
    
    async def _warmup_kline_data(self, symbols: List[str]):
        """预热K线数据"""
        logger.info("Warming up K-line data")
        
        periods = ['1d', '1w']  # 预热日线和周线
        
        for symbol in symbols[:5]:  # 只为前5个热门股票预热K线数据
            for period in periods:
                try:
                    kline_data = await data_source_manager.get_kline_data(
                        symbol, period=period, limit=100
                    )
                    if kline_data:
                        cache_key = f"kline:{symbol}:{period}:100"
                        await unified_cache.set(
                            CacheType.KLINE,
                            [kline.__dict__ for kline in kline_data],
                            cache_key,
                            expire=900  # 15分钟
                        )
                        logger.debug(f"Warmed up K-line for {symbol} {period}")
                    
                    await asyncio.sleep(0.2)
                    
                except Exception as e:
                    error_msg = f"Failed to warmup K-line for {symbol} {period}: {e}"
                    self.warmup_stats['errors'].append(error_msg)
                    logger.error(error_msg)
    
    async def _warmup_market_overview(self):
        """预热市场概览数据"""
        logger.info("Warming up market overview")
        
        try:
            # 这里可以调用市场概览API来预热数据
            # 由于我们使用mock数据，这里只是设置一个示例
            overview_data = {
                "indices": {
                    "上证指数": {"value": 3200.0, "change": 1.2, "change_percent": 0.038},
                    "深证成指": {"value": 12000.0, "change": -5.8, "change_percent": -0.048},
                    "创业板指": {"value": 2800.0, "change": 8.5, "change_percent": 0.304}
                },
                "stats": {"advancers": 1245, "decliners": 987, "unchanged": 234, "total": 2466}
            }
            
            await unified_cache.set(
                CacheType.REALTIME,
                overview_data,
                "market_overview",
                expire=30
            )
            logger.info("Market overview warmed up successfully")
            
        except Exception as e:
            error_msg = f"Failed to warmup market overview: {e}"
            self.warmup_stats['errors'].append(error_msg)
            logger.error(error_msg)
    
    def _get_popular_symbols(self) -> List[str]:
        """获取热门股票代码列表"""
        return [
            "000001",  # 平安银行
            "000002",  # 万科A
            "600000",  # 浦发银行
            "600036",  # 招商银行
            "600519",  # 贵州茅台
            "000858",  # 五粮液
            "002415",  # 海康威视
            "600276",  # 恒瑞医药
            "000725",  # 京东方A
            "002594",  # 比亚迪
            "600887",  # 伊利股份
            "000063",  # 中兴通讯
            "002230",  # 科大讯飞
            "600031",  # 三一重工
            "000568",  # 泸州老窖
        ]
    
    def get_warmup_status(self) -> Dict[str, Any]:
        """获取预热状态"""
        return {
            "is_warming_up": self.is_warming_up,
            "stats": self.warmup_stats
        }


# 全局缓存预热服务实例
cache_warmup_service = CacheWarmupService()
