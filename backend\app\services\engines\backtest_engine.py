"""backtest_engine.py
核心回测引擎

该模块提供 BacktestEngine 类，负责：
1. 加载历史数据（目前通过依赖注入的 data_loader 回调获得 pandas.DataFrame）。
2. 运行策略（strategy_callable 接收行数据并返回 signal）。
3. 执行撮合逻辑（简单买入/卖出，固定滑点与手续费）。
4. 生成收益、回撤等基本绩效指标。

后续可扩展：多标的、分批下单、止盈止损等。
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)

# --------------------------- 数据类与类型 ---------------------------


@dataclass
class BacktestConfig:
    """回测配置"""

    symbol: str
    start_date: datetime
    end_date: datetime
    initial_capital: float = 1_000_000
    commission_rate: float = 0.0003
    slippage: float = 0.0
    benchmark: str = None


@dataclass
class BacktestResult:
    equity_curve: pd.Series
    trades: List[Dict[str, Any]]
    metrics: Dict[str, Any]


# --------------------------- 回测引擎 ---------------------------


class BacktestEngine:
    """简单的向量化回测引擎（单标的示例）"""

    def __init__(
        self,
        data_loader: Callable[[str, datetime, datetime, str], pd.DataFrame],
        strategy_callable: Callable[[pd.DataFrame], pd.Series],
        symbol: str,
        start: datetime,
        end: datetime,
        initial_capital: float = 1_000_000,
        commission_rate: float = 0.0003,
        slippage: float = 0.0,
    ) -> None:
        self.data_loader = data_loader
        self.strategy_callable = strategy_callable
        self.symbol = symbol
        self.start = start
        self.end = end
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage

        self.data: pd.DataFrame | None = None
        self.trades: List[Dict[str, Any]] = []

    # -----------------------------------------------------------------
    def run(self) -> BacktestResult:
        """执行回测并返回结果"""
        self._load_data()
        signals = self._generate_signals()
        self._simulate_trading(signals)
        result = self._calculate_metrics()
        return result

    # -----------------------------------------------------------------
    def _load_data(self) -> None:
        """调用 data_loader 载入历史数据"""
        logger.info("加载历史数据: %s %s ~ %s", self.symbol, self.start, self.end)
        self.data = self.data_loader(self.symbol, self.start, self.end, "1d")
        if self.data is None or self.data.empty:
            raise ValueError("历史数据为空，无法回测")
        self.data = self.data.sort_index()

    def _generate_signals(self) -> pd.Series:
        """调用策略函数，返回 pd.Series 信号 (1 买入, -1 卖出, 0 空)"""
        logger.info("生成交易信号")
        signals = self.strategy_callable(self.data)
        if len(signals) != len(self.data):
            raise ValueError("策略返回的信号长度与数据不匹配")
        return signals

    def _simulate_trading(self, signals: pd.Series) -> None:
        """简单撮合：当信号从 0 -> 1 买入，1 -> -1 反手，-1 -> 0/1 平仓"""
        position = 0  # 当前仓位 (手数)
        cash = self.initial_capital
        equity_curve = []
        last_price = 0.0

        for ts, row in self.data.iterrows():
            price = row["close"]
            signal = signals.loc[ts]

            # 开仓或平仓
            if signal == 1 and position <= 0:
                trade_size = cash // (price * 100)  # 以100股为最小交易单位
                if trade_size > 0:
                    cost = (
                        trade_size * price * 100 * (1 + self.commission_rate)
                        + self.slippage * trade_size
                    )
                    cash -= cost
                    position += trade_size * 100
                    self.trades.append(
                        {
                            "timestamp": ts,
                            "type": "BUY",
                            "price": price,
                            "volume": trade_size * 100,
                        }
                    )
            elif signal == -1 and position >= 0:
                if position > 0:
                    proceeds = position * price * (
                        1 - self.commission_rate
                    ) - self.slippage * (position / 100)
                    cash += proceeds
                    self.trades.append(
                        {
                            "timestamp": ts,
                            "type": "SELL",
                            "price": price,
                            "volume": position,
                        }
                    )
                    position = 0

            # 计算权益
            equity = cash + position * price
            last_price = price
            equity_curve.append(equity)

        # 收盘强平
        if position > 0:
            proceeds = position * last_price * (1 - self.commission_rate)
            cash += proceeds
            self.trades.append(
                {
                    "timestamp": self.data.index[-1],
                    "type": "SELL",
                    "price": last_price,
                    "volume": position,
                }
            )
            position = 0
            equity_curve[-1] = cash

        self.equity_curve = pd.Series(equity_curve, index=self.data.index)
        self.final_cash = cash

    def _calculate_metrics(self) -> BacktestResult:
        logger.info("计算绩效指标")
        pnl = self.equity_curve - self.initial_capital
        returns = self.equity_curve.pct_change().fillna(0)
        cumulative_return = (self.equity_curve.iloc[-1] / self.initial_capital) - 1
        annual_return = (1 + cumulative_return) ** (252 / len(self.equity_curve)) - 1
        max_drawdown = (
            (self.equity_curve.cummax() - self.equity_curve)
            / self.equity_curve.cummax()
        ).max()
        sharpe = np.sqrt(252) * returns.mean() / (returns.std() + 1e-9)

        # Sortino Ratio (下行风险)
        downside_returns = returns.copy()
        downside_returns[downside_returns > 0] = 0
        downside_std = downside_returns.std() * np.sqrt(252)
        sortino = np.sqrt(252) * returns.mean() / (downside_std + 1e-9)

        # Calmar Ratio (年化收益 / 最大回撤)
        calmar = annual_return / (max_drawdown + 1e-9)

        # 月度收益统计
        monthly_returns = self.equity_curve.resample("M").last().pct_change().dropna()
        monthly_return_mean = monthly_returns.mean()
        monthly_return_std = monthly_returns.std()

        metrics = {
            "total_return": round(cumulative_return, 4),
            "annual_return": round(annual_return, 4),
            "max_drawdown": round(max_drawdown, 4),
            "sharpe_ratio": round(sharpe, 4),
            "sortino_ratio": round(sortino, 4),
            "calmar_ratio": round(calmar, 4),
            "volatility": round(returns.std() * np.sqrt(252), 4),
            "monthly_return_mean": (
                round(monthly_return_mean, 4) if not monthly_returns.empty else 0
            ),
            "monthly_return_std": (
                round(monthly_return_std, 4) if not monthly_returns.empty else 0
            ),
            "trades": len(self.trades),
        }

        return BacktestResult(
            equity_curve=self.equity_curve,
            trades=self.trades,
            metrics=metrics,
        )
