#!/usr/bin/env python3
"""
数据源状态检查和修复工具
检查Tushare、AkShare等数据源的配置和可用性
"""

import os
import sys
import asyncio
import importlib
from pathlib import Path

def check_environment_config():
    """检查环境配置"""
    print("🔧 检查环境配置...")
    
    # 检查.env文件
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("❌ backend/.env 文件不存在")
        return False
    
    # 读取配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查Tushare配置
    if "TUSHARE_TOKEN=your_tushare_token_here" in content:
        print("⚠️ Tushare Token 未配置（仍为默认值）")
        tushare_configured = False
    elif "TUSHARE_TOKEN=" in content:
        print("✅ Tushare Token 已配置")
        tushare_configured = True
    else:
        print("❌ 未找到 TUSHARE_TOKEN 配置")
        tushare_configured = False
    
    # 检查数据源设置
    if "MARKET_DATA_SOURCE=tushare" in content:
        print("✅ 数据源设置为 Tushare")
    elif "MARKET_DATA_SOURCE=mock" in content:
        print("⚠️ 数据源设置为 Mock（模拟数据）")
    else:
        print("⚠️ 数据源配置不明确")
    
    return tushare_configured

def check_dependencies():
    """检查依赖库安装情况"""
    print("\n📦 检查依赖库...")
    
    dependencies = {
        'tushare': 'Tushare数据源',
        'akshare': 'AkShare数据源',
        'pandas': 'Pandas数据处理',
        'numpy': 'NumPy数值计算',
        'aiohttp': '异步HTTP客户端'
    }
    
    results = {}
    
    for lib, desc in dependencies.items():
        try:
            module = importlib.import_module(lib)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {desc}: {lib} v{version}")
            results[lib] = True
        except ImportError:
            print(f"❌ {desc}: {lib} 未安装")
            results[lib] = False
    
    return results

async def test_tushare_connection():
    """测试Tushare连接"""
    print("\n🔗 测试Tushare连接...")
    
    try:
        # 尝试导入Tushare服务
        sys.path.append('backend')
        from app.services.tushare_data_service import TushareDataService
        from app.core.config import settings
        
        # 检查Token
        if not settings.TUSHARE_TOKEN or settings.TUSHARE_TOKEN == "your_tushare_token_here":
            print("❌ Tushare Token 未正确配置")
            return False
        
        # 测试连接
        async with TushareDataService() as service:
            # 尝试获取股票列表
            result = await service.get_stock_list(limit=5)
            if result and len(result) > 0:
                print(f"✅ Tushare连接成功，获取到 {len(result)} 只股票")
                return True
            else:
                print("⚠️ Tushare连接成功但未获取到数据")
                return False
                
    except Exception as e:
        print(f"❌ Tushare连接失败: {e}")
        return False

def test_akshare_availability():
    """测试AkShare可用性"""
    print("\n📊 测试AkShare可用性...")
    
    try:
        import akshare as ak
        
        # 尝试获取简单数据
        df = ak.stock_zh_a_spot_em()
        if df is not None and len(df) > 0:
            print(f"✅ AkShare可用，获取到 {len(df)} 只股票数据")
            return True
        else:
            print("⚠️ AkShare可用但未获取到数据")
            return False
            
    except ImportError:
        print("❌ AkShare 未安装")
        return False
    except Exception as e:
        print(f"❌ AkShare 测试失败: {e}")
        return False

def check_service_layer():
    """检查服务层状态"""
    print("\n🏗️ 检查服务层...")
    
    backend_path = Path("backend/app/services")
    if not backend_path.exists():
        print("❌ 服务层目录不存在")
        return False
    
    # 统计服务文件
    service_files = list(backend_path.glob("*service*.py"))
    print(f"📁 发现 {len(service_files)} 个服务文件")
    
    # 检查关键服务
    key_services = [
        "market_service.py",
        "tushare_data_service.py", 
        "akshare_data_service.py",
        "mock_market_service.py"
    ]
    
    for service in key_services:
        service_path = backend_path / service
        if service_path.exists():
            size_kb = service_path.stat().st_size / 1024
            print(f"  ✅ {service} ({size_kb:.1f}KB)")
        else:
            print(f"  ❌ {service} 缺失")
    
    # 检查是否有重复服务
    market_services = [f for f in service_files if 'market' in f.name.lower()]
    if len(market_services) > 3:
        print(f"⚠️ 发现 {len(market_services)} 个市场相关服务，可能存在重复")
        for service in market_services:
            print(f"    - {service.name}")
    
    return True

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n💡 修复建议:")
    
    print("1. 🔑 配置Tushare Token:")
    print("   - 访问 https://tushare.pro/ 注册账号")
    print("   - 获取API Token")
    print("   - 修改 backend/.env 文件中的 TUSHARE_TOKEN")
    
    print("\n2. 📦 安装缺失依赖:")
    print("   cd backend")
    print("   pip install -r requirements.txt")
    
    print("\n3. 🔧 激活真实数据源:")
    print("   修改 backend/.env:")
    print("   MARKET_DATA_SOURCE=tushare")
    print("   USE_REAL_DATA=true")
    print("   USE_MOCK_DATA=false")
    
    print("\n4. 🧹 清理重复服务:")
    print("   - 合并功能重复的服务文件")
    print("   - 统一数据源接口")
    print("   - 优化导入依赖")

async def main():
    """主函数"""
    print("🚀 开始数据源状态检查...\n")
    
    # 检查环境配置
    env_ok = check_environment_config()
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    # 检查服务层
    service_ok = check_service_layer()
    
    # 测试数据源连接
    if deps_ok.get('tushare', False):
        tushare_ok = await test_tushare_connection()
    else:
        tushare_ok = False
    
    if deps_ok.get('akshare', False):
        akshare_ok = test_akshare_availability()
    else:
        akshare_ok = False
    
    # 总结
    print("\n📊 检查结果总结:")
    print(f"环境配置: {'✅' if env_ok else '❌'}")
    print(f"依赖库: {'✅' if all(deps_ok.values()) else '⚠️'}")
    print(f"服务层: {'✅' if service_ok else '❌'}")
    print(f"Tushare: {'✅' if tushare_ok else '❌'}")
    print(f"AkShare: {'✅' if akshare_ok else '❌'}")
    
    # 计算总体状态
    total_score = sum([env_ok, all(deps_ok.values()), service_ok, tushare_ok, akshare_ok])
    print(f"\n🎯 总体状态: {total_score}/5")
    
    if total_score >= 4:
        print("🎉 数据源状态良好，可以使用真实数据！")
    elif total_score >= 2:
        print("⚠️ 数据源部分可用，建议进行优化")
    else:
        print("❌ 数据源存在严重问题，需要立即修复")
    
    # 生成修复建议
    generate_fix_recommendations()

if __name__ == "__main__":
    asyncio.run(main())
