"""
数据源健康检查和监控服务
实时监控各数据源的可用性、响应时间和数据质量
"""

import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass, asdict
from enum import Enum
import json
from pathlib import Path

from app.core.config import settings

logger = logging.getLogger(__name__)

class DataSourceStatus(Enum):
    """数据源状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class HealthCheckResult:
    """健康检查结果"""
    source_name: str
    status: DataSourceStatus
    response_time_ms: float
    success_rate: float
    error_message: Optional[str] = None
    data_freshness: Optional[int] = None  # 数据新鲜度(分钟)
    last_check: datetime = None
    check_count: int = 0
    
    def __post_init__(self):
        if self.last_check is None:
            self.last_check = datetime.now()

@dataclass
class DataSourceMetrics:
    """数据源指标"""
    source_name: str
    total_checks: int = 0
    successful_checks: int = 0
    failed_checks: int = 0
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    min_response_time: float = float('inf')
    uptime_percentage: float = 100.0
    last_error: Optional[str] = None
    first_check_time: Optional[datetime] = None
    last_check_time: Optional[datetime] = None

class DataSourceMonitor:
    """数据源监控器"""
    
    def __init__(self):
        self.health_results: Dict[str, HealthCheckResult] = {}
        self.metrics: Dict[str, DataSourceMetrics] = {}
        self.monitoring = False
        self.check_interval = 60  # 秒
        self.timeout = 10  # API超时时间
        
        # 报告存储
        self.report_dir = Path("data/monitoring")
        self.report_dir.mkdir(parents=True, exist_ok=True)
    
    async def check_tushare_health(self) -> HealthCheckResult:
        """检查Tushare数据源健康状态"""
        start_time = time.time()
        
        try:
            if not settings.TUSHARE_API_TOKEN:
                return HealthCheckResult(
                    source_name="tushare",
                    status=DataSourceStatus.UNHEALTHY,
                    response_time_ms=0,
                    success_rate=0.0,
                    error_message="Token未配置"
                )
            
            # 测试API连接
            data = {
                "api_name": "stock_basic",
                "token": settings.TUSHARE_API_TOKEN,
                "params": {"exchange": "SSE", "list_status": "L"},
                "fields": "ts_code,symbol,name"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post("http://api.tushare.pro", json=data) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get('code') == 0:
                            items = result.get('data', {}).get('items', [])
                            if items:
                                return HealthCheckResult(
                                    source_name="tushare",
                                    status=DataSourceStatus.HEALTHY,
                                    response_time_ms=response_time,
                                    success_rate=100.0,
                                    data_freshness=0  # 实时数据
                                )
                            else:
                                return HealthCheckResult(
                                    source_name="tushare",
                                    status=DataSourceStatus.DEGRADED,
                                    response_time_ms=response_time,
                                    success_rate=50.0,
                                    error_message="返回数据为空"
                                )
                        else:
                            return HealthCheckResult(
                                source_name="tushare",
                                status=DataSourceStatus.UNHEALTHY,
                                response_time_ms=response_time,
                                success_rate=0.0,
                                error_message=result.get('msg', 'API错误')
                            )
                    else:
                        return HealthCheckResult(
                            source_name="tushare",
                            status=DataSourceStatus.UNHEALTHY,
                            response_time_ms=response_time,
                            success_rate=0.0,
                            error_message=f"HTTP {response.status}"
                        )
                        
        except asyncio.TimeoutError:
            return HealthCheckResult(
                source_name="tushare",
                status=DataSourceStatus.UNHEALTHY,
                response_time_ms=(time.time() - start_time) * 1000,
                success_rate=0.0,
                error_message="请求超时"
            )
        except Exception as e:
            return HealthCheckResult(
                source_name="tushare",
                status=DataSourceStatus.UNHEALTHY,
                response_time_ms=(time.time() - start_time) * 1000,
                success_rate=0.0,
                error_message=str(e)
            )
    
    async def check_akshare_health(self) -> HealthCheckResult:
        """检查AKShare数据源健康状态"""
        start_time = time.time()
        
        try:
            # 动态导入akshare
            import akshare as ak
            
            # 测试获取股票列表
            df = ak.stock_info_a_code_name()
            response_time = (time.time() - start_time) * 1000
            
            if not df.empty:
                return HealthCheckResult(
                    source_name="akshare",
                    status=DataSourceStatus.HEALTHY,
                    response_time_ms=response_time,
                    success_rate=100.0,
                    data_freshness=5  # 估计5分钟延迟
                )
            else:
                return HealthCheckResult(
                    source_name="akshare",
                    status=DataSourceStatus.DEGRADED,
                    response_time_ms=response_time,
                    success_rate=50.0,
                    error_message="返回数据为空"
                )
                
        except ImportError:
            return HealthCheckResult(
                source_name="akshare",
                status=DataSourceStatus.UNHEALTHY,
                response_time_ms=0,
                success_rate=0.0,
                error_message="AKShare未安装"
            )
        except Exception as e:
            return HealthCheckResult(
                source_name="akshare",
                status=DataSourceStatus.UNHEALTHY,
                response_time_ms=(time.time() - start_time) * 1000,
                success_rate=0.0,
                error_message=str(e)
            )
    
    async def check_database_health(self) -> HealthCheckResult:
        """检查数据库健康状态"""
        start_time = time.time()
        
        try:
            # 这里应该检查实际的数据库连接
            # 暂时返回模拟结果
            await asyncio.sleep(0.1)  # 模拟数据库查询
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                source_name="database",
                status=DataSourceStatus.HEALTHY,
                response_time_ms=response_time,
                success_rate=100.0,
                data_freshness=0
            )
        except Exception as e:
            return HealthCheckResult(
                source_name="database",
                status=DataSourceStatus.UNHEALTHY,
                response_time_ms=(time.time() - start_time) * 1000,
                success_rate=0.0,
                error_message=str(e)
            )
    
    async def run_health_checks(self) -> Dict[str, HealthCheckResult]:
        """运行所有健康检查"""
        checks = [
            self.check_tushare_health(),
            self.check_akshare_health(),
            self.check_database_health(),
        ]
        
        results = await asyncio.gather(*checks, return_exceptions=True)
        
        health_results = {}
        for result in results:
            if isinstance(result, HealthCheckResult):
                health_results[result.source_name] = result
                self.health_results[result.source_name] = result
                self._update_metrics(result)
            elif isinstance(result, Exception):
                logger.error(f"健康检查异常: {result}")
        
        return health_results
    
    def _update_metrics(self, result: HealthCheckResult):
        """更新指标"""
        source_name = result.source_name
        
        if source_name not in self.metrics:
            self.metrics[source_name] = DataSourceMetrics(
                source_name=source_name,
                first_check_time=datetime.now()
            )
        
        metrics = self.metrics[source_name]
        metrics.total_checks += 1
        metrics.last_check_time = datetime.now()
        
        if result.status == DataSourceStatus.HEALTHY:
            metrics.successful_checks += 1
        else:
            metrics.failed_checks += 1
            metrics.last_error = result.error_message
        
        # 更新响应时间统计
        metrics.avg_response_time = (
            (metrics.avg_response_time * (metrics.total_checks - 1) + result.response_time_ms) 
            / metrics.total_checks
        )
        metrics.max_response_time = max(metrics.max_response_time, result.response_time_ms)
        metrics.min_response_time = min(metrics.min_response_time, result.response_time_ms)
        
        # 更新可用性百分比
        metrics.uptime_percentage = (metrics.successful_checks / metrics.total_checks) * 100
    
    async def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        logger.info("数据源监控已启动")
        
        while self.monitoring:
            try:
                await self.run_health_checks()
                logger.info("完成一轮健康检查")
                
                # 生成报告
                await self.generate_report()
                
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"监控异常: {e}")
                await asyncio.sleep(5)  # 短暂等待后继续
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("数据源监控已停止")
    
    async def generate_report(self):
        """生成监控报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "health_results": {
                name: asdict(result) for name, result in self.health_results.items()
            },
            "metrics": {
                name: asdict(metrics) for name, metrics in self.metrics.items()
            },
            "overall_status": self._calculate_overall_status()
        }
        
        # 序列化datetime对象
        def json_serial(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Type {type(obj)} not serializable")
        
        report_file = self.report_dir / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=json_serial)
    
    def _calculate_overall_status(self) -> Dict[str, Any]:
        """计算整体状态"""
        if not self.health_results:
            return {"status": "unknown", "healthy_sources": 0, "total_sources": 0}
        
        healthy_count = sum(
            1 for result in self.health_results.values() 
            if result.status == DataSourceStatus.HEALTHY
        )
        total_count = len(self.health_results)
        
        if healthy_count == total_count:
            overall_status = "healthy"
        elif healthy_count > 0:
            overall_status = "degraded"
        else:
            overall_status = "unhealthy"
        
        avg_response_time = sum(
            result.response_time_ms for result in self.health_results.values()
        ) / len(self.health_results)
        
        return {
            "status": overall_status,
            "healthy_sources": healthy_count,
            "total_sources": total_count,
            "availability_percentage": (healthy_count / total_count) * 100,
            "avg_response_time_ms": avg_response_time
        }
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            "health_results": {
                name: asdict(result) for name, result in self.health_results.items()
            },
            "metrics": {
                name: asdict(metrics) for name, metrics in self.metrics.items()
            },
            "overall_status": self._calculate_overall_status(),
            "monitoring_active": self.monitoring
        }

# 全局实例
data_source_monitor = DataSourceMonitor()

# 便捷函数
async def check_all_data_sources() -> Dict[str, HealthCheckResult]:
    """检查所有数据源健康状态"""
    return await data_source_monitor.run_health_checks()

async def get_data_source_status() -> Dict[str, Any]:
    """获取数据源状态"""
    return data_source_monitor.get_current_status()

def start_data_source_monitoring():
    """启动数据源监控"""
    asyncio.create_task(data_source_monitor.start_monitoring())

def stop_data_source_monitoring():
    """停止数据源监控"""
    data_source_monitor.stop_monitoring()