"""
统一市场数据API
整合所有市场相关的端点，解决路由重复和命名不一致问题
支持可配置的认证和限流
"""
from fastapi import APIRouter, Query, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from typing import Optional, List, Dict, Any
from datetime import datetime
import logging

from app.core.unified_cache import unified_cache, CacheType
from app.core.optional_auth import OptionalAuth, StrictAuth, AuthConfig
from app.middleware.rate_limit_middleware import market_rate_limiter
from app.services.cache_warmup_service import cache_warmup_service
try:
    from app.services.enhanced_historical_manager import EnhancedHistoricalDataManager
    enhanced_historical_manager = EnhancedHistoricalDataManager()
except ImportError:
    enhanced_historical_manager = None
try:
    from app.services.scheduler_service import scheduler_service
except ImportError:
    scheduler_service = None

try:
    from app.services.market_facade_service import market_facade
except ImportError:
    market_facade = None

try:
    from app.services.mock_market_service import mock_market_service
except ImportError:
    from app.services.market_service_simplified import simplified_market_service as mock_market_service

logger = logging.getLogger(__name__)

router = APIRouter()


async def check_rate_limit_and_auth(request: Request, user=None):
    """检查限流和认证的辅助函数"""
    # 检查限流
    user_id = str(user.id) if user else None
    rate_limit_response = await market_rate_limiter.check_rate_limit(request, user_id)
    if rate_limit_response:
        return rate_limit_response
    return None


# ============ 市场概览接口 ============

@router.get("/overview")
async def get_market_overview():
    """
    获取市场概览

    返回市场整体状态、涨跌统计、主要指数等
    """
    cache_key = "market_overview"
    
    try:
        # 初始化缓存管理器（如果尚未初始化）
        await unified_cache.initialize()
        
        # 尝试从缓存获取
        cached_data = await unified_cache.get(CacheType.REALTIME, cache_key)
        if cached_data:
            logger.debug("市场概览数据来自缓存")
            return {
                "success": True,
                "data": cached_data,
                "timestamp": datetime.now().isoformat(),
                "source": "cache"
            }
        
        # 缓存未命中，获取新数据
        if mock_market_service:
            overview = await mock_market_service.get_market_overview()
        else:
            # 提供基础的市场概览数据
            overview = {
                "timestamp": datetime.now().isoformat(),
                "indices": {
                    "000001": {"name": "上证指数", "currentPrice": 3245.68, "change": 12.45, "changePercent": 0.38},
                    "399001": {"name": "深证成指", "currentPrice": 10856.34, "change": -23.67, "changePercent": -0.22}
                },
                "stats": {"advancers": 1245, "decliners": 987, "unchanged": 234, "total": 2466}
            }

        # 缓存数据 (30秒TTL)
        await unified_cache.set(CacheType.REALTIME, overview, cache_key, expire=30)
        
        return {
            "success": True,
            "data": overview,
            "timestamp": datetime.now().isoformat(),
            "source": "live"
        }
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")


@router.get("/stocks")
async def get_stock_list(
    market: Optional[str] = Query(None, description="市场代码(SH/SZ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    page: int = Query(1, description="页码", ge=1),
    pageSize: int = Query(20, description="每页数量", ge=1, le=100)
):
    """
    获取股票列表

    - **market**: 市场代码，如SH、SZ
    - **industry**: 行业分类
    - **page**: 页码
    - **pageSize**: 每页数量
    """
    # 构建缓存key
    cache_key = f"stock_list:{market or 'all'}:{industry or 'all'}:{page}:{pageSize}"
    
    try:
        # 尝试从缓存获取
        cached_data = await unified_cache.get(CacheType.STOCK_INFO, cache_key)
        if cached_data:
            logger.debug("股票列表数据来自缓存")
            return {
                "success": True,
                "data": cached_data["data"],
                "pagination": cached_data["pagination"],
                "timestamp": datetime.now().isoformat(),
                "source": "cache"
            }
        # 使用mock服务获取股票列表
        if mock_market_service:
            stocks = await mock_market_service.get_stock_list()
        else:
            # 提供基础的股票列表
            stocks = [
                {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行", "currentPrice": 12.5, "change": 0.15, "changePercent": 1.2, "volume": 1000000, "amount": 12500000},
                {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产", "currentPrice": 15.8, "change": -0.25, "changePercent": -1.5, "volume": 2000000, "amount": 31600000},
                {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行", "currentPrice": 38.2, "change": 0.8, "changePercent": 2.1, "volume": 3000000, "amount": 114600000},
                {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料", "currentPrice": 1680.0, "change": -15.0, "changePercent": -0.9, "volume": 100000, "amount": 168000000}
            ]

        # 根据参数筛选
        filtered_stocks = stocks
        if market:
            filtered_stocks = [s for s in filtered_stocks if s.get('market', '').upper() == market.upper()]
        if industry:
            filtered_stocks = [s for s in filtered_stocks if s.get('industry') == industry]

        # 分页
        total = len(filtered_stocks)
        start = (page - 1) * pageSize
        end = start + pageSize
        page_stocks = filtered_stocks[start:end]

        # 构建返回数据
        result_data = {
            "data": page_stocks,
            "pagination": {
                "page": page,
                "pageSize": pageSize,
                "total": total,
                "totalPages": (total + pageSize - 1) // pageSize
            }
        }
        
        # 缓存结果 (1小时TTL)
        await unified_cache.set(CacheType.STOCK_INFO, result_data, cache_key, expire=3600)

        return {
            "success": True,
            "data": page_stocks,
            "pagination": result_data["pagination"],
            "timestamp": datetime.now().isoformat(),
            "source": "live"
        }
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")


# ============ 实时行情接口 ============

@router.get("/quotes/realtime")
async def get_realtime_quotes(
    request: Request,
    symbols: str = Query(..., description="股票代码，多个用逗号分隔"),
    current_user=OptionalAuth
):
    """
    获取实时行情

    - **symbols**: 股票代码列表，如"000001,000002,600036"
    """
    # 检查限流
    rate_limit_response = await check_rate_limit_and_auth(request, current_user)
    if rate_limit_response:
        return rate_limit_response

    symbol_list = [s.strip() for s in symbols.split(",") if s.strip()]

    if not symbol_list:
        raise HTTPException(status_code=400, detail="请提供股票代码")
    
    # 构建缓存key
    cache_key = f"realtime_quotes:{':'.join(sorted(symbol_list))}"
    
    try:
        # 尝试从缓存获取
        cached_data = await unified_cache.get(CacheType.REALTIME, cache_key)
        if cached_data:
            logger.debug("实时行情数据来自缓存")
            return {
                "success": True,
                "data": cached_data,
                "timestamp": datetime.now().isoformat(),
                "source": "cache"
            }
    except Exception as e:
        logger.warning(f"缓存获取失败: {e}")

    # 使用统一服务门面或备用实现
    quotes = []
    if market_facade:
        quotes = await market_facade.get_batch_quotes(symbol_list)
    elif mock_market_service:
        for symbol in symbol_list:
            quote = await mock_market_service.get_realtime_quote(symbol)
            if quote:
                quotes.append(quote)
    else:
        # 基础备用数据
        for symbol in symbol_list:
            quotes.append({
                "symbol": symbol,
                "name": f"股票{symbol}",
                "current_price": 10.0,
                "change": 0.1,
                "change_percent": 1.0,
                "volume": 1000000,
                "timestamp": datetime.now()
            })

    # 转换为API响应格式
    quote_data = []
    for quote in quotes:
        if hasattr(quote, 'symbol'):
            # 对象形式的数据
            quote_data.append({
                "symbol": quote.symbol,
                "name": getattr(quote, 'name', f"股票{quote.symbol}"),
                "currentPrice": getattr(quote, 'current_price', getattr(quote, 'currentPrice', 0)),
                "change": getattr(quote, 'change', 0),
                "changePercent": getattr(quote, 'change_percent', getattr(quote, 'changePercent', 0)),
                "volume": getattr(quote, 'volume', 0),
                "timestamp": getattr(quote, 'timestamp', datetime.now()).isoformat() if hasattr(getattr(quote, 'timestamp', None), 'isoformat') else str(getattr(quote, 'timestamp', datetime.now().isoformat()))
            })
        else:
            # 字典形式的数据
            quote_data.append({
                "symbol": quote.get("symbol", ""),
                "name": quote.get("name", f"股票{quote.get('symbol', '')}"),
                "currentPrice": quote.get("currentPrice", quote.get("current_price", 0)),
                "change": quote.get("change", 0),
                "changePercent": quote.get("changePercent", quote.get("change_percent", 0)),
                "volume": quote.get("volume", 0),
                "timestamp": quote.get("timestamp", datetime.now().isoformat())
            })

    # 构建返回数据
    result_data = {
        "quotes": quote_data,
        "count": len(quote_data)
    }
    
    # 缓存结果 (30秒TTL)
    try:
        await unified_cache.set(CacheType.REALTIME, result_data, cache_key, expire=30)
    except Exception as e:
        logger.warning(f"缓存设置失败: {e}")

    # 构建响应
    response_data = {
        "success": True,
        "data": result_data,
        "timestamp": datetime.now().isoformat(),
        "source": "live"
    }

    # 创建响应并添加限流头部
    response = JSONResponse(content=response_data)
    market_rate_limiter.add_rate_limit_headers(response, request)
    return response


@router.get("/quotes/{symbol}")
async def get_quote_detail(symbol: str):
    """
    获取单个股票详细行情

    - **symbol**: 股票代码
    """
    try:
        # 直接使用Mock服务确保有数据返回
        from app.services.mock_market_service import mock_market_service
        quote = await mock_market_service.get_realtime_quote(symbol)

        if not quote:
            raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的行情数据")

        # 直接返回Mock服务的数据格式
        return {
            "success": True,
            "data": quote
        }
    except Exception as e:
        logger.error(f"获取股票行情失败: {symbol}, 错误: {e}")
        raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的行情数据")


# ============ K线数据接口 ============

@router.get("/kline/{symbol}")
async def get_kline_data(
    symbol: str,
    period: str = Query("1d", pattern="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
):
    """
    获取K线数据

    - **symbol**: 股票代码
    - **period**: K线周期(1m/5m/15m/30m/1h/1d/1w/1M)
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **limit**: 返回数量
    """
    # 解析日期参数
    start_dt = None
    end_dt = None
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date)
        except ValueError:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date)
        except ValueError:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

    # 使用统一服务门面或备用实现
    klines = []
    if market_facade:
        klines = await market_facade.get_kline_data(
            symbol=symbol,
            period=period,
            start_date=start_dt,
            end_date=end_dt,
            limit=limit
        )
    else:
        # 生成基础K线数据
        import random
        base_price = 10.0
        for i in range(min(limit, 100)):
            timestamp = int((datetime.now() - timedelta(days=i)).timestamp())
            open_price = base_price * random.uniform(0.99, 1.01)
            high_price = open_price * random.uniform(1.0, 1.02)
            low_price = open_price * random.uniform(0.98, 1.0)
            close_price = open_price * random.uniform(0.99, 1.01)
            
            klines.append({
                "timestamp": timestamp,
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(100000, 1000000),
                "amount": random.randint(1000000, 10000000)
            })
        klines.reverse()  # 时间正序

    # 转换为API响应格式
    kline_data = []
    for kline in klines:
        kline_data.append({
            "timestamp": kline.timestamp,
            "open": kline.open,
            "high": kline.high,
            "low": kline.low,
            "close": kline.close,
            "volume": kline.volume,
            "amount": kline.amount
        })

    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "period": period,
            "klines": kline_data,
            "count": len(kline_data)
        },
        "timestamp": datetime.now().isoformat()
    }


# ============ 历史数据接口 ============

@router.get("/historical/stats")
async def get_historical_stats():
    """
    获取历史数据统计信息
    """
    try:
        # 基础统计数据
        stats = {
            "total_stocks": 4500,
            "markets": {
                "SH": 1800,
                "SZ": 2500, 
                "BJ": 200
            },
            "industries": {
                "银行": 40,
                "房地产": 130,
                "食品饮料": 85,
                "电子": 200,
                "计算机": 180,
                "医药生物": 160
            },
            "data_range": {
                "start_date": "2015-01-01",
                "end_date": "2025-01-01"
            },
            "total_records": 15000000,
            "last_updated": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取历史统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史统计失败: {str(e)}")

@router.get("/historical/hot-stocks")
async def get_hot_stocks(
    category: str = Query(..., description="分类"),
    limit: int = Query(50, description="返回条数")
):
    """
    获取热门股票
    """
    try:
        # 基础热门股票数据
        hot_stocks = {
            "热门股票": [
                {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
                {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
                {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料"},
                {"symbol": "000858", "name": "五粮液", "market": "SZ", "industry": "食品饮料"}
            ],
            "银行股": [
                {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
                {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
                {"symbol": "600000", "name": "浦发银行", "market": "SH", "industry": "银行"}
            ],
            "科技股": [
                {"symbol": "000063", "name": "中兴通讯", "market": "SZ", "industry": "计算机"},
                {"symbol": "002415", "name": "海康威视", "market": "SZ", "industry": "计算机"},
                {"symbol": "300059", "name": "东方财富", "market": "SZ", "industry": "计算机"}
            ],
            "白酒股": [
                {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料"},
                {"symbol": "000858", "name": "五粮液", "market": "SZ", "industry": "食品饮料"}
            ]
        }
        
        stocks = hot_stocks.get(category, hot_stocks["热门股票"])[:limit]
        
        return {
            "success": True,
            "data": stocks,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取热门股票失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取热门股票失败: {str(e)}")

@router.get("/overview")
async def market_overview_endpoint():
    """
    获取市场概览
    
    返回市场整体状态、涨跌统计、主要指数等
    """
    cache_key = "market_overview"
    
    try:
        # 初始化缓存管理器（如果尚未初始化）
        await unified_cache.initialize()
        
        # 尝试从缓存获取
        cached_data = await unified_cache.get(CacheType.REALTIME, cache_key)
        if cached_data:
            logger.debug("市场概览数据来自缓存")
            return {
                "success": True,
                "data": cached_data,
                "timestamp": datetime.now().isoformat(),
                "source": "cache"
            }
        
        # 缓存未命中，获取新数据
        if mock_market_service:
            overview = await mock_market_service.get_market_overview()
        else:
            # 提供基础的市场概览数据
            overview = {
                "timestamp": datetime.now().isoformat(),
                "indices": {
                    "000001": {"name": "上证指数", "currentPrice": 3245.68, "change": 12.45, "changePercent": 0.38},
                    "399001": {"name": "深证成指", "currentPrice": 10856.34, "change": -23.67, "changePercent": -0.22}
                },
                "stats": {"advancers": 1245, "decliners": 987, "unchanged": 234, "total": 2466}
            }

        # 缓存数据 (30秒TTL)
        await unified_cache.set(CacheType.REALTIME, overview, cache_key, expire=30)
        
        return {
            "success": True,
            "data": overview,
            "timestamp": datetime.now().isoformat(),
            "source": "live"
        }
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")

@router.get("/stocks")
async def stock_list_endpoint(
    market: Optional[str] = Query(None, description="市场代码(SH/SZ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    page: int = Query(1, description="页码", ge=1),
    pageSize: int = Query(20, description="每页数量", ge=1, le=100)
):
    """
    获取股票列表
    
    - **market**: 市场代码，如SH、SZ
    - **industry**: 行业分类
    - **page**: 页码
    - **pageSize**: 每页数量
    """
    # 构建缓存key
    cache_key = f"stock_list:{market or 'all'}:{industry or 'all'}:{page}:{pageSize}"
    
    try:
        # 尝试从缓存获取
        cached_data = await unified_cache.get(CacheType.STOCK_INFO, cache_key)
        if cached_data:
            logger.debug("股票列表数据来自缓存")
            return {
                "success": True,
                "data": cached_data["data"],
                "pagination": cached_data["pagination"],
                "timestamp": datetime.now().isoformat(),
                "source": "cache"
            }
        # 使用mock服务获取股票列表
        if mock_market_service:
            stocks = await mock_market_service.get_stock_list()
        else:
            # 提供基础的股票列表
            stocks = [
                {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行", "currentPrice": 12.5, "change": 0.15, "changePercent": 1.2, "volume": 1000000, "amount": 12500000},
                {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产", "currentPrice": 15.8, "change": -0.25, "changePercent": -1.5, "volume": 2000000, "amount": 31600000},
                {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行", "currentPrice": 38.2, "change": 0.8, "changePercent": 2.1, "volume": 3000000, "amount": 114600000},
                {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料", "currentPrice": 1680.0, "change": -15.0, "changePercent": -0.9, "volume": 100000, "amount": 168000000}
            ]

        # 根据参数筛选
        filtered_stocks = stocks
        if market:
            filtered_stocks = [s for s in filtered_stocks if s.get('market', '').upper() == market.upper()]
        if industry:
            filtered_stocks = [s for s in filtered_stocks if s.get('industry') == industry]

        # 分页
        total = len(filtered_stocks)
        start = (page - 1) * pageSize
        end = start + pageSize
        page_stocks = filtered_stocks[start:end]

        # 构建返回数据
        result_data = {
            "data": page_stocks,
            "pagination": {
                "page": page,
                "pageSize": pageSize,
                "total": total,
                "totalPages": (total + pageSize - 1) // pageSize
            }
        }
        
        # 缓存结果 (1小时TTL)
        await unified_cache.set(CacheType.STOCK_INFO, result_data, cache_key, expire=3600)

        return {
            "success": True,
            "data": page_stocks,
            "pagination": result_data["pagination"],
            "timestamp": datetime.now().isoformat(),
            "source": "live"
        }
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")

@router.get("/historical/stocks")
async def get_historical_stock_list(
    market: Optional[str] = Query(None, description="市场代码 (SH/SZ/BJ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    获取历史数据中的股票列表
    """
    if enhanced_historical_manager:
        if hasattr(enhanced_historical_manager, 'ensure_initialized'):
            await enhanced_historical_manager.ensure_initialized()

        result = await enhanced_historical_manager.get_stock_list(
            market=market,
            industry=industry,
            page=page,
            page_size=page_size
        )
    else:
        # 基础历史数据
        basic_stocks = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
            {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
            {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料"}
        ]
        
        # 应用筛选
        filtered = basic_stocks
        if market:
            filtered = [s for s in filtered if s["market"] == market]
        if industry:
            filtered = [s for s in filtered if s["industry"] == industry]
        
        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        result = {
            "stocks": filtered[start:end],
            "total": len(filtered),
            "total_pages": (len(filtered) + page_size - 1) // page_size
        }
    
    return {
        "success": True,
        "data": result,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/search")
async def search_historical_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    在历史数据中搜索股票
    """
    if enhanced_historical_manager:
        if hasattr(enhanced_historical_manager, 'ensure_initialized'):
            await enhanced_historical_manager.ensure_initialized()

        results = await enhanced_historical_manager.search_stocks(keyword, limit)
    else:
        # 基础搜索
        basic_stocks = [
            {"symbol": "000001", "name": "平安银行"},
            {"symbol": "000002", "name": "万科A"},
            {"symbol": "600036", "name": "招商银行"},
            {"symbol": "600519", "name": "贵州茅台"}
        ]
        
        results = []
        keyword_lower = keyword.lower()
        for stock in basic_stocks:
            if (keyword_lower in stock["symbol"].lower() or 
                keyword_lower in stock["name"].lower()):
                results.append(stock)
                if len(results) >= limit:
                    break
    
    return {
        "success": True,
        "data": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/data/{symbol}")
async def get_historical_stock_data(
    symbol: str,
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    columns: Optional[str] = Query(None, description="指定列名，逗号分隔"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    获取指定股票的历史数据
    """
    if historical_data_manager:
        if hasattr(historical_data_manager, 'initialize'):
            await historical_data_manager.initialize()
        
        column_list = columns.split(',') if columns else None
        
        df = await historical_data_manager.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            columns=column_list
        )
        
        if df is None or hasattr(df, 'empty') and df.empty:
            raise HTTPException(status_code=404, detail="未找到股票历史数据")
        
        # 转换为字典格式
        if hasattr(df, 'to_dict'):
            data = df.to_dict('records')
            columns_list = list(df.columns)
        else:
            data = df if isinstance(df, list) else [df]
            columns_list = list(data[0].keys()) if data else []
    else:
        # 生成基础历史数据
        import random
        from datetime import timedelta
        
        data = []
        base_price = 10.0
        current_date = datetime.now()
        
        for i in range(30):  # 30天数据
            date = current_date - timedelta(days=i)
            price = base_price * random.uniform(0.95, 1.05)
            
            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(price * random.uniform(0.99, 1.01), 2),
                "high": round(price * random.uniform(1.0, 1.03), 2),
                "low": round(price * random.uniform(0.97, 1.0), 2),
                "close": round(price, 2),
                "volume": random.randint(100000, 1000000),
                "amount": random.randint(1000000, 10000000)
            })
        
        data.reverse()  # 时间正序
        columns_list = ["date", "open", "high", "low", "close", "volume", "amount"]
    
    return {
        "success": True,
        "data": data,
        "count": len(data),
        "columns": columns_list,
        "symbol": symbol,
        "timestamp": datetime.now().isoformat()
    }


# ============ 搜索接口 ============

@router.get("/search")
async def search_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数")
):
    """
    搜索股票（实时+历史）
    """
    # 使用统一服务门面搜索或备用实现
    results = []
    
    if market_facade:
        results = await market_facade.search_stocks(keyword, limit)
    elif mock_market_service:
        results = await mock_market_service.search_stocks(keyword, limit)
    else:
        # 基础搜索实现
        basic_stocks = [
            {"symbol": "000001", "name": "平安银行", "currentPrice": 12.5, "changePercent": 1.2, "market": "SZ"},
            {"symbol": "000002", "name": "万科A", "currentPrice": 15.8, "changePercent": -1.5, "market": "SZ"},
            {"symbol": "600036", "name": "招商银行", "currentPrice": 38.2, "changePercent": 2.1, "market": "SH"},
            {"symbol": "600519", "name": "贵州茅台", "currentPrice": 1680.0, "changePercent": -0.9, "market": "SH"}
        ]
        
        keyword_lower = keyword.lower()
        for stock in basic_stocks:
            if (keyword_lower in stock["symbol"].lower() or 
                keyword_lower in stock["name"].lower()):
                results.append(stock)
                if len(results) >= limit:
                    break

    # 如果服务门面没有结果，尝试历史数据管理器
    if not results and historical_data_manager:
        try:
            if hasattr(historical_data_manager, 'search_stocks'):
                results = await historical_data_manager.search_stocks(keyword, limit)
        except Exception as e:
            logger.warning(f"Historical data search failed: {e}")

    return {
        "success": True,
        "data": {
            "results": results,
            "count": len(results),
            "keyword": keyword
        },
        "timestamp": datetime.now().isoformat()
    }


@router.get("/depth/{symbol}")
async def get_market_depth(symbol: str):
    """
    获取市场深度数据

    - **symbol**: 股票代码
    """
    # 使用统一服务门面或备用实现
    depth_data = None
    
    if market_facade:
        depth_data = await market_facade.get_market_depth(symbol)
    else:
        # 生成基础深度数据
        import random
        base_price = 10.0
        depth_data = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "bids": [
                {"price": round(base_price - 0.01 * (i + 1), 2), "volume": random.randint(100, 1000) * 100}
                for i in range(5)
            ],
            "asks": [
                {"price": round(base_price + 0.01 * (i + 1), 2), "volume": random.randint(100, 1000) * 100}
                for i in range(5)
            ]
        }

    if not depth_data:
        raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的深度数据")

    return {
        "success": True,
        "data": depth_data,
        "symbol": symbol,
        "timestamp": datetime.now().isoformat()
    }


# ============ 系统管理接口 ============

@router.post("/cache/clear")
async def clear_cache(
    request: Request,
    pattern: Optional[str] = Query("*", description="清除模式"),
    current_user=StrictAuth
):
    """
    清除缓存
    """
    # 检查限流（管理端点使用严格认证，已经包含用户信息）
    rate_limit_response = await check_rate_limit_and_auth(request, current_user)
    if rate_limit_response:
        return rate_limit_response

    try:
        if pattern == "*":
            # 清除所有缓存
            total_cleared = 0
            for cache_type in CacheType:
                cleared = await unified_cache.clear(cache_type)
                total_cleared += cleared
        else:
            # 根据模式清除
            total_cleared = await unified_cache.clear()

        return {
            "success": True,
            "cleared_count": total_cleared,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")


@router.post("/cache/warmup")
async def warmup_cache(
    request: Request,
    symbols: Optional[List[str]] = Query(None, description="要预热的股票代码列表"),
    current_user=StrictAuth
):
    """
    缓存预热

    预加载热门股票的实时行情和K线数据到缓存中
    """
    # 检查限流
    rate_limit_response = await check_rate_limit_and_auth(request, current_user)
    if rate_limit_response:
        return rate_limit_response

    try:
        # 启动异步预热任务
        import asyncio
        warmup_task = asyncio.create_task(cache_warmup_service.warmup_cache(symbols))

        # 不等待完成，立即返回
        return {
            "success": True,
            "message": "缓存预热任务已启动",
            "symbols_count": len(symbols) if symbols else len(cache_warmup_service._get_popular_symbols()),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动缓存预热失败: {str(e)}")


@router.get("/cache/warmup/status")
async def get_warmup_status():
    """
    获取缓存预热状态
    """
    try:
        status = cache_warmup_service.get_warmup_status()
        return {
            "success": True,
            "data": status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预热状态失败: {str(e)}")


@router.post("/historical/rebuild-index")
async def rebuild_historical_index(
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    重建历史数据索引
    """
    try:
        await historical_data_manager.initialize()
        result = await historical_data_manager.rebuild_index()
        
        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重建索引失败: {str(e)}")


@router.get("/cache/health")
async def cache_health_check():
    """
    缓存健康检查

    检查缓存系统状态、连接性和性能指标
    """
    try:
        # 初始化缓存（如果尚未初始化）
        await unified_cache.initialize()

        # 测试缓存读写功能
        test_key = "health_check_test"
        test_value = {"timestamp": datetime.now().isoformat(), "test": True}

        # 写入测试
        write_success = await unified_cache.set(CacheType.REALTIME, test_value, test_key, expire=60)

        # 读取测试
        read_result = await unified_cache.get(CacheType.REALTIME, test_key)
        read_success = read_result is not None

        # 删除测试数据
        await unified_cache.delete(CacheType.REALTIME, test_key)

        # 获取缓存统计信息
        try:
            cache_stats = await unified_cache.get_stats()
        except Exception as stats_error:
            logger.warning(f"Failed to get cache stats: {stats_error}")
            cache_stats = {"error": str(stats_error)}

        return {
            "success": True,
            "status": "healthy",
            "functionality_test": {
                "write_success": write_success,
                "read_success": read_success,
                "overall_success": write_success and read_success
            },
            "cache_stats": cache_stats,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/health")
async def health_check():
    """
    健康检查
    """
    try:
        # 检查缓存状态
        cache_stats = await unified_cache.get_stats()

        # 检查调度器状态
        scheduler_jobs = scheduler_service.get_jobs() if scheduler_service and hasattr(scheduler_service, '_initialized') and scheduler_service._initialized else []

        return {
            "success": True,
            "status": "healthy",
            "cache_stats": cache_stats,
            "scheduler_jobs_count": len(scheduler_jobs),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
