#!/usr/bin/env python3
"""
P0问题清理验证脚本
检查关键问题是否已被正确修复
"""

import os
import sys
from pathlib import Path

def verify_gitignore():
    """验证.gitignore是否包含必要规则"""
    gitignore = Path(".gitignore")
    if not gitignore.exists():
        return False, "缺少.gitignore文件"
    
    content = gitignore.read_text()
    required_patterns = [
        "backend/venv/", 
        "frontend/dist/",
        "package-lock.json"
    ]
    
    missing = [p for p in required_patterns if p not in content]
    if missing:
        return False, f"缺少忽略规则: {missing}"
    
    return True, "✅ .gitignore规则完整"

def verify_lock_files():
    """验证是否存在冲突的锁文件"""
    conflicts = [
        "frontend/package-lock.json",
        "frontend/yarn.lock", 
        "package-lock.json",
        "yarn.lock"
    ]
    
    found_conflicts = [f for f in conflicts if Path(f).exists()]
    if found_conflicts:
        return False, f"发现冲突锁文件: {found_conflicts}"
    
    return True, "✅ 无包管理器冲突"

def main():
    print("🔍 验证P0问题修复状态...")
    
    checks = [
        ("gitignore规则", verify_gitignore),
        ("锁文件冲突", verify_lock_files)
    ]
    
    all_passed = True
    for name, check_func in checks:
        try:
            passed, message = check_func()
            print(f"{name}: {message}")
            if not passed:
                all_passed = False
        except Exception as e:
            print(f"{name}: ❌ 检查失败 - {e}")
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有P0问题已修复！")
        return 0
    else:
        print("\n⚠️  仍有问题需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
