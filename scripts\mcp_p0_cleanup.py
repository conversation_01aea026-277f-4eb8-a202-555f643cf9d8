#!/usr/bin/env python3
"""
MCP P0问题清理脚本
自动化处理项目中的关键问题

P0问题清单:
1. 文档与实际文件不一致
2. Python版本要求冲突  
3. 虚拟环境入库问题
4. 构建产物入库问题
"""

import os
import shutil
import json
import sys
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

class MCPCleanupTool:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues_fixed = []
        self.errors = []
        
    def log_fix(self, issue: str, action: str, status: str = "success"):
        """记录修复操作"""
        self.issues_fixed.append({
            "issue": issue,
            "action": action,
            "status": status,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[SUCCESS] {issue}: {action}")
        
    def log_error(self, issue: str, error: str):
        """记录错误"""
        self.errors.append({
            "issue": issue,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[ERROR] {issue}: {error}")

    def fix_gitignore(self):
        """修复.gitignore文件，添加缺失的忽略规则"""
        gitignore_path = self.project_root / ".gitignore"
        
        # 需要添加的忽略规则
        additional_rules = [
            "",
            "# === MCP P0 修复: 虚拟环境和构建产物 ===",
            "# Python 虚拟环境 (项目特定)",
            "backend/venv/",
            "backend/venv310/", 
            "backend/env/",
            "venv/",
            "env/",
            "",
            "# 前端构建产物和依赖",
            "frontend/dist/",
            "frontend/node_modules/",
            "dist/",
            "node_modules/",
            "",
            "# 包管理器锁文件冲突处理",
            "package-lock.json",
            "yarn.lock",
            "",
            "# Python 编译产物",
            "*.pyc",
            "__pycache__/",
            "*.pyo",
            "*.pyd",
            ".Python",
            "*.so",
            "",
            "# 开发工具产物",
            ".vscode/settings.json",
            ".idea/",
            "*.swp",
            "*.swo",
            "*~",
            "",
            "# 日志文件",
            "*.log",
            "logs/",
            "",
            "# 数据库文件",
            "*.db",
            "*.sqlite",
            "*.sqlite3",
            "",
            "# 缓存目录",
            "cache/",
            ".cache/",
            "",
            "# 临时文件",
            "tmp/",
            "temp/",
            ".tmp/",
            "",
            "# OS 生成文件",
            ".DS_Store",
            ".DS_Store?",
            "._*",
            ".Spotlight-V100",
            ".Trashes",
            "ehthumbs.db",
            "Thumbs.db",
        ]
        
        try:
            # 读取现有内容
            current_content = ""
            if gitignore_path.exists():
                current_content = gitignore_path.read_text(encoding='utf-8')
            
            # 检查是否已经包含MCP修复标记
            if "# === MCP P0 修复" in current_content:
                self.log_fix("P0-gitignore", "规则已存在，跳过更新")
                return
                
            # 添加新规则
            updated_content = current_content + "\n" + "\n".join(additional_rules)
            gitignore_path.write_text(updated_content, encoding='utf-8')
            
            self.log_fix("P0-gitignore", f"添加了{len(additional_rules)}条忽略规则")
            
        except Exception as e:
            self.log_error("P0-gitignore", f"更新失败: {e}")

    def remove_conflicting_lock_files(self):
        """删除冲突的包管理器锁文件"""
        conflicts = [
            self.project_root / "frontend" / "package-lock.json",
            self.project_root / "frontend" / "yarn.lock",
            self.project_root / "package-lock.json",
            self.project_root / "yarn.lock"
        ]
        
        for lock_file in conflicts:
            if lock_file.exists():
                try:
                    lock_file.unlink()
                    self.log_fix("P0-lock-conflicts", f"删除冲突锁文件: {lock_file.name}")
                except Exception as e:
                    self.log_error("P0-lock-conflicts", f"删除失败 {lock_file}: {e}")

    def update_python_version_docs(self):
        """修正文档中的Python版本要求"""
        docs_to_fix = [
            {
                "file": "PROJECT_STATUS_SUMMARY.md",
                "old": "Python 3.13",
                "new": "Python 3.10.13           # 推荐Python版本 (TA-Lib/vnpy兼容性要求)"
            },
            {
                "file": "PROJECT_COMPLETION_SUMMARY.md", 
                "old": "Python 3.13",
                "new": "Python 3.10.13 (推荐版本，TA-Lib兼容)"
            },
            {
                "file": "backend/README_WINDOWS.md",
                "old": "Python 3.13.3",
                "new": "Python 3.10.13 (推荐版本)"
            }
        ]
        
        for doc in docs_to_fix:
            file_path = self.project_root / doc["file"]
            if not file_path.exists():
                continue
                
            try:
                content = file_path.read_text(encoding='utf-8')
                if doc["old"] in content:
                    updated_content = content.replace(doc["old"], doc["new"])
                    file_path.write_text(updated_content, encoding='utf-8')
                    self.log_fix("P0-python-version", f"修正 {doc['file']} 中的Python版本")
                    
            except Exception as e:
                self.log_error("P0-python-version", f"更新失败 {doc['file']}: {e}")

    def check_and_warn_venv_directories(self):
        """检查并警告虚拟环境目录"""
        venv_dirs = [
            self.project_root / "backend" / "venv",
            self.project_root / "backend" / "venv310", 
            self.project_root / "backend" / "env",
            self.project_root / "venv",
            self.project_root / "env"
        ]
        
        found_venvs = []
        for venv_dir in venv_dirs:
            if venv_dir.exists() and venv_dir.is_dir():
                found_venvs.append(str(venv_dir.relative_to(self.project_root)))
        
        if found_venvs:
            warning_msg = f"""
[WARNING] 发现虚拟环境目录已入库: {', '.join(found_venvs)}

建议手动操作:
1. git rm -r --cached {' '.join(found_venvs)}
2. git commit -m "Remove virtual environment directories from version control"

这些目录已添加到.gitignore，新的提交将自动忽略它们。
            """
            print(warning_msg)
            self.log_fix("P0-venv-warning", f"发现{len(found_venvs)}个虚拟环境目录需要手动从git中移除")

    def fix_documentation_inconsistencies(self):
        """修复文档中的文件名不一致问题"""
        completion_summary = self.project_root / "PROJECT_COMPLETION_SUMMARY.md"
        
        if not completion_summary.exists():
            return
            
        try:
            content = completion_summary.read_text(encoding='utf-8')
            
            # 需要修正的文件名映射
            fixes = {
                "main_optimized.py": "main.py (698行，功能完整的FastAPI应用)",
                "optimized-api.ts": "http.ts (支持重试、缓存、拦截器)",
                "LoginViewOptimized.vue": "LoginView.vue (支持演示登录、忘记密码)",
                "TradingViewOptimized.vue": "TradingTerminal.vue (完整交易功能)",
                "start_complete_platform.bat": "backend\\start_windows.bat",
                "start_optimized_backend.bat": "python backend\\start_backend.py"
            }
            
            updated = False
            for old_name, new_name in fixes.items():
                if old_name in content:
                    content = content.replace(old_name, new_name)
                    updated = True
                    
            if updated:
                completion_summary.write_text(content, encoding='utf-8')
                self.log_fix("P0-doc-consistency", f"修正了{len(fixes)}个文件名不一致问题")
            else:
                self.log_fix("P0-doc-consistency", "文档已是最新状态")
                
        except Exception as e:
            self.log_error("P0-doc-consistency", f"文档更新失败: {e}")

    def create_cleanup_verification_script(self):
        """创建清理验证脚本"""
        verification_script = self.project_root / "scripts" / "verify_p0_cleanup.py"
        verification_script.parent.mkdir(exist_ok=True)
        
        script_content = '''#!/usr/bin/env python3
"""
P0问题清理验证脚本
检查关键问题是否已被正确修复
"""

import os
import sys
from pathlib import Path

def verify_gitignore():
    """验证.gitignore是否包含必要规则"""
    gitignore = Path(".gitignore")
    if not gitignore.exists():
        return False, "缺少.gitignore文件"
    
    content = gitignore.read_text()
    required_patterns = [
        "backend/venv/", 
        "frontend/dist/",
        "package-lock.json"
    ]
    
    missing = [p for p in required_patterns if p not in content]
    if missing:
        return False, f"缺少忽略规则: {missing}"
    
    return True, "✅ .gitignore规则完整"

def verify_lock_files():
    """验证是否存在冲突的锁文件"""
    conflicts = [
        "frontend/package-lock.json",
        "frontend/yarn.lock", 
        "package-lock.json",
        "yarn.lock"
    ]
    
    found_conflicts = [f for f in conflicts if Path(f).exists()]
    if found_conflicts:
        return False, f"发现冲突锁文件: {found_conflicts}"
    
    return True, "✅ 无包管理器冲突"

def main():
    print("🔍 验证P0问题修复状态...")
    
    checks = [
        ("gitignore规则", verify_gitignore),
        ("锁文件冲突", verify_lock_files)
    ]
    
    all_passed = True
    for name, check_func in checks:
        try:
            passed, message = check_func()
            print(f"{name}: {message}")
            if not passed:
                all_passed = False
        except Exception as e:
            print(f"{name}: ❌ 检查失败 - {e}")
            all_passed = False
    
    if all_passed:
        print("\\n🎉 所有P0问题已修复！")
        return 0
    else:
        print("\\n⚠️  仍有问题需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
        
        try:
            verification_script.write_text(script_content, encoding='utf-8')
            self.log_fix("P0-verification", "创建验证脚本成功")
        except Exception as e:
            self.log_error("P0-verification", f"创建验证脚本失败: {e}")

    def run_all_fixes(self):
        """执行所有P0修复"""
        print("开始MCP P0问题修复...")
        print("=" * 50)
        
        # 执行所有修复操作
        self.fix_gitignore()
        self.remove_conflicting_lock_files()
        self.update_python_version_docs()
        self.fix_documentation_inconsistencies()
        self.check_and_warn_venv_directories()
        self.create_cleanup_verification_script()
        
        # 生成修复报告
        self.generate_report()

    def generate_report(self):
        """生成修复报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_fixes": len(self.issues_fixed),
            "total_errors": len(self.errors),
            "fixes": self.issues_fixed,
            "errors": self.errors
        }
        
        # 保存JSON报告
        report_file = self.project_root / "reports" / "mcp_p0_cleanup_report.json"
        report_file.parent.mkdir(exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存报告失败: {e}")
        
        # 打印摘要
        print("\n" + "=" * 50)
        print("MCP P0修复摘要")
        print("=" * 50)
        print(f"成功修复: {len(self.issues_fixed)}个问题")
        print(f"修复失败: {len(self.errors)}个问题")
        
        if self.issues_fixed:
            print("\n修复的问题:")
            for fix in self.issues_fixed:
                print(f"  - {fix['issue']}: {fix['action']}")
        
        if self.errors:
            print("\n需要手动处理的问题:")
            for error in self.errors:
                print(f"  - {error['issue']}: {error['error']}")
        
        print(f"\n详细报告: {report_file}")

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    cleaner = MCPCleanupTool(project_root)
    cleaner.run_all_fixes()

if __name__ == "__main__":
    main()