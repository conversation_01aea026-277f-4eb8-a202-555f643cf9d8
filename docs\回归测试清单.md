# 代码重复度修复 - 回归测试清单

## 🎯 测试目标
验证代码重复度修复后系统功能正常，性能提升，无回归问题。

---

## 📋 测试环境准备

### 环境要求
- [ ] 后端服务正常启动
- [ ] Redis缓存服务可用
- [ ] 前端开发服务器运行
- [ ] 测试数据准备完成

### 配置检查
- [ ] 统一缓存配置正确
- [ ] 路由注册无冲突
- [ ] 环境变量设置正确
- [ ] 日志级别适当

---

## 🔧 API接口测试

### 1. 实时行情接口

#### 1.1 批量行情 `/market/quotes/realtime`
```bash
# 测试用例1：单个股票
curl "http://localhost:8000/api/v1/market/quotes/realtime?symbols=000001"

# 测试用例2：多个股票
curl "http://localhost:8000/api/v1/market/quotes/realtime?symbols=000001,000002,600036"

# 测试用例3：无效参数
curl "http://localhost:8000/api/v1/market/quotes/realtime?symbols="
```

**预期结果**:
- [ ] 返回格式: `{"success": true, "data": [...], "count": N, "timestamp": "..."}`
- [ ] 数据字段完整: symbol, name, currentPrice, change, changePercent, volume
- [ ] 响应时间 < 500ms
- [ ] 缓存命中率 > 80%（第二次请求）

#### 1.2 单股详情 `/market/quotes/{symbol}`
```bash
# 测试用例1：有效股票代码
curl "http://localhost:8000/api/v1/market/quotes/000001"

# 测试用例2：无效股票代码
curl "http://localhost:8000/api/v1/market/quotes/INVALID"
```

**预期结果**:
- [ ] 详细字段完整: openPrice, highPrice, lowPrice, prevClose, amount, turnoverRate, pe
- [ ] 错误处理正确（404 for invalid symbol）
- [ ] 缓存生效

### 2. K线数据接口

#### 2.1 K线数据 `/market/kline/{symbol}`
```bash
# 测试用例1：默认参数
curl "http://localhost:8000/api/v1/market/kline/000001"

# 测试用例2：指定周期和数量
curl "http://localhost:8000/api/v1/market/kline/000001?period=1h&limit=50"

# 测试用例3：日期范围
curl "http://localhost:8000/api/v1/market/kline/000001?start_date=2024-01-01&end_date=2024-12-31"
```

**预期结果**:
- [ ] K线数据格式: timestamp, open, high, low, close, volume
- [ ] 数据按时间排序
- [ ] 参数验证正确
- [ ] 缓存策略生效

### 3. 搜索接口

#### 3.1 股票搜索 `/market/search`
```bash
# 测试用例1：关键词搜索
curl "http://localhost:8000/api/v1/market/search?keyword=平安&limit=10"

# 测试用例2：代码搜索
curl "http://localhost:8000/api/v1/market/search?keyword=000001&limit=5"
```

**预期结果**:
- [ ] 搜索结果相关性高
- [ ] 返回字段: symbol, name, market, type
- [ ] 结果数量符合limit参数
- [ ] 搜索缓存生效

### 4. 市场深度接口

#### 4.1 深度数据 `/market/depth/{symbol}`
```bash
# 测试用例1：获取深度
curl "http://localhost:8000/api/v1/market/depth/000001"
```

**预期结果**:
- [ ] 买卖盘数据完整
- [ ] 价格和数量字段正确
- [ ] 实时缓存（TTL=30s）

### 5. 历史数据接口

#### 5.1 股票列表 `/market/historical/stocks`
```bash
# 需要登录token
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/api/v1/market/historical/stocks?page=1&page_size=20"
```

#### 5.2 历史搜索 `/market/historical/search`
```bash
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/api/v1/market/historical/search?keyword=平安"
```

#### 5.3 历史数据 `/market/historical/data/{symbol}`
```bash
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8000/api/v1/market/historical/data/000001"
```

**预期结果**:
- [ ] 鉴权正确（需要登录）
- [ ] 分页功能正常
- [ ] 数据格式正确

---

## 🎨 前端集成测试

### 1. 股票详情页面
- [ ] 页面正常加载
- [ ] 实时行情数据显示
- [ ] K线图正常渲染
- [ ] 数据刷新功能正常

### 2. 搜索功能
- [ ] 搜索框输入响应
- [ ] 搜索结果正确显示
- [ ] 点击结果跳转正常

### 3. 市场概览
- [ ] 热门股票列表
- [ ] 涨跌幅排行
- [ ] 数据实时更新

---

## ⚡ 性能测试

### 1. 响应时间测试
```bash
# 使用ab工具进行压力测试
ab -n 1000 -c 10 "http://localhost:8000/api/v1/market/quotes/realtime?symbols=000001"
```

**性能指标**:
- [ ] 平均响应时间 < 200ms
- [ ] 95%请求 < 500ms
- [ ] 99%请求 < 1000ms
- [ ] 无超时错误

### 2. 并发测试
```bash
# 并发访问不同接口
for i in {1..100}; do
  curl "http://localhost:8000/api/v1/market/quotes/000001" &
  curl "http://localhost:8000/api/v1/market/kline/000002" &
done
wait
```

**预期结果**:
- [ ] 无并发冲突
- [ ] 缓存一致性
- [ ] 无内存泄漏

### 3. 缓存效率测试
```bash
# 第一次请求（缓存miss）
time curl "http://localhost:8000/api/v1/market/quotes/000001"

# 第二次请求（缓存hit）
time curl "http://localhost:8000/api/v1/market/quotes/000001"
```

**预期结果**:
- [ ] 缓存命中率 > 80%
- [ ] 缓存命中响应时间 < 50ms
- [ ] 缓存过期正确

---

## 🔍 功能验证测试

### 1. 数据源降级测试
```python
# 模拟真实数据源失败
# 验证自动降级到Mock数据源
```

**验证点**:
- [ ] 降级机制触发
- [ ] Mock数据返回
- [ ] 错误日志记录
- [ ] 服务可用性保持

### 2. 缓存故障测试
```python
# 模拟Redis不可用
# 验证降级到内存缓存
```

**验证点**:
- [ ] 内存缓存生效
- [ ] 服务正常运行
- [ ] 性能影响可控

### 3. 错误处理测试
- [ ] 无效参数处理
- [ ] 网络超时处理
- [ ] 数据格式错误处理
- [ ] 系统异常处理

---

## 📊 监控指标验证

### 1. 系统指标
- [ ] CPU使用率 < 70%
- [ ] 内存使用率 < 80%
- [ ] 磁盘I/O正常
- [ ] 网络延迟 < 100ms

### 2. 应用指标
- [ ] API成功率 > 99%
- [ ] 平均响应时间改善
- [ ] 缓存命中率提升
- [ ] 错误率下降

### 3. 业务指标
- [ ] 用户体验无下降
- [ ] 功能完整性保持
- [ ] 数据准确性验证

---

## 🚨 回归风险检查

### 1. 向后兼容性
- [ ] 旧API路径是否仍可访问（如果需要）
- [ ] 数据格式是否保持一致
- [ ] 前端调用是否正常

### 2. 配置依赖
- [ ] 环境变量配置
- [ ] 数据库连接
- [ ] 外部服务依赖

### 3. 部署验证
- [ ] 服务启动正常
- [ ] 健康检查通过
- [ ] 日志输出正常

---

## 📝 测试报告模板

### 测试执行记录
```
测试日期: ____
测试环境: ____
测试人员: ____

API测试结果:
- 实时行情: ✅/❌
- K线数据: ✅/❌
- 搜索功能: ✅/❌
- 市场深度: ✅/❌
- 历史数据: ✅/❌

性能测试结果:
- 响应时间: ____ms (目标: <200ms)
- 并发处理: ____req/s
- 缓存命中率: ____%

问题记录:
1. ____
2. ____

修复建议:
1. ____
2. ____
```

### 测试通过标准
- [ ] 所有API接口测试通过
- [ ] 性能指标达到预期
- [ ] 无功能回归问题
- [ ] 前端集成正常
- [ ] 监控指标健康

---

## 🔄 测试自动化

### 1. 单元测试
```bash
# 运行服务层单元测试
pytest backend/tests/services/test_market_facade_service.py -v
```

### 2. 集成测试
```bash
# 运行API集成测试
pytest backend/tests/api/test_unified_market.py -v
```

### 3. 端到端测试
```bash
# 运行前端E2E测试
npm run test:e2e
```

**自动化覆盖率目标**:
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试覆盖率 > 70%
- [ ] E2E测试覆盖核心流程

---

## ✅ 测试完成检查清单

- [ ] 所有API接口测试通过
- [ ] 前端功能验证完成
- [ ] 性能指标达标
- [ ] 缓存机制验证
- [ ] 错误处理验证
- [ ] 监控指标正常
- [ ] 文档更新完成
- [ ] 部署验证通过

**测试负责人签字**: ________________
**测试完成日期**: ________________
