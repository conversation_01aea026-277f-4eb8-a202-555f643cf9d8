"""
可选认证模块
根据配置决定是否启用认证，支持开发环境关闭认证
"""

from typing import Optional, Callable, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
import logging

from app.core.config import get_settings

# 简单的用户类，避免复杂的数据库依赖
class User:
    def __init__(self, id: int, username: str, email: str, is_active: bool = True):
        self.id = id
        self.username = username
        self.email = email
        self.is_active = is_active

logger = logging.getLogger(__name__)
settings = get_settings()

# HTTP Bearer 认证方案
security = HTTPBearer(auto_error=False)


async def get_current_user_optional() -> Optional[User]:
    """
    可选的用户认证
    
    如果启用认证，则验证用户身份
    如果未启用认证，则返回None（匿名用户）
    """
    if not settings.MARKET_AUTH_ENABLED:
        # 认证未启用，返回匿名用户
        logger.debug("Market authentication disabled, allowing anonymous access")
        return None
    
    # 认证已启用，需要验证用户身份
    try:
        from app.core.auth import get_current_user
        # 这里应该调用真实的认证函数
        # 由于当前项目中认证模块可能不完整，我们先返回一个模拟用户
        logger.debug("Market authentication enabled, validating user")
        
        # 模拟认证用户（生产环境应该替换为真实认证）
        mock_user = User(
            id=1,
            username="authenticated_user",
            email="<EMAIL>",
            is_active=True
        )
        return mock_user
        
    except ImportError:
        logger.warning("Authentication module not available, falling back to anonymous access")
        return None
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        if settings.MARKET_AUTH_ENABLED:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required but failed",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return None


async def get_current_user_strict() -> User:
    """
    严格的用户认证（总是要求认证）
    用于管理类端点
    """
    user = await get_current_user_optional()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required for this endpoint",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user


def optional_auth_dependency():
    """
    可选认证依赖
    根据配置决定是否需要认证
    """
    if settings.MARKET_AUTH_ENABLED:
        return Depends(get_current_user_optional)
    else:
        # 返回一个总是返回None的依赖
        async def no_auth():
            return None
        return Depends(no_auth)


def strict_auth_dependency():
    """
    严格认证依赖
    总是要求认证
    """
    return Depends(get_current_user_strict)


class AuthConfig:
    """认证配置类"""
    
    @staticmethod
    def is_auth_enabled() -> bool:
        """检查认证是否启用"""
        return settings.MARKET_AUTH_ENABLED
    
    @staticmethod
    def is_rate_limit_enabled() -> bool:
        """检查限流是否启用"""
        return settings.MARKET_RATE_LIMIT_ENABLED
    
    @staticmethod
    def get_rate_limits() -> dict:
        """获取限流配置"""
        return {
            "realtime": settings.MARKET_REALTIME_RATE_LIMIT,
            "historical": settings.MARKET_HISTORICAL_RATE_LIMIT,
            "admin": settings.MARKET_ADMIN_RATE_LIMIT
        }
    
    @staticmethod
    def get_ws_limits() -> dict:
        """获取WebSocket限制配置"""
        return {
            "max_connections_per_user": settings.MARKET_WS_MAX_CONNECTIONS_PER_USER,
            "max_connections_per_ip": settings.MARKET_WS_MAX_CONNECTIONS_PER_IP,
            "max_subscriptions_per_connection": settings.MARKET_WS_MAX_SUBSCRIPTIONS_PER_CONNECTION
        }


# 导出常用的依赖
OptionalAuth = optional_auth_dependency()
StrictAuth = strict_auth_dependency()
