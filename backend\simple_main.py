"""
简化的主应用入口
只启动市场数据相关的API，用于测试B部分核心决策
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="量化投资平台 - 简化版",
    description="用于测试B部分核心决策的简化版API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 健康检查
@app.get("/api/v1/health")
async def health_check():
    return JSONResponse({
        "status": "ok",
        "message": "简化版API服务运行正常",
        "version": "1.0.0"
    })

# 导入市场数据相关的路由
try:
    from app.api.v1.market import router as market_router
    app.include_router(market_router, prefix="/api/v1/market", tags=["market"])
    print("✅ 基础版市场API已加载")
except Exception as e:
    print(f"⚠️ 基础版市场API加载失败: {e}")

try:
    from app.api.v1.enhanced_market import router as enhanced_market_router
    app.include_router(enhanced_market_router, prefix="/api/v1/market/enhanced-market", tags=["enhanced-market"])
    print("✅ 增强版市场API已加载")
except Exception as e:
    print(f"⚠️ 增强版市场API加载失败: {e}")

try:
    from app.api.v1.websocket_enhanced import router as websocket_router
    app.include_router(websocket_router, prefix="/api/v1", tags=["websocket"])
    print("✅ WebSocket API已加载")
except Exception as e:
    print(f"⚠️ WebSocket API加载失败: {e}")

# 根路径
@app.get("/")
async def root():
    return JSONResponse({
        "message": "量化投资平台 - 简化版API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/v1/health"
    })

# 启动应用
if __name__ == "__main__":
    print("🚀 启动简化版量化投资平台...")
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
