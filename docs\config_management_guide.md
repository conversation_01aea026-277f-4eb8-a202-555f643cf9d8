# 配置管理指南

## 配置层级

### 1. 环境配置
```
config/environment/
├── development.env.template    # 开发环境模板
├── production.env.template     # 生产环境模板
├── test.env.template          # 测试环境模板
└── README.md                  # 配置说明
```

### 2. 应用配置
```
backend/app/core/config.py     # 后端配置管理
frontend/src/config/           # 前端配置管理
```

### 3. 基础设施配置
```
docker/                        # 容器配置
monitoring/                    # 监控配置
scripts/                       # 部署脚本
```

## 配置使用流程

### 1. 首次部署
```bash
# 1. 复制环境配置模板
cp config/environment/development.env.template .env.development

# 2. 修改配置参数
vim .env.development

# 3. 加载配置
source .env.development

# 4. 启动服务
./scripts/start.sh
```

### 2. 环境切换
```bash
# 切换到生产环境
export ENVIRONMENT=production
source .env.production

# 切换到测试环境
export ENVIRONMENT=test
source .env.test
```

### 3. 配置验证
```bash
# 验证配置完整性
python scripts/validate-config.py

# 检查敏感信息
python scripts/check-secrets.py
```

## 配置管理最佳实践

### 1. 敏感信息管理
- **禁止**: 敏感信息写入代码
- **禁止**: 敏感信息提交到版本控制
- **推荐**: 使用环境变量
- **推荐**: 使用密钥管理服务

### 2. 配置层次化
```python
# 配置优先级（从高到低）
1. 环境变量
2. .env文件
3. 配置文件
4. 默认值
```

### 3. 配置验证
```python
# 配置模式验证
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    database_url: str
    redis_url: str
    secret_key: str
    
    @validator('secret_key')
    def secret_key_must_be_secure(cls, v):
        if len(v) < 32:
            raise ValueError('Secret key must be at least 32 characters')
        return v
```

## 环境差异管理

### 开发环境特点
- SQLite数据库
- Debug模式开启
- 详细日志输出
- Mock外部服务

### 测试环境特点
- 内存数据库
- 快速测试数据
- Mock所有外部依赖
- 覆盖率收集

### 生产环境特点
- PostgreSQL数据库
- 性能优化配置
- 结构化日志
- 监控告警

## 配置安全检查清单

### 部署前检查
- [ ] 所有密钥已更新
- [ ] 数据库连接已验证
- [ ] 外部服务连接已测试
- [ ] 日志级别已设置
- [ ] 监控配置已启用
- [ ] 备份策略已配置

### 运行时监控
- [ ] 配置加载成功
- [ ] 服务连接正常
- [ ] 性能指标正常
- [ ] 错误率在阈值内
