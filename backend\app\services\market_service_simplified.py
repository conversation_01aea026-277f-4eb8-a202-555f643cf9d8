"""
简化的市场数据服务
提供基础的模拟数据，替代复杂的依赖
"""
import random
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class SimplifiedMarketService:
    """简化的市场数据服务"""
    
    def __init__(self):
        # A股主要股票列表
        self.stock_list = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行", "currentPrice": 12.5, "change": 0.15, "changePercent": 1.2, "volume": 1000000, "amount": 12500000},
            {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产", "currentPrice": 15.8, "change": -0.25, "changePercent": -1.5, "volume": 2000000, "amount": 31600000},
            {"symbol": "000858", "name": "五粮液", "market": "SZ", "industry": "食品饮料", "currentPrice": 168.5, "change": 2.5, "changePercent": 1.5, "volume": 500000, "amount": 84250000},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行", "currentPrice": 38.2, "change": 0.8, "changePercent": 2.1, "volume": 3000000, "amount": 114600000},
            {"symbol": "600519", "name": "贵州茅台", "market": "SH", "industry": "食品饮料", "currentPrice": 1680.0, "change": -15.0, "changePercent": -0.9, "volume": 100000, "amount": 168000000},
        ]
        
        # 指数数据
        self.indices = {
            "000001": {"name": "上证指数", "currentPrice": 3245.68, "change": 12.45, "changePercent": 0.38, "volume": 245680000, "amount": 3456789000},
            "399001": {"name": "深证成指", "currentPrice": 10856.34, "change": -23.67, "changePercent": -0.22, "volume": 189450000, "amount": 2789456000},
            "399006": {"name": "创业板指", "currentPrice": 2234.56, "change": 8.92, "changePercent": 0.40, "volume": 156780000, "amount": 1987654000}
        }
    
    async def get_market_overview(self) -> Dict:
        """获取市场概览"""
        return {
            "timestamp": datetime.now().isoformat(),
            "indices": self.indices,
            "stats": {
                "advancers": 1245,
                "decliners": 987,
                "unchanged": 234,
                "total": 2466
            }
        }
    
    async def get_stock_list(self) -> List[Dict]:
        """获取股票列表"""
        return self.stock_list
    
    async def get_realtime_quote(self, symbol: str) -> Optional[Dict]:
        """获取实时行情"""
        for stock in self.stock_list:
            if stock["symbol"] == symbol:
                # 添加一些随机波动
                base_price = stock["currentPrice"]
                change_percent = random.uniform(-0.01, 0.01)  # ±1%波动
                current_price = base_price * (1 + change_percent)
                
                return {
                    "symbol": symbol,
                    "name": stock["name"],
                    "currentPrice": round(current_price, 2),
                    "change": round(current_price - base_price, 2),
                    "changePercent": round((current_price - base_price) / base_price * 100, 2),
                    "volume": stock["volume"],
                    "amount": stock["amount"],
                    "market": stock["market"],
                    "industry": stock["industry"],
                    "timestamp": datetime.now().isoformat()
                }
        return None
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict]:
        """搜索股票"""
        results = []
        keyword_lower = keyword.lower()
        
        for stock in self.stock_list:
            if (keyword_lower in stock["symbol"].lower() or 
                keyword_lower in stock["name"].lower()):
                results.append(stock)
                
            if len(results) >= limit:
                break
                
        return results

# 创建全局实例
simplified_market_service = SimplifiedMarketService()