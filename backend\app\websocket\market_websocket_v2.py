"""
WebSocket市场数据推送服务 - V2版本
实现主题订阅规范和实时数据推送
"""

import json
import logging
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, validator

from app.services.data_source_manager import data_source_manager
from app.core.websocket import ConnectionManager

logger = logging.getLogger(__name__)


class TopicType(str, Enum):
    """主题类型枚举"""
    MARKET_ALL = "market:all"           # 全量行情
    MARKET_SYMBOL = "market:{symbol}"   # 单品种
    MARKET_KLINE = "market:{symbol}:kline:{interval}"  # K线
    MARKET_SECTOR = "market:sector:{name}"             # 板块
    MARKET_RANKING = "market:ranking:{type}"           # 排行榜


class MessageType(str, Enum):
    """消息类型枚举"""
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    TICK = "tick"
    KLINE = "kline"
    SECTOR = "sector"
    RANKING = "ranking"
    ERROR = "error"
    HEARTBEAT = "heartbeat"


class SubscribeMessage(BaseModel):
    """订阅消息模型"""
    type: str = "subscribe"
    data: Dict[str, List[str]]
    
    @validator('data')
    def validate_topics(cls, v):
        if 'topics' not in v:
            raise ValueError("Missing 'topics' field")
        if not isinstance(v['topics'], list):
            raise ValueError("'topics' must be a list")
        return v


class UnsubscribeMessage(BaseModel):
    """取消订阅消息模型"""
    type: str = "unsubscribe"
    data: Dict[str, List[str]]


class WebSocketMessage(BaseModel):
    """WebSocket推送消息模型"""
    type: MessageType
    topic: str
    symbol: Optional[str] = None
    timestamp: int
    data: Dict[str, Any]


class TopicManager:
    """主题管理器"""
    
    def __init__(self):
        # websocket_id -> set of topics
        self.client_topics: Dict[str, Set[str]] = {}
        # topic -> set of websocket_ids
        self.topic_clients: Dict[str, Set[str]] = {}
    
    def subscribe(self, client_id: str, topics: List[str]) -> None:
        """订阅主题"""
        if client_id not in self.client_topics:
            self.client_topics[client_id] = set()
        
        for topic in topics:
            # 添加客户端订阅
            self.client_topics[client_id].add(topic)
            
            # 添加主题订阅者
            if topic not in self.topic_clients:
                self.topic_clients[topic] = set()
            self.topic_clients[topic].add(client_id)
        
        logger.info(f"Client {client_id} subscribed to topics: {topics}")
    
    def unsubscribe(self, client_id: str, topics: List[str]) -> None:
        """取消订阅主题"""
        if client_id not in self.client_topics:
            return
        
        for topic in topics:
            # 移除客户端订阅
            self.client_topics[client_id].discard(topic)
            
            # 移除主题订阅者
            if topic in self.topic_clients:
                self.topic_clients[topic].discard(client_id)
                if not self.topic_clients[topic]:
                    del self.topic_clients[topic]
        
        logger.info(f"Client {client_id} unsubscribed from topics: {topics}")
    
    def unsubscribe_all(self, client_id: str) -> None:
        """取消所有订阅"""
        if client_id not in self.client_topics:
            return
        
        topics = list(self.client_topics[client_id])
        for topic in topics:
            if topic in self.topic_clients:
                self.topic_clients[topic].discard(client_id)
                if not self.topic_clients[topic]:
                    del self.topic_clients[topic]
        
        del self.client_topics[client_id]
        logger.info(f"Client {client_id} unsubscribed from all topics")
    
    def get_subscribers(self, topic: str) -> Set[str]:
        """获取主题订阅者"""
        return self.topic_clients.get(topic, set())
    
    def get_client_topics(self, client_id: str) -> Set[str]:
        """获取客户端订阅的主题"""
        return self.client_topics.get(client_id, set())


class MarketWebSocketManager(ConnectionManager):
    """市场数据WebSocket管理器"""
    
    def __init__(self):
        super().__init__()
        self.topic_manager = TopicManager()
        self.push_tasks: Dict[str, asyncio.Task] = {}
        self._is_running = False
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """建立WebSocket连接"""
        await super().connect(websocket, client_id)
        
        # 发送连接成功消息
        await self.send_message(client_id, {
            "type": "connected",
            "client_id": client_id,
            "timestamp": int(datetime.now().timestamp() * 1000),
            "message": "WebSocket connected successfully"
        })
        
        # 开始推送任务
        if not self._is_running:
            self._start_push_tasks()
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        # 取消所有订阅
        self.topic_manager.unsubscribe_all(client_id)
        super().disconnect(client_id)
        
        # 如果没有客户端连接，停止推送任务
        if not self.active_connections:
            self._stop_push_tasks()
    
    async def handle_message(self, client_id: str, message: dict):
        """处理客户端消息"""
        try:
            msg_type = message.get("type")
            
            if msg_type == "subscribe":
                await self._handle_subscribe(client_id, message)
            elif msg_type == "unsubscribe":
                await self._handle_unsubscribe(client_id, message)
            elif msg_type == "ping":
                await self._handle_ping(client_id)
            else:
                await self.send_error(client_id, f"Unknown message type: {msg_type}")
                
        except Exception as e:
            logger.error(f"Error handling message from {client_id}: {e}")
            await self.send_error(client_id, str(e))
    
    async def _handle_subscribe(self, client_id: str, message: dict):
        """处理订阅消息"""
        try:
            subscribe_msg = SubscribeMessage(**message)
            topics = subscribe_msg.data["topics"]
            
            # 验证主题格式
            valid_topics = []
            for topic in topics:
                if self._validate_topic(topic):
                    valid_topics.append(topic)
                else:
                    logger.warning(f"Invalid topic format: {topic}")
            
            if valid_topics:
                self.topic_manager.subscribe(client_id, valid_topics)
                await self.send_message(client_id, {
                    "type": "subscribed",
                    "topics": valid_topics,
                    "timestamp": int(datetime.now().timestamp() * 1000)
                })
            else:
                await self.send_error(client_id, "No valid topics provided")
                
        except Exception as e:
            await self.send_error(client_id, f"Invalid subscribe message: {str(e)}")
    
    async def _handle_unsubscribe(self, client_id: str, message: dict):
        """处理取消订阅消息"""
        try:
            unsubscribe_msg = UnsubscribeMessage(**message)
            topics = unsubscribe_msg.data["topics"]
            
            self.topic_manager.unsubscribe(client_id, topics)
            await self.send_message(client_id, {
                "type": "unsubscribed",
                "topics": topics,
                "timestamp": int(datetime.now().timestamp() * 1000)
            })
            
        except Exception as e:
            await self.send_error(client_id, f"Invalid unsubscribe message: {str(e)}")
    
    async def _handle_ping(self, client_id: str):
        """处理心跳消息"""
        await self.send_message(client_id, {
            "type": "pong",
            "timestamp": int(datetime.now().timestamp() * 1000)
        })
    
    def _validate_topic(self, topic: str) -> bool:
        """验证主题格式"""
        # 全量行情
        if topic == "market:all":
            return True
        
        # 单品种行情
        if topic.startswith("market:") and not ":" in topic[7:]:
            return True
        
        # K线数据
        if topic.startswith("market:") and ":kline:" in topic:
            parts = topic.split(":")
            if len(parts) == 4 and parts[0] == "market" and parts[2] == "kline":
                return True
        
        # 板块数据
        if topic.startswith("market:sector:"):
            return True
        
        # 排行榜数据
        if topic.startswith("market:ranking:"):
            return True
        
        return False
    
    async def send_error(self, client_id: str, error_message: str):
        """发送错误消息"""
        await self.send_message(client_id, {
            "type": "error",
            "message": error_message,
            "timestamp": int(datetime.now().timestamp() * 1000)
        })
    
    def _start_push_tasks(self):
        """启动推送任务"""
        if self._is_running:
            return
        
        self._is_running = True
        
        # 启动不同类型的推送任务
        self.push_tasks["market_all"] = asyncio.create_task(self._push_market_all())
        self.push_tasks["market_quotes"] = asyncio.create_task(self._push_market_quotes())
        self.push_tasks["market_ranking"] = asyncio.create_task(self._push_market_ranking())
        
        logger.info("Market WebSocket push tasks started")
    
    def _stop_push_tasks(self):
        """停止推送任务"""
        if not self._is_running:
            return
        
        self._is_running = False
        
        for task_name, task in self.push_tasks.items():
            if not task.done():
                task.cancel()
        
        self.push_tasks.clear()
        logger.info("Market WebSocket push tasks stopped")
    
    async def _push_market_all(self):
        """推送全量行情"""
        while self._is_running:
            try:
                # 获取订阅者
                subscribers = self.topic_manager.get_subscribers("market:all")
                if not subscribers:
                    await asyncio.sleep(5)
                    continue
                
                # 获取市场概览数据
                overview_data = {
                    "total_stocks": 4500,
                    "trading_stocks": 4200,
                    "gainers": 2100,
                    "losers": 1800,
                    "indices": {
                        "sh_composite": {"value": 3245.67, "change": 12.34, "change_pct": 0.38},
                        "sz_component": {"value": 11234.56, "change": -23.45, "change_pct": -0.21}
                    }
                }
                
                message = {
                    "type": "tick",
                    "topic": "market:all",
                    "timestamp": int(datetime.now().timestamp() * 1000),
                    "data": overview_data
                }
                
                # 推送给所有订阅者
                for client_id in subscribers:
                    await self.send_message(client_id, message)
                
                await asyncio.sleep(30)  # 30秒推送一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in _push_market_all: {e}")
                await asyncio.sleep(5)
    
    async def _push_market_quotes(self):
        """推送单品种行情"""
        while self._is_running:
            try:
                # 获取所有单品种订阅
                symbol_topics = {topic for topic in self.topic_manager.topic_clients.keys() 
                               if topic.startswith("market:") and ":" not in topic[7:]}
                
                if not symbol_topics:
                    await asyncio.sleep(5)
                    continue
                
                # 为每个订阅的股票推送数据
                for topic in symbol_topics:
                    symbol = topic.split(":")[1]
                    subscribers = self.topic_manager.get_subscribers(topic)
                    
                    if subscribers:
                        try:
                            # 获取实时行情
                            quote = await data_source_manager.get_realtime_quote(symbol)
                            
                            message = {
                                "type": "tick",
                                "topic": topic,
                                "symbol": symbol,
                                "timestamp": int(datetime.now().timestamp() * 1000),
                                "data": quote
                            }
                            
                            # 推送给订阅者
                            for client_id in subscribers:
                                await self.send_message(client_id, message)
                                
                        except Exception as e:
                            logger.error(f"Error fetching quote for {symbol}: {e}")
                
                await asyncio.sleep(3)  # 3秒推送一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in _push_market_quotes: {e}")
                await asyncio.sleep(5)
    
    async def _push_market_ranking(self):
        """推送排行榜数据"""
        while self._is_running:
            try:
                # 获取排行榜订阅
                ranking_topics = {topic for topic in self.topic_manager.topic_clients.keys() 
                                if topic.startswith("market:ranking:")}
                
                if not ranking_topics:
                    await asyncio.sleep(10)
                    continue
                
                # 为每个排行榜类型推送数据
                for topic in ranking_topics:
                    ranking_type = topic.split(":")[2]
                    subscribers = self.topic_manager.get_subscribers(topic)
                    
                    if subscribers:
                        # 模拟排行榜数据
                        import random
                        ranking = []
                        for i in range(10):
                            if ranking_type == "gainers":
                                change_pct = round(random.uniform(5, 15), 2)
                            elif ranking_type == "losers":
                                change_pct = round(random.uniform(-15, -5), 2)
                            else:
                                change_pct = round(random.uniform(-5, 5), 2)
                            
                            ranking.append({
                                "rank": i + 1,
                                "symbol": f"00000{i+1:02d}",
                                "name": f"股票{i+1}",
                                "price": round(random.uniform(5, 50), 2),
                                "change_pct": change_pct
                            })
                        
                        message = {
                            "type": "ranking",
                            "topic": topic,
                            "timestamp": int(datetime.now().timestamp() * 1000),
                            "data": {
                                "type": ranking_type,
                                "ranking": ranking
                            }
                        }
                        
                        # 推送给订阅者
                        for client_id in subscribers:
                            await self.send_message(client_id, message)
                
                await asyncio.sleep(60)  # 60秒推送一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in _push_market_ranking: {e}")
                await asyncio.sleep(10)


# 全局WebSocket管理器实例
market_websocket_manager = MarketWebSocketManager()


async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket端点处理函数"""
    await market_websocket_manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                await market_websocket_manager.handle_message(client_id, message)
            except json.JSONDecodeError:
                await market_websocket_manager.send_error(client_id, "Invalid JSON format")
            
    except WebSocketDisconnect:
        market_websocket_manager.disconnect(client_id)
        logger.info(f"WebSocket client {client_id} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        market_websocket_manager.disconnect(client_id)