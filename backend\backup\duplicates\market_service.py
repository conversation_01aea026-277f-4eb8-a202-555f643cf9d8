"""
市场数据服务
提供股票行情、K线、排行榜等市场数据服务
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

try:
    from sqlalchemy import and_, desc, func, select
    from sqlalchemy.ext.asyncio import AsyncSession
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    AsyncSession = None

try:
    from app.core.database import get_db
except ImportError:
    get_db = None

try:
    from app.db.models.market import MarketData, Symbol
except ImportError:
    MarketData = None
    Symbol = None

try:
    from app.db.models.watchlist import WatchlistItem as WatchlistItemModel
except ImportError:
    WatchlistItemModel = None

try:
    from app.db.models.user import User
except ImportError:
    User = None
try:
    from app.schemas.market import (
        BarData,
        Exchange,
        MarketOverviewResponse,
        OrderBookData,
        RankingData,
        RankingType,
        SearchResult,
        SectorData,
        TickData,
        Interval as TimePeriod,
    )
except ImportError:
    # 如果无法导入schemas，定义基本的类
    BarData = None
    Exchange = None
    MarketOverviewResponse = None
    OrderBookData = None
    RankingData = None
    RankingType = None
    SearchResult = None
    SectorData = None
    TickData = None
    TimePeriod = None

try:
    from app.schemas.market_data import (
        WatchlistItemResponse as WatchlistItem,
        KlineData,
    )
except ImportError:
    WatchlistItem = None
    KlineData = None
try:
    from app.services.market_data_service import market_data_service
except ImportError:
    market_data_service = None

try:
    from app.services.historical_stock_service import historical_stock_service
except ImportError:
    historical_stock_service = None


class QuoteData:
    """行情数据类，用于兼容"""
    def __init__(self, **kwargs):
        self.symbol = kwargs.get('symbol', '')
        self.name = kwargs.get('name', '')
        self.currentPrice = kwargs.get('currentPrice', 0.0)
        self.change = kwargs.get('change', 0.0)
        self.changePercent = kwargs.get('changePercent', 0.0)
        self.open = kwargs.get('open', 0.0)
        self.high = kwargs.get('high', 0.0)
        self.low = kwargs.get('low', 0.0)
        self.previousClose = kwargs.get('previousClose', 0.0)
        self.volume = kwargs.get('volume', 0)
        self.turnover = kwargs.get('turnover', 0.0)
        self.timestamp = kwargs.get('timestamp', 0.0)
    
    def dict(self):
        return {
            'symbol': self.symbol,
            'name': self.name,
            'currentPrice': self.currentPrice,
            'change': self.change,
            'changePercent': self.changePercent,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'previousClose': self.previousClose,
            'volume': self.volume,
            'turnover': self.turnover,
            'timestamp': self.timestamp,
        }


class MarketService:
    """市场数据服务类"""

    def __init__(self, db: AsyncSession = None):
        self.db = db
        self._quote_cache: Dict[str, dict] = {}
        self._cache_ttl = 5  # 缓存5秒

    # ============ 实时行情 ============

    async def get_quote(self, symbol: str) -> Optional[QuoteData]:
        """获取单个股票行情"""
        # 先检查缓存
        cached = self._get_cached_quote(symbol)
        if cached:
            return QuoteData(**cached)

        # 模拟数据（实际应从数据源获取）
        quote_data = {
            "symbol": symbol,
            "name": self._get_stock_name(symbol),
            "currentPrice": 100.0,
            "change": 2.5,
            "changePercent": 2.56,
            "open": 98.0,
            "high": 102.0,
            "low": 97.5,
            "previousClose": 97.5,
            "volume": 1000000,
            "turnover": 100000000,
            "timestamp": datetime.now().timestamp(),
        }

        # 缓存数据
        self._cache_quote(symbol, quote_data)
        quote = QuoteData(**quote_data)
        return quote

    async def get_quotes(self, symbols: List[str]) -> List[dict]:
        """批量获取股票行情"""
        quotes = []
        for symbol in symbols:
            quote = await self.get_quote(symbol)
            if quote:
                quotes.append(quote.dict())
        return quotes

    # ============ K线数据 ============

    async def get_kline_data(
        self,
        symbol: str,
        period: str,
        limit: int = 500,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> List[dict]:
        """获取K线数据"""
        # 如果没有指定时间范围，默认获取最近的数据
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            # 根据周期计算开始时间
            period_minutes = self._get_period_minutes(period)
            start_time = end_time - timedelta(minutes=period_minutes * limit)

        # 模拟K线数据
        klines = []
        current_time = start_time
        base_price = 100.0

        while current_time <= end_time and len(klines) < limit:
            # 生成随机OHLC数据
            import random

            open_price = base_price + random.uniform(-2, 2)
            close_price = open_price + random.uniform(-1, 1)
            high_price = max(open_price, close_price) + random.uniform(0, 0.5)
            low_price = min(open_price, close_price) - random.uniform(0, 0.5)
            volume = random.randint(100000, 1000000)

            kline = {
                "symbol": symbol,
                "exchange": "SSE",  # 默认使用上交所
                "timestamp": current_time.isoformat(),
                "interval": period,
                "open_price": open_price,
                "high_price": high_price,
                "low_price": low_price,
                "close_price": close_price,
                "volume": volume,
                "turnover": volume * close_price,
            }
            klines.append(kline)

            # 更新基准价格和时间
            base_price = close_price
            period_minutes = self._get_period_minutes(period)
            current_time += timedelta(minutes=period_minutes)

        return klines

    # ============ 市场概览 ============

    async def get_market_overview(self) -> dict:
        """获取市场概览"""
        try:
            # 从历史股票服务获取真实数据
            if historical_stock_service:
                sh_stocks = await historical_stock_service.get_stock_list(page=1, page_size=1000, market="SH")
                sz_stocks = await historical_stock_service.get_stock_list(page=1, page_size=1000, market="SZ")
            else:
                sh_stocks = {"stocks": [], "total": 0}
                sz_stocks = {"stocks": [], "total": 0}

            # 计算上海市场统计
            sh_up = sum(1 for stock in sh_stocks["stocks"] if stock.get("changePercent", 0) > 0)
            sh_down = sum(1 for stock in sh_stocks["stocks"] if stock.get("changePercent", 0) < 0)
            sh_flat = sh_stocks["total"] - sh_up - sh_down
            sh_volume = sum(stock.get("volume", 0) for stock in sh_stocks["stocks"])
            sh_turnover = sum(stock.get("turnover", 0) for stock in sh_stocks["stocks"])

            # 计算深圳市场统计
            sz_up = sum(1 for stock in sz_stocks["stocks"] if stock.get("changePercent", 0) > 0)
            sz_down = sum(1 for stock in sz_stocks["stocks"] if stock.get("changePercent", 0) < 0)
            sz_flat = sz_stocks["total"] - sz_up - sz_down
            sz_volume = sum(stock.get("volume", 0) for stock in sz_stocks["stocks"])
            sz_turnover = sum(stock.get("turnover", 0) for stock in sz_stocks["stocks"])

            overview = {
                "marketStats": [
                    {
                        "market": "SH",
                        "name": "上证主板",
                        "upCount": sh_up,
                        "downCount": sh_down,
                        "flatCount": sh_flat,
                        "totalVolume": sh_volume,
                        "totalTurnover": sh_turnover,
                    },
                    {
                        "market": "SZ",
                        "name": "深证主板",
                        "upCount": sz_up,
                        "downCount": sz_down,
                        "flatCount": sz_flat,
                        "totalVolume": sz_volume,
                        "totalTurnover": sz_turnover,
                    },
                ],
                "timestamp": datetime.now().timestamp(),
            }
            return overview
        except Exception as e:
            # 如果获取历史数据失败，返回默认数据
            overview = {
                "marketStats": [
                    {
                        "market": "SH",
                        "name": "上证主板",
                        "upCount": 1200,
                        "downCount": 800,
                        "flatCount": 100,
                        "totalVolume": 150000000000,
                        "totalTurnover": 200000000000,
                    },
                    {
                        "market": "SZ",
                        "name": "深证主板",
                        "upCount": 800,
                        "downCount": 600,
                        "flatCount": 50,
                        "totalVolume": 100000000000,
                        "totalTurnover": 120000000000,
                    },
                ],
                "timestamp": datetime.now().timestamp(),
            }
            return overview

    async def get_indices(self) -> List[dict]:
        """获取主要指数"""
        # 模拟指数数据
        indices = [
            {
                "symbol": "000001",
                "name": "上证指数",
                "currentPrice": 3200.50,
                "change": 25.30,
                "changePercent": 0.80,
                "volume": 150000000000,
                "turnover": 200000000000,
            },
            {
                "symbol": "399001",
                "name": "深证成指",
                "currentPrice": 10500.20,
                "change": -50.60,
                "changePercent": -0.48,
                "volume": 100000000000,
                "turnover": 120000000000,
            },
            {
                "symbol": "399006",
                "name": "创业板指",
                "currentPrice": 2100.80,
                "change": 15.20,
                "changePercent": 0.73,
                "volume": 50000000000,
                "turnover": 60000000000,
            },
            {
                "symbol": "000688",
                "name": "科创50",
                "currentPrice": 950.30,
                "change": 8.50,
                "changePercent": 0.90,
                "volume": 30000000000,
                "turnover": 35000000000,
            },
        ]

        return [
            {
                "symbol": idx["symbol"],
                "name": idx["name"],
                "currentPrice": idx["currentPrice"],
                "change": idx["change"],
                "changePercent": idx["changePercent"],
                "volume": idx["volume"],
                "turnover": idx["turnover"],
                "timestamp": datetime.now().timestamp(),
            }
            for idx in indices
        ]

    # ============ 板块数据 ============

    async def get_sectors(self) -> List[dict]:
        """获取板块行情"""
        # 模拟板块数据
        sectors = [
            {
                "code": "BK0001",
                "name": "银行",
                "changePercent": 1.25,
                "leadingStock": "600036",
                "stockCount": 42,
            },
            {
                "code": "BK0002",
                "name": "房地产",
                "changePercent": -0.85,
                "leadingStock": "000002",
                "stockCount": 128,
            },
            {
                "code": "BK0003",
                "name": "新能源",
                "changePercent": 2.35,
                "leadingStock": "300750",
                "stockCount": 256,
            },
            {
                "code": "BK0004",
                "name": "半导体",
                "changePercent": 1.85,
                "leadingStock": "603986",
                "stockCount": 180,
            },
            {
                "code": "BK0005",
                "name": "白酒",
                "changePercent": -0.25,
                "leadingStock": "600519",
                "stockCount": 20,
            },
        ]

        return [
            {
                "code": s["code"],
                "name": s["name"],
                "changePercent": s["changePercent"],
                "leadingStock": s["leadingStock"],
                "stockCount": s["stockCount"],
                "updateTime": datetime.now().timestamp(),
            }
            for s in sectors
        ]

    async def get_industries(self) -> List[dict]:
        """获取行业列表"""
        # 模拟行业数据
        industries = [
            {"code": "IND001", "name": "银行"},
            {"code": "IND002", "name": "房地产"},
            {"code": "IND003", "name": "汽车"},
            {"code": "IND004", "name": "医药生物"},
            {"code": "IND005", "name": "电子"},
            {"code": "IND006", "name": "计算机"},
            {"code": "IND007", "name": "传媒"},
            {"code": "IND008", "name": "通信"},
            {"code": "IND009", "name": "家用电器"},
            {"code": "IND010", "name": "食品饮料"},
        ]
        return industries

    # ============ 排行榜 ============

    async def get_rankings(
        self, type: str, limit: int = 50
    ) -> List[dict]:
        """获取排行榜数据"""
        # 模拟排行榜数据
        stocks = await self._get_all_stocks()

        # 根据类型排序
        if type == "CHANGE_PERCENT":
            stocks.sort(key=lambda x: x.get("changePercent", 0), reverse=True)
        elif type == "TURNOVER":
            stocks.sort(key=lambda x: x.get("turnover", 0), reverse=True)
        elif type == "VOLUME":
            stocks.sort(key=lambda x: x.get("volume", 0), reverse=True)
        elif type == "AMPLITUDE":
            stocks.sort(key=lambda x: x.get("amplitude", 0), reverse=True)

        # 转换为排行榜数据
        rankings = []
        for i, stock in enumerate(stocks[:limit]):
            ranking = {
                "rank": i + 1,
                "symbol": stock["symbol"],
                "name": stock["name"],
                "currentPrice": stock["currentPrice"],
                "change": stock["change"],
                "changePercent": stock["changePercent"],
                "volume": stock["volume"],
                "turnover": stock["turnover"],
                "amplitude": stock.get("amplitude", 0),
            }
            rankings.append(ranking)

        return rankings

    # ============ 搜索功能 ============

    async def search_stocks(self, keyword: str, limit: int = 20) -> List[dict]:
        """搜索股票"""
        # 模拟搜索结果
        all_stocks = await self._get_all_stocks()
        results = []

        keyword_lower = keyword.lower()
        for stock in all_stocks:
            if (
                keyword_lower in stock["symbol"].lower()
                or keyword_lower in stock["name"].lower()
            ):
                result = {
                    "symbol": stock["symbol"],
                    "name": stock["name"],
                    "market": stock.get("market", "SH"),
                    "industry": stock.get("industry", ""),
                }
                results.append(result)
                if len(results) >= limit:
                    break

        return results

    # ============ 自选股管理 ============

    async def get_watchlist(self, user_id: int) -> List[dict]:
        """获取用户自选股列表"""
        if not self.db or not SQLALCHEMY_AVAILABLE or not WatchlistItemModel:
            # 返回模拟数据
            return [
                {
                    "symbol": "600519",
                    "name": "贵州茅台",
                    "addTime": int(datetime.now().timestamp() * 1000),
                    "sort": 0,
                },
                {
                    "symbol": "000858",
                    "name": "五粮液",
                    "addTime": int(datetime.now().timestamp() * 1000),
                    "sort": 1,
                },
            ]

        # 从数据库查询
        result = await self.db.execute(
            select(WatchlistItemModel)
            .where(WatchlistItemModel.user_id == user_id)
            .order_by(WatchlistItemModel.id)
        )
        watchlist = result.scalars().all()

        items = []
        for w in watchlist:
            # 获取股票信息
            if Symbol:
                symbol_result = await self.db.execute(
                    select(Symbol).where(Symbol.symbol == w.symbol)
                )
                symbol = symbol_result.scalar_one_or_none()
            else:
                symbol = None

            item = {
                "symbol": w.symbol,
                "name": w.name if hasattr(w, 'name') and w.name else (symbol.name if symbol else w.symbol),
                "addTime": int(w.created_at.timestamp() * 1000),
                "sort": getattr(w, 'sort', 0),
            }
            items.append(item)

        return items

    async def add_to_watchlist(self, user_id: int, symbol: str) -> bool:
        """添加自选股"""
        if not self.db or not SQLALCHEMY_AVAILABLE or not WatchlistItemModel:
            return True

        # 检查是否已存在
        existing = await self.db.execute(
            select(WatchlistItemModel).where(
                and_(WatchlistItemModel.user_id == user_id, WatchlistItemModel.symbol == symbol)
            )
        )
        if existing.scalar_one_or_none():
            return False

        # 获取股票名称
        stock_name = self._get_stock_name(symbol)
        
        # 添加到自选股
        watchlist_item = WatchlistItemModel(
            user_id=user_id,
            symbol=symbol,
            name=stock_name,
        )
        self.db.add(watchlist_item)
        await self.db.commit()

        return True

    async def remove_from_watchlist(self, user_id: int, symbol: str) -> bool:
        """删除自选股"""
        if not self.db or not SQLALCHEMY_AVAILABLE or not WatchlistItemModel:
            return True

        result = await self.db.execute(
            select(WatchlistItemModel).where(
                and_(WatchlistItemModel.user_id == user_id, WatchlistItemModel.symbol == symbol)
            )
        )
        watchlist_item = result.scalar_one_or_none()

        if watchlist_item:
            await self.db.delete(watchlist_item)
            await self.db.commit()
            return True

        return False

    # ============ 订单簿 ============

    async def get_orderbook(
        self, symbol: str, depth: int = 5
    ) -> Optional[dict]:
        """获取订单簿数据"""
        # 模拟订单簿数据
        import random

        base_price = 100.0
        orderbook = {
            "symbol": symbol,
            "bids": [],
            "asks": [],
            "timestamp": datetime.now().timestamp(),
        }

        # 生成买单
        for i in range(depth):
            price = base_price - (i + 1) * 0.01
            volume = random.randint(1000, 10000) * 100
            orderbook["bids"].append({"price": price, "volume": volume})

        # 生成卖单
        for i in range(depth):
            price = base_price + (i + 1) * 0.01
            volume = random.randint(1000, 10000) * 100
            orderbook["asks"].append({"price": price, "volume": volume})

        return orderbook

    # ============ 股票列表 ============

    async def get_stock_list(
        self,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        page: int = 1,
        page_size: int = 50,
    ) -> List[dict]:
        """获取股票列表"""
        all_stocks = await self._get_all_stocks()

        # 过滤
        filtered_stocks = all_stocks
        if market:
            filtered_stocks = [s for s in filtered_stocks if s.get("market") == market]
        if industry:
            filtered_stocks = [
                s for s in filtered_stocks if s.get("industry") == industry
            ]

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        return filtered_stocks[start:end]

    # ============ 新闻资讯 ============

    async def get_news(
        self, limit: int = 20, category: Optional[str] = None
    ) -> List[dict]:
        """获取市场新闻"""
        # 模拟新闻数据
        news = [
            {
                "id": "1",
                "title": "A股三大指数集体收涨，北向资金净买入超50亿",
                "source": "财联社",
                "publishTime": datetime.now().isoformat(),
                "category": "市场",
                "url": "#",
            },
            {
                "id": "2",
                "title": "新能源板块持续走强，多只个股涨停",
                "source": "证券时报",
                "publishTime": (datetime.now() - timedelta(hours=1)).isoformat(),
                "category": "板块",
                "url": "#",
            },
            {
                "id": "3",
                "title": "央行：继续实施稳健的货币政策",
                "source": "新华社",
                "publishTime": (datetime.now() - timedelta(hours=2)).isoformat(),
                "category": "政策",
                "url": "#",
            },
            {
                "id": "4",
                "title": "半导体板块午后拉升，行业景气度持续向好",
                "source": "中国证券报",
                "publishTime": (datetime.now() - timedelta(hours=3)).isoformat(),
                "category": "行业",
                "url": "#",
            },
            {
                "id": "5",
                "title": "北交所总市值突破3000亿元大关",
                "source": "上海证券报",
                "publishTime": (datetime.now() - timedelta(hours=4)).isoformat(),
                "category": "市场",
                "url": "#",
            },
        ]

        # 按分类过滤
        if category:
            news = [n for n in news if n["category"] == category]

        return news[:limit]

    # ============ 历史数据 ============

    async def get_history_kline(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        period: str = "DAILY",
    ) -> List[dict]:
        """获取历史K线数据"""
        return await self.get_kline_data(
            symbol=symbol,
            period=period,
            start_time=start_date,
            end_time=end_date,
            limit=10000,
        )

    async def get_history_tick(
        self, symbol: str, start_date: datetime, end_date: datetime
    ) -> List[dict]:
        """获取历史Tick数据"""
        # 模拟历史Tick数据
        ticks = []
        current_time = start_date
        base_price = 100.0

        while current_time <= end_date:
            import random

            tick = {
                "symbol": symbol,
                "exchange": "SH",
                "timestamp": current_time.isoformat(),
                "last_price": base_price + random.uniform(-0.5, 0.5),
                "volume": random.randint(100, 1000),
                "turnover": random.randint(10000, 100000),
                "bid_price": base_price - 0.01,
                "bid_volume": random.randint(100, 1000),
                "ask_price": base_price + 0.01,
                "ask_volume": random.randint(100, 1000),
            }
            ticks.append(tick)

            # 更新时间（每5秒一个tick）
            current_time += timedelta(seconds=5)
            if len(ticks) >= 1000:  # 限制最多返回1000条
                break

        return ticks

    # ============ 辅助方法 ============

    def _get_stock_name(self, symbol: str) -> str:
        """根据股票代码获取名称"""
        # 模拟股票名称映射
        stock_names = {
            "600519": "贵州茅台",
            "000858": "五粮液",
            "000001": "平安银行",
            "000002": "万科A",
            "600036": "招商银行",
            "300750": "宁德时代",
            "002594": "比亚迪",
            "603986": "兆易创新",
        }
        return stock_names.get(symbol, f"股票{symbol}")

    def _get_cached_quote(self, symbol: str) -> Optional[dict]:
        """从缓存获取行情"""
        if symbol in self._quote_cache:
            quote = self._quote_cache[symbol]
            # 检查是否过期
            if datetime.now().timestamp() - quote["timestamp"] < self._cache_ttl:
                return quote
        return None

    def _cache_quote(self, symbol: str, quote: dict):
        """缓存行情数据"""
        self._quote_cache[symbol] = quote

    def _get_period_minutes(self, period: str) -> int:
        """获取周期对应的分钟数"""
        period_map = {
            "MINUTE": 1,
            "MINUTE_5": 5,
            "MINUTE_15": 15,
            "MINUTE_30": 30,
            "HOUR": 60,
            "HOUR_4": 240,
            "DAILY": 1440,
            "WEEKLY": 10080,
            "MONTHLY": 43200,
        }
        return period_map.get(period, 60)

    async def _get_all_stocks(self) -> List[dict]:
        """获取所有股票数据（模拟）"""
        import random

        stocks = []
        # 生成模拟股票数据
        symbols = [
            ("600519", "贵州茅台", "SH", "食品饮料"),
            ("000858", "五粮液", "SZ", "食品饮料"),
            ("000001", "平安银行", "SZ", "银行"),
            ("000002", "万科A", "SZ", "房地产"),
            ("600036", "招商银行", "SH", "银行"),
            ("300750", "宁德时代", "SZ", "新能源"),
            ("002594", "比亚迪", "SZ", "汽车"),
            ("603986", "兆易创新", "SH", "半导体"),
            ("600309", "万华化学", "SH", "化工"),
            ("002415", "海康威视", "SZ", "电子"),
        ]

        for symbol, name, market, industry in symbols:
            base_price = random.uniform(50, 500)
            change_percent = random.uniform(-5, 5)
            change = base_price * change_percent / 100
            volume = random.randint(1000000, 10000000)

            stock = {
                "symbol": symbol,
                "name": name,
                "market": market,
                "industry": industry,
                "currentPrice": base_price,
                "change": change,
                "changePercent": change_percent,
                "volume": volume,
                "turnover": volume * base_price,
                "amplitude": random.uniform(0, 10),
            }
            stocks.append(stock)

        return stocks


# 创建全局市场服务实例
market_service = MarketService()
