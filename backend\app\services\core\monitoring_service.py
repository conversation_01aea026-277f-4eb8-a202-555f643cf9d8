"""
监控服务
提供系统监控、性能监控、交易监控和报警功能
"""

import asyncio
import logging
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


class AlertLevel(str, Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MonitorType(str, Enum):
    """监控类型"""
    SYSTEM = "system"         # 系统监控
    TRADING = "trading"       # 交易监控
    PERFORMANCE = "performance" # 性能监控
    SECURITY = "security"     # 安全监控


@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    type: MonitorType
    title: str
    message: str
    timestamp: datetime
    resolved: bool = False
    resolve_time: Optional[datetime] = None


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    uptime: float
    timestamp: datetime


@dataclass
class TradingMetrics:
    """交易指标"""
    order_count: int
    trade_count: int
    order_success_rate: float
    avg_response_time: float
    active_connections: int
    error_count: int
    timestamp: datetime


class MonitoringService:
    """监控服务"""
    
    def __init__(self):
        self.alerts: List[Alert] = []
        self.system_metrics_history: List[SystemMetrics] = []
        self.trading_metrics_history: List[TradingMetrics] = []
        self.alert_callbacks: List[Callable] = []
        
        # 监控配置
        self.config = {
            "cpu_threshold": 80.0,           # CPU使用率告警阈值
            "memory_threshold": 85.0,        # 内存使用率告警阈值
            "disk_threshold": 90.0,          # 磁盘使用率告警阈值
            "response_time_threshold": 1000, # 响应时间告警阈值(ms)
            "error_rate_threshold": 0.05,    # 错误率告警阈值
            "connection_threshold": 1000,    # 连接数告警阈值
        }
        
        # 监控状态
        self.monitoring_active = False
        self.last_check_time = datetime.now()
        
        logger.info("Monitoring Service initialized")
    
    async def start_monitoring(self):
        """启动监控"""
        try:
            self.monitoring_active = True
            logger.info("Monitoring started")
            
            # 启动监控任务
            asyncio.create_task(self._system_monitor_loop())
            asyncio.create_task(self._trading_monitor_loop())
            asyncio.create_task(self._alert_processor_loop())
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
    
    async def stop_monitoring(self):
        """停止监控"""
        try:
            self.monitoring_active = False
            logger.info("Monitoring stopped")
        except Exception as e:
            logger.error(f"Failed to stop monitoring: {e}")
    
    async def _system_monitor_loop(self):
        """系统监控循环"""
        while self.monitoring_active:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(30)  # 每30秒检查一次
            except Exception as e:
                logger.error(f"System monitor error: {e}")
                await asyncio.sleep(60)
    
    async def _trading_monitor_loop(self):
        """交易监控循环"""
        while self.monitoring_active:
            try:
                await self._collect_trading_metrics()
                await asyncio.sleep(10)  # 每10秒检查一次
            except Exception as e:
                logger.error(f"Trading monitor error: {e}")
                await asyncio.sleep(30)
    
    async def _alert_processor_loop(self):
        """告警处理循环"""
        while self.monitoring_active:
            try:
                await self._process_alerts()
                await asyncio.sleep(5)  # 每5秒处理一次告警
            except Exception as e:
                logger.error(f"Alert processor error: {e}")
                await asyncio.sleep(10)
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 获取系统指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent,
                network_io={
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                process_count=len(psutil.pids()),
                uptime=time.time() - psutil.boot_time(),
                timestamp=datetime.now()
            )
            
            # 保存指标
            self.system_metrics_history.append(metrics)
            
            # 保留最近1000条记录
            if len(self.system_metrics_history) > 1000:
                self.system_metrics_history = self.system_metrics_history[-1000:]
            
            # 检查告警条件
            await self._check_system_alerts(metrics)
            
        except Exception as e:
            logger.error(f"Collect system metrics error: {e}")
    
    async def _collect_trading_metrics(self):
        """收集交易指标"""
        try:
            # 这里应该从CTP服务和交易服务获取实际指标
            # 现在使用模拟数据
            
            metrics = TradingMetrics(
                order_count=100,
                trade_count=95,
                order_success_rate=0.95,
                avg_response_time=150.0,
                active_connections=50,
                error_count=2,
                timestamp=datetime.now()
            )
            
            # 保存指标
            self.trading_metrics_history.append(metrics)
            
            # 保留最近1000条记录
            if len(self.trading_metrics_history) > 1000:
                self.trading_metrics_history = self.trading_metrics_history[-1000:]
            
            # 检查告警条件
            await self._check_trading_alerts(metrics)
            
        except Exception as e:
            logger.error(f"Collect trading metrics error: {e}")
    
    async def _check_system_alerts(self, metrics: SystemMetrics):
        """检查系统告警"""
        try:
            # CPU使用率告警
            if metrics.cpu_percent > self.config["cpu_threshold"]:
                await self._create_alert(
                    level=AlertLevel.WARNING,
                    type=MonitorType.SYSTEM,
                    title="CPU使用率过高",
                    message=f"CPU使用率达到{metrics.cpu_percent:.1f}%，超过阈值{self.config['cpu_threshold']}%"
                )
            
            # 内存使用率告警
            if metrics.memory_percent > self.config["memory_threshold"]:
                await self._create_alert(
                    level=AlertLevel.WARNING,
                    type=MonitorType.SYSTEM,
                    title="内存使用率过高",
                    message=f"内存使用率达到{metrics.memory_percent:.1f}%，超过阈值{self.config['memory_threshold']}%"
                )
            
            # 磁盘使用率告警
            if metrics.disk_percent > self.config["disk_threshold"]:
                await self._create_alert(
                    level=AlertLevel.ERROR,
                    type=MonitorType.SYSTEM,
                    title="磁盘空间不足",
                    message=f"磁盘使用率达到{metrics.disk_percent:.1f}%，超过阈值{self.config['disk_threshold']}%"
                )
                
        except Exception as e:
            logger.error(f"Check system alerts error: {e}")
    
    async def _check_trading_alerts(self, metrics: TradingMetrics):
        """检查交易告警"""
        try:
            # 响应时间告警
            if metrics.avg_response_time > self.config["response_time_threshold"]:
                await self._create_alert(
                    level=AlertLevel.WARNING,
                    type=MonitorType.TRADING,
                    title="交易响应时间过长",
                    message=f"平均响应时间{metrics.avg_response_time:.1f}ms，超过阈值{self.config['response_time_threshold']}ms"
                )
            
            # 错误率告警
            if metrics.order_count > 0:
                error_rate = metrics.error_count / metrics.order_count
                if error_rate > self.config["error_rate_threshold"]:
                    await self._create_alert(
                        level=AlertLevel.ERROR,
                        type=MonitorType.TRADING,
                        title="交易错误率过高",
                        message=f"错误率达到{error_rate:.2%}，超过阈值{self.config['error_rate_threshold']:.2%}"
                    )
            
            # 连接数告警
            if metrics.active_connections > self.config["connection_threshold"]:
                await self._create_alert(
                    level=AlertLevel.WARNING,
                    type=MonitorType.TRADING,
                    title="活跃连接数过多",
                    message=f"活跃连接数{metrics.active_connections}，超过阈值{self.config['connection_threshold']}"
                )
                
        except Exception as e:
            logger.error(f"Check trading alerts error: {e}")
    
    async def _create_alert(self, level: AlertLevel, type: MonitorType, title: str, message: str):
        """创建告警"""
        try:
            alert = Alert(
                id=f"alert_{int(time.time())}_{len(self.alerts)}",
                level=level,
                type=type,
                title=title,
                message=message,
                timestamp=datetime.now()
            )
            
            self.alerts.append(alert)
            
            # 保留最近1000条告警
            if len(self.alerts) > 1000:
                self.alerts = self.alerts[-1000:]
            
            # 触发告警回调
            for callback in self.alert_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    logger.error(f"Alert callback error: {e}")
            
            logger.warning(f"Alert created: {title} - {message}")
            
        except Exception as e:
            logger.error(f"Create alert error: {e}")
    
    async def _process_alerts(self):
        """处理告警"""
        try:
            # 自动解决一些告警
            for alert in self.alerts:
                if not alert.resolved and alert.level == AlertLevel.INFO:
                    # 信息级别告警1小时后自动解决
                    if datetime.now() - alert.timestamp > timedelta(hours=1):
                        alert.resolved = True
                        alert.resolve_time = datetime.now()
                        
        except Exception as e:
            logger.error(f"Process alerts error: {e}")
    
    def register_alert_callback(self, callback: Callable):
        """注册告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            latest_metrics = self.system_metrics_history[-1] if self.system_metrics_history else None
            
            return {
                "monitoring_active": self.monitoring_active,
                "last_check_time": self.last_check_time.isoformat(),
                "system_metrics": {
                    "cpu_percent": latest_metrics.cpu_percent if latest_metrics else 0,
                    "memory_percent": latest_metrics.memory_percent if latest_metrics else 0,
                    "disk_percent": latest_metrics.disk_percent if latest_metrics else 0,
                    "process_count": latest_metrics.process_count if latest_metrics else 0,
                    "uptime": latest_metrics.uptime if latest_metrics else 0
                } if latest_metrics else {},
                "alert_summary": {
                    "total": len(self.alerts),
                    "unresolved": len([a for a in self.alerts if not a.resolved]),
                    "critical": len([a for a in self.alerts if a.level == AlertLevel.CRITICAL and not a.resolved]),
                    "error": len([a for a in self.alerts if a.level == AlertLevel.ERROR and not a.resolved]),
                    "warning": len([a for a in self.alerts if a.level == AlertLevel.WARNING and not a.resolved])
                }
            }
        except Exception as e:
            logger.error(f"Get system status error: {e}")
            return {"error": str(e)}
    
    def get_trading_status(self) -> Dict:
        """获取交易状态"""
        try:
            latest_metrics = self.trading_metrics_history[-1] if self.trading_metrics_history else None
            
            return {
                "trading_metrics": {
                    "order_count": latest_metrics.order_count if latest_metrics else 0,
                    "trade_count": latest_metrics.trade_count if latest_metrics else 0,
                    "success_rate": latest_metrics.order_success_rate if latest_metrics else 0,
                    "avg_response_time": latest_metrics.avg_response_time if latest_metrics else 0,
                    "active_connections": latest_metrics.active_connections if latest_metrics else 0,
                    "error_count": latest_metrics.error_count if latest_metrics else 0
                } if latest_metrics else {},
                "performance_status": "normal"  # 简化状态
            }
        except Exception as e:
            logger.error(f"Get trading status error: {e}")
            return {"error": str(e)}
    
    def get_alerts(self, limit: int = 50) -> List[Dict]:
        """获取告警列表"""
        try:
            recent_alerts = self.alerts[-limit:] if self.alerts else []
            return [
                {
                    "id": alert.id,
                    "level": alert.level.value,
                    "type": alert.type.value,
                    "title": alert.title,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat(),
                    "resolved": alert.resolved,
                    "resolve_time": alert.resolve_time.isoformat() if alert.resolve_time else None
                }
                for alert in reversed(recent_alerts)
            ]
        except Exception as e:
            logger.error(f"Get alerts error: {e}")
            return []


# 全局监控服务实例
_monitoring_service: Optional[MonitoringService] = None


def get_monitoring_service() -> MonitoringService:
    """获取监控服务实例"""
    global _monitoring_service
    if _monitoring_service is None:
        _monitoring_service = MonitoringService()
    return _monitoring_service


async def initialize_monitoring_service() -> MonitoringService:
    """初始化监控服务"""
    service = get_monitoring_service()
    await service.start_monitoring()
    return service
