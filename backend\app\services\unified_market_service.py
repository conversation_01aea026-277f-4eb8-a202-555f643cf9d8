"""
统一市场数据服务
整合所有数据源，提供统一接口，实现数据源降级机制
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

from app.core.config import settings
from app.services.mock_market_service import mock_market_service

logger = logging.getLogger(__name__)

@dataclass
class DataSourceStatus:
    """数据源状态"""
    name: str
    available: bool
    last_check: datetime
    error_message: Optional[str] = None
    priority: int = 0  # 优先级，数字越小优先级越高

class UnifiedMarketService:
    """统一市场数据服务"""
    
    def __init__(self):
        self.data_sources = {}
        self.source_status = {}
        self.current_source = "mock"
        self.cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        # 初始化数据源
        self._init_data_sources()
    
    def _init_data_sources(self):
        """初始化数据源"""
        logger.info("🔧 初始化数据源...")
        
        # Mock数据源（总是可用）
        self.source_status["mock"] = DataSourceStatus(
            name="Mock数据源",
            available=True,
            last_check=datetime.now(),
            priority=99  # 最低优先级
        )
        
        # AkShare数据源
        try:
            import akshare as ak
            self.source_status["akshare"] = DataSourceStatus(
                name="AkShare数据源",
                available=True,
                last_check=datetime.now(),
                priority=2
            )
            logger.info("✅ AkShare数据源已加载")
        except ImportError as e:
            self.source_status["akshare"] = DataSourceStatus(
                name="AkShare数据源",
                available=False,
                last_check=datetime.now(),
                error_message=f"导入失败: {e}",
                priority=2
            )
            logger.warning("❌ AkShare数据源不可用")
        
        # Tushare数据源
        try:
            if settings.TUSHARE_TOKEN and settings.TUSHARE_TOKEN != "your_tushare_token_here":
                import tushare as ts
                ts.set_token(settings.TUSHARE_TOKEN)
                self.source_status["tushare"] = DataSourceStatus(
                    name="Tushare数据源",
                    available=True,
                    last_check=datetime.now(),
                    priority=1  # 最高优先级
                )
                logger.info("✅ Tushare数据源已配置")
            else:
                self.source_status["tushare"] = DataSourceStatus(
                    name="Tushare数据源",
                    available=False,
                    last_check=datetime.now(),
                    error_message="Token未配置",
                    priority=1
                )
                logger.warning("⚠️ Tushare Token未配置")
        except Exception as e:
            self.source_status["tushare"] = DataSourceStatus(
                name="Tushare数据源",
                available=False,
                last_check=datetime.now(),
                error_message=str(e),
                priority=1
            )
            logger.error(f"❌ Tushare数据源初始化失败: {e}")
        
        # 选择当前数据源
        self._select_best_source()
    
    def _select_best_source(self):
        """选择最佳数据源"""
        available_sources = [
            (name, status) for name, status in self.source_status.items()
            if status.available
        ]
        
        if available_sources:
            # 按优先级排序
            available_sources.sort(key=lambda x: x[1].priority)
            self.current_source = available_sources[0][0]
            logger.info(f"🎯 当前数据源: {self.current_source}")
        else:
            self.current_source = "mock"
            logger.warning("⚠️ 所有数据源不可用，使用Mock数据")
    
    async def get_stock_list(self, market: str = None, limit: int = 100) -> List[Dict]:
        """获取股票列表"""
        cache_key = f"stock_list_{market}_{limit}"
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]["data"]
        
        try:
            if self.current_source == "tushare":
                data = await self._get_stock_list_tushare(market, limit)
            elif self.current_source == "akshare":
                data = await self._get_stock_list_akshare(market, limit)
            else:
                data = await self._get_stock_list_mock(market, limit)
            
            # 缓存结果
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            logger.error(f"获取股票列表失败 ({self.current_source}): {e}")
            # 降级到下一个数据源
            return await self._fallback_get_stock_list(market, limit)
    
    async def get_realtime_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """获取实时行情"""
        cache_key = f"quotes_{','.join(symbols)}"
        
        # 实时数据缓存时间较短
        if self._is_cache_valid(cache_key, ttl=30):
            return self.cache[cache_key]["data"]
        
        try:
            if self.current_source == "tushare":
                data = await self._get_quotes_tushare(symbols)
            elif self.current_source == "akshare":
                data = await self._get_quotes_akshare(symbols)
            else:
                data = await self._get_quotes_mock(symbols)
            
            self._cache_data(cache_key, data, ttl=30)
            return data
            
        except Exception as e:
            logger.error(f"获取实时行情失败 ({self.current_source}): {e}")
            return await self._fallback_get_quotes(symbols)
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict]:
        """获取K线数据"""
        cache_key = f"kline_{symbol}_{period}_{limit}"
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]["data"]
        
        try:
            if self.current_source == "tushare":
                data = await self._get_kline_tushare(symbol, period, limit)
            elif self.current_source == "akshare":
                data = await self._get_kline_akshare(symbol, period, limit)
            else:
                data = await self._get_kline_mock(symbol, period, limit)
            
            self._cache_data(cache_key, data)
            return data
            
        except Exception as e:
            logger.error(f"获取K线数据失败 ({self.current_source}): {e}")
            return await self._fallback_get_kline(symbol, period, limit)
    
    # ============ 数据源实现方法 ============
    
    async def _get_stock_list_mock(self, market: str = None, limit: int = 100) -> List[Dict]:
        """Mock数据源获取股票列表"""
        stocks = await mock_market_service.get_stock_list()
        return stocks[:limit] if limit else stocks
    
    async def _get_quotes_mock(self, symbols: List[str]) -> Dict[str, Any]:
        """Mock数据源获取行情"""
        quotes = {}
        for symbol in symbols:
            quote = await mock_market_service.get_realtime_quote(symbol)
            if quote:
                quotes[symbol] = quote
        return quotes
    
    async def _get_kline_mock(self, symbol: str, period: str, limit: int) -> List[Dict]:
        """Mock数据源获取K线"""
        return await mock_market_service.get_kline_data(symbol, period, limit)
    
    # ============ 降级处理方法 ============
    
    async def _fallback_get_stock_list(self, market: str = None, limit: int = 100) -> List[Dict]:
        """股票列表降级处理"""
        logger.warning("🔄 数据源降级，使用Mock数据")
        return await self._get_stock_list_mock(market, limit)
    
    async def _fallback_get_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """行情数据降级处理"""
        logger.warning("🔄 数据源降级，使用Mock数据")
        return await self._get_quotes_mock(symbols)
    
    async def _fallback_get_kline(self, symbol: str, period: str, limit: int) -> List[Dict]:
        """K线数据降级处理"""
        logger.warning("🔄 数据源降级，使用Mock数据")
        return await self._get_kline_mock(symbol, period, limit)
    
    # ============ 缓存管理 ============
    
    def _is_cache_valid(self, key: str, ttl: int = None) -> bool:
        """检查缓存是否有效"""
        if key not in self.cache:
            return False
        
        cache_ttl = ttl or self.cache_ttl
        cache_time = self.cache[key]["timestamp"]
        return (datetime.now() - cache_time).total_seconds() < cache_ttl
    
    def _cache_data(self, key: str, data: Any, ttl: int = None):
        """缓存数据"""
        self.cache[key] = {
            "data": data,
            "timestamp": datetime.now(),
            "ttl": ttl or self.cache_ttl
        }
    
    def get_source_status(self) -> Dict[str, DataSourceStatus]:
        """获取数据源状态"""
        return self.source_status
    
    def get_current_source(self) -> str:
        """获取当前数据源"""
        return self.current_source

# 创建全局实例
unified_market_service = UnifiedMarketService()
