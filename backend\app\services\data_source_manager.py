"""
数据源管理器 - 统一数据源选择与切换
实现三层数据源优先级：Tushare -> AkShare -> Mock
"""

import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod

from app.core.config import get_settings
from app.core.exceptions import DataSourceException, MarketDataException

logger = logging.getLogger(__name__)
settings = get_settings()


class DataSourceType(str, Enum):
    """数据源类型枚举"""
    TUSHARE = "tushare"
    AKSHARE = "akshare" 
    MOCK = "mock"


class DataSourceStatus(str, Enum):
    """数据源状态枚举"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class DataSourceInfo:
    """数据源信息"""
    type: DataSourceType
    status: DataSourceStatus
    priority: int
    description: str
    last_check: Optional[str] = None
    error_message: Optional[str] = None


class DataSourceInterface(ABC):
    """数据源接口抽象类"""
    
    @abstractmethod
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取股票列表"""
        pass
    
    @abstractmethod
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """获取实时行情"""
        pass
    
    @abstractmethod
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """批量获取行情"""
        pass
    
    @abstractmethod
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        """获取K线数据"""
        pass
    
    @abstractmethod
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索股票"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass


class TushareDataSource(DataSourceInterface):
    """Tushare数据源实现"""

    def __init__(self, token: Optional[str] = None):
        self.token = token or settings.TUSHARE_API_TOKEN
        self.available = bool(self.token)
        # 导入Tushare服务
        if self.available:
            try:
                from app.services.tushare_data_service import TushareDataService
                self.tushare_service = TushareDataService(self.token)
            except ImportError:
                logger.warning("Tushare服务导入失败")
                self.available = False

    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")

        logger.info(f"Fetching stock list from Tushare, market: {market}")
        try:
            # 调用Tushare服务获取股票基本信息
            stock_basic = await self.tushare_service.get_stock_basic()

            # 转换为标准格式
            result = []
            for item in stock_basic:
                if isinstance(item, list) and len(item) >= 7:
                    # Tushare返回的是列表格式
                    ts_code, symbol, name, area, industry, market_code, list_date = item[:7]
                    stock_data = {
                        "symbol": ts_code,  # 使用ts_code作为symbol
                        "name": name,
                        "market": market_code,
                        "industry": industry,
                        "list_date": list_date
                    }
                    result.append(stock_data)

            # 如果指定了市场，进行过滤
            if market:
                result = [item for item in result if market.upper() in item["symbol"]]

            return result

        except Exception as e:
            logger.error(f"Tushare获取股票列表失败: {e}")
            raise DataSourceException(f"Tushare API调用失败: {e}")
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        logger.info(f"Fetching realtime quote from Tushare: {symbol}")
        try:
            # 调用Tushare服务获取实时行情
            quotes = await self.tushare_service.get_realtime_quote(symbol)

            if quotes and len(quotes) > 0:
                quote_data = quotes[0]
                if isinstance(quote_data, list) and len(quote_data) >= 10:
                    # Tushare实时数据格式解析
                    ts_code, trade_date, open_price, high, low, close, pre_close, change, pct_chg, vol = quote_data[:10]
                    return {
                        "symbol": symbol,
                        "price": float(close) if close else 0,
                        "change": float(change) if change else 0,
                        "change_pct": float(pct_chg) if pct_chg else 0,
                        "volume": int(vol) if vol else 0,
                        "turnover": 0,  # Tushare实时数据可能不包含成交额
                        "high": float(high) if high else 0,
                        "low": float(low) if low else 0,
                        "open": float(open_price) if open_price else 0,
                        "close": float(pre_close) if pre_close else 0,
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

            # 如果没有实时数据，返回默认值
            logger.warning(f"Tushare未找到股票{symbol}的实时数据")
            return {
                "symbol": symbol,
                "price": 0,
                "change": 0,
                "change_pct": 0,
                "volume": 0,
                "turnover": 0,
                "high": 0,
                "low": 0,
                "open": 0,
                "close": 0,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"Tushare获取{symbol}实时行情失败: {e}")
            raise DataSourceException(f"Tushare API调用失败: {e}")
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        logger.info(f"Fetching kline data from Tushare: {symbol}, period: {period}")
        try:
            # 计算日期范围
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=limit)).strftime('%Y%m%d')

            # 调用Tushare服务获取日线数据
            daily_data = await self.tushare_service.get_daily_data(symbol, start_date, end_date)

            # 转换为标准格式
            result = []
            for item in daily_data:
                if isinstance(item, list) and len(item) >= 9:
                    # Tushare日线数据格式：ts_code, trade_date, open, high, low, close, pre_close, change, pct_chg, vol, amount
                    ts_code, trade_date, open_price, high, low, close, pre_close, change, pct_chg = item[:9]
                    vol = item[9] if len(item) > 9 else 0

                    try:
                        # 转换日期为时间戳
                        date_obj = datetime.strptime(str(trade_date), '%Y%m%d')
                        timestamp = int(date_obj.timestamp() * 1000)
                    except:
                        timestamp = 0

                    kline_item = {
                        "timestamp": timestamp,
                        "open": float(open_price) if open_price else 0,
                        "high": float(high) if high else 0,
                        "low": float(low) if low else 0,
                        "close": float(close) if close else 0,
                        "volume": int(vol) if vol else 0
                    }
                    result.append(kline_item)

            # 按时间戳排序并限制数量
            result.sort(key=lambda x: x['timestamp'])
            return result[-limit:] if len(result) > limit else result

        except Exception as e:
            logger.error(f"Tushare获取{symbol}K线数据失败: {e}")
            raise DataSourceException(f"Tushare API调用失败: {e}")
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        logger.info(f"Searching stocks from Tushare: {keyword}")
        # 模拟搜索结果
        return [
            {
                "symbol": "000001.SZ",
                "name": "平安银行",
                "market": "SZ",
                "industry": "银行"
            }
        ]
    
    async def health_check(self) -> bool:
        return self.available


class AkShareDataSource(DataSourceInterface):
    """AkShare数据源实现"""

    def __init__(self):
        self.available = settings.AKSHARE_ENABLED
        # 导入AKShare服务
        try:
            from app.services.akshare_data_service import akshare_service
            self.akshare_service = akshare_service
            self.available = True
        except ImportError:
            logger.warning("AKShare服务导入失败")
            self.available = False

    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")

        logger.info(f"Fetching stock list from AkShare, market: {market}")
        try:
            # 调用AKShare服务获取股票列表
            stock_info = await self.akshare_service.get_stock_info_a()

            # 转换为标准格式
            result = []
            for item in stock_info:
                stock_data = {
                    "symbol": item.get("code", ""),
                    "name": item.get("name", ""),
                    "market": "SZ" if item.get("code", "").startswith(("000", "002", "300")) else "SH",
                    "industry": "",
                    "list_date": ""
                }
                result.append(stock_data)

            # 如果指定了市场，进行过滤
            if market:
                result = [item for item in result if item["market"] == market.upper()]

            return result

        except Exception as e:
            logger.error(f"AkShare获取股票列表失败: {e}")
            raise DataSourceException(f"AkShare API调用失败: {e}")
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")

        logger.info(f"Fetching realtime quote from AkShare: {symbol}")
        try:
            # 获取实时行情数据
            realtime_data = await self.akshare_service.get_stock_zh_a_spot_em(use_cache=False)

            # 查找指定股票的数据
            for item in realtime_data:
                if item.get("代码") == symbol or item.get("symbol") == symbol:
                    return {
                        "symbol": symbol,
                        "price": float(item.get("最新价", 0)),
                        "change": float(item.get("涨跌额", 0)),
                        "change_pct": float(item.get("涨跌幅", 0)),
                        "volume": int(item.get("成交量", 0)),
                        "turnover": float(item.get("成交额", 0)),
                        "high": float(item.get("最高", 0)),
                        "low": float(item.get("最低", 0)),
                        "open": float(item.get("今开", 0)),
                        "close": float(item.get("昨收", 0)),
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

            # 如果没找到，返回默认数据
            logger.warning(f"AkShare未找到股票{symbol}的实时数据")
            return {
                "symbol": symbol,
                "price": 0,
                "change": 0,
                "change_pct": 0,
                "volume": 0,
                "turnover": 0,
                "high": 0,
                "low": 0,
                "open": 0,
                "close": 0,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"AkShare获取{symbol}实时行情失败: {e}")
            raise DataSourceException(f"AkShare API调用失败: {e}")
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        logger.info(f"Fetching kline data from AkShare: {symbol}, period: {period}")
        try:
            # 计算开始日期（根据limit参数）
            start_date = (datetime.now() - timedelta(days=limit)).strftime('%Y%m%d')
            end_date = datetime.now().strftime('%Y%m%d')

            # 获取历史数据
            hist_data = await self.akshare_service.get_stock_zh_a_hist(
                symbol=symbol,
                period="daily",  # AkShare目前主要支持日线
                start_date=start_date,
                end_date=end_date
            )

            # 转换为标准格式
            result = []
            for item in hist_data:
                if item.get('日期'):
                    try:
                        # 使用pandas处理日期转换
                        import pandas as pd
                        timestamp = int(pd.to_datetime(item['日期']).timestamp() * 1000)
                    except:
                        # 如果pandas不可用，使用datetime
                        date_obj = datetime.strptime(str(item['日期']), '%Y-%m-%d')
                        timestamp = int(date_obj.timestamp() * 1000)

                    kline_item = {
                        "timestamp": timestamp,
                        "open": float(item.get('开盘', 0)),
                        "high": float(item.get('最高', 0)),
                        "low": float(item.get('最低', 0)),
                        "close": float(item.get('收盘', 0)),
                        "volume": int(item.get('成交量', 0))
                    }
                    result.append(kline_item)

            # 按时间戳排序并限制数量
            result.sort(key=lambda x: x['timestamp'])
            return result[-limit:] if len(result) > limit else result

        except Exception as e:
            logger.error(f"AkShare获取{symbol}K线数据失败: {e}")
            raise DataSourceException(f"AkShare API调用失败: {e}")
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        logger.info(f"Searching stocks from AkShare: {keyword}")
        try:
            # 获取股票列表
            stock_list = await self.get_stock_list()

            # 搜索匹配的股票
            results = []
            keyword_lower = keyword.lower()

            for stock in stock_list:
                symbol = stock.get("symbol", "")
                name = stock.get("name", "")

                # 检查代码或名称是否匹配
                if (keyword_lower in symbol.lower() or
                    keyword_lower in name.lower() or
                    keyword in symbol or keyword in name):
                    results.append({
                        "symbol": symbol,
                        "name": name,
                        "market": stock.get("market", "")
                    })

                # 限制返回结果数量
                if len(results) >= 20:
                    break

            return results

        except Exception as e:
            logger.error(f"AkShare搜索股票失败: {e}")
            raise DataSourceException(f"AkShare API调用失败: {e}")
    
    async def health_check(self) -> bool:
        if not self.available:
            return False

        try:
            # 尝试获取少量数据来验证服务可用性
            test_data = await self.akshare_service.get_stock_zh_a_spot_em(use_cache=True)
            return len(test_data) > 0
        except Exception as e:
            logger.warning(f"AkShare健康检查失败: {e}")
            return False


class MockDataSource(DataSourceInterface):
    """Mock数据源实现 - 兜底方案"""
    
    def __init__(self):
        self.available = True
        
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        logger.info(f"Fetching stock list from Mock, market: {market}")
        mock_stocks = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
            {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产"},
            {"symbol": "600000", "name": "浦发银行", "market": "SH", "industry": "银行"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
        ]
        
        if market:
            return [stock for stock in mock_stocks if stock["market"] == market]
        return mock_stocks
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        logger.info(f"Fetching realtime quote from Mock: {symbol}")
        import random
        base_price = 10.0 + random.random() * 20
        change = (random.random() - 0.5) * 2
        
        return {
            "symbol": symbol,
            "price": round(base_price + change, 2),
            "change": round(change, 2),
            "change_pct": round((change / base_price) * 100, 2),
            "volume": random.randint(10000000, 200000000),
            "turnover": round(random.uniform(100000000, 2000000000), 2),
            "high": round(base_price + abs(change) + random.random(), 2),
            "low": round(base_price - abs(change) - random.random(), 2),
            "open": round(base_price + (random.random() - 0.5), 2),
            "close": round(base_price, 2),
            "timestamp": "2025-01-14 15:00:00"
        }
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        logger.info(f"Fetching kline data from Mock: {symbol}, period: {period}")
        import random
        from datetime import datetime, timedelta
        
        klines = []
        base_price = 10.0 + random.random() * 20
        start_date = datetime.now() - timedelta(days=limit)
        
        for i in range(limit):
            date = start_date + timedelta(days=i)
            open_price = base_price + (random.random() - 0.5) * 2
            close_price = open_price + (random.random() - 0.5) * 2
            high_price = max(open_price, close_price) + random.random()
            low_price = min(open_price, close_price) - random.random()
            
            klines.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(10000000, 200000000),
                "amount": round(random.uniform(100000000, 2000000000), 2)
            })
            base_price = close_price
            
        return klines
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        logger.info(f"Searching stocks from Mock: {keyword}")
        mock_results = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
        ]
        
        # 简单的关键词匹配
        return [stock for stock in mock_results if keyword.lower() in stock["name"].lower() or keyword in stock["symbol"]]
    
    async def health_check(self) -> bool:
        return True


class DataSourceManager:
    """数据源管理器 - 实现智能切换和故障转移"""
    
    def __init__(self):
        self.sources = {}
        self.priority_order = []
        self.current_source = None
        self._initialize_sources()
    
    def _initialize_sources(self):
        """初始化数据源"""
        # 按优先级初始化数据源
        self.sources[DataSourceType.TUSHARE] = TushareDataSource()
        self.sources[DataSourceType.AKSHARE] = AkShareDataSource()
        self.sources[DataSourceType.MOCK] = MockDataSource()
        
        # 设置优先级顺序
        if settings.USE_REAL_DATA:
            if settings.PREFERRED_DATA_SOURCE == "tushare":
                self.priority_order = [DataSourceType.TUSHARE, DataSourceType.AKSHARE, DataSourceType.MOCK]
            else:
                self.priority_order = [DataSourceType.AKSHARE, DataSourceType.TUSHARE, DataSourceType.MOCK]
        else:
            self.priority_order = [DataSourceType.MOCK]
        
        logger.info(f"Data source priority order: {self.priority_order}")
    
    async def get_available_source(self) -> DataSourceInterface:
        """获取可用的数据源"""
        for source_type in self.priority_order:
            source = self.sources[source_type]
            try:
                if await source.health_check():
                    if self.current_source != source_type:
                        logger.info(f"Switching to data source: {source_type}")
                        self.current_source = source_type
                    return source
            except Exception as e:
                logger.warning(f"Data source {source_type} health check failed: {e}")
                continue
        
        raise DataSourceException("No available data source")
    
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取股票列表"""
        source = await self.get_available_source()
        return await source.get_stock_list(market)
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """获取实时行情 - 支持故障转移"""
        for source_type in self.priority_order:
            source = self.sources[source_type]
            try:
                if await source.health_check():
                    result = await source.get_realtime_quote(symbol)
                    if result and result.get("price", 0) > 0:  # 验证数据有效性
                        if self.current_source != source_type:
                            logger.info(f"Successfully switched to {source_type} for realtime quote")
                            self.current_source = source_type
                        return result
                    else:
                        logger.warning(f"{source_type} returned invalid quote data for {symbol}")
            except Exception as e:
                logger.warning(f"{source_type} failed to get quote for {symbol}: {e}")
                continue

        # 如果所有数据源都失败，返回默认数据
        logger.error(f"All data sources failed for symbol {symbol}")
        return {
            "symbol": symbol,
            "price": 0,
            "change": 0,
            "change_pct": 0,
            "volume": 0,
            "turnover": 0,
            "high": 0,
            "low": 0,
            "open": 0,
            "close": 0,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """批量获取行情"""
        source = await self.get_available_source()
        return await source.get_batch_quotes(symbols)
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        """获取K线数据"""
        source = await self.get_available_source()
        return await source.get_kline_data(symbol, period, limit)
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索股票"""
        source = await self.get_available_source()
        return await source.search_stocks(keyword)

    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """批量获取股票行情"""
        results = []
        for symbol in symbols:
            try:
                quote = await self.get_realtime_quote(symbol)
                results.append(quote)
            except Exception as e:
                logger.warning(f"Failed to get quote for {symbol}: {e}")
                # 添加默认数据避免中断
                results.append({
                    "symbol": symbol,
                    "price": 0,
                    "change": 0,
                    "change_pct": 0,
                    "volume": 0,
                    "turnover": 0,
                    "high": 0,
                    "low": 0,
                    "open": 0,
                    "close": 0,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
        return results
    
    async def get_source_status(self) -> Dict[str, DataSourceInfo]:
        """获取所有数据源状态"""
        status = {}
        for source_type, source in self.sources.items():
            try:
                is_available = await source.health_check()
                status[source_type] = DataSourceInfo(
                    type=source_type,
                    status=DataSourceStatus.AVAILABLE if is_available else DataSourceStatus.UNAVAILABLE,
                    priority=self.priority_order.index(source_type) if source_type in self.priority_order else -1,
                    description=f"{source_type.title()} data source"
                )
            except Exception as e:
                status[source_type] = DataSourceInfo(
                    type=source_type,
                    status=DataSourceStatus.ERROR,
                    priority=self.priority_order.index(source_type) if source_type in self.priority_order else -1,
                    description=f"{source_type.title()} data source",
                    error_message=str(e)
                )
        
        return status


# 全局数据源管理器实例
data_source_manager = DataSourceManager()