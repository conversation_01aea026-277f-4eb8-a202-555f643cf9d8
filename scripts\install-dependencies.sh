#!/bin/bash

# 行情中心依赖安装脚本
# 用途: 一键安装行情中心所需的所有依赖

set -e  # 遇到错误立即退出

echo "🚀 开始安装行情中心依赖..."

# 检查Python环境
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

python_version=$(python3 --version | cut -d' ' -f2)
echo "✅ Python版本: $python_version"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

# 安装后端依赖
echo "📦 安装后端Python依赖..."
cd backend

# 创建虚拟环境 (可选)
if [ "$1" = "--venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ 虚拟环境已激活"
fi

# 升级pip
pip3 install --upgrade pip

# 安装基础依赖
echo "📦 安装基础依赖..."
pip3 install -r requirements.txt

# 安装行情数据源依赖
echo "📈 安装行情数据源依赖..."
pip3 install tushare akshare pandas-ta

# 安装可选依赖
echo "🔧 安装可选依赖..."
pip3 install redis aioredis

echo "✅ 后端依赖安装完成"

# 返回根目录
cd ..

# 检查Node.js环境
echo "📋 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装Node.js"
    exit 1
fi

node_version=$(node --version)
echo "✅ Node.js版本: $node_version"

# 检查pnpm
if ! command -v pnpm &> /dev/null; then
    echo "📦 安装pnpm..."
    npm install -g pnpm
fi

pnpm_version=$(pnpm --version)
echo "✅ pnpm版本: $pnpm_version"

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
pnpm install

echo "✅ 前端依赖安装完成"

# 返回根目录
cd ..

# 检查Redis
echo "📋 检查Redis服务..."
if command -v redis-server &> /dev/null; then
    redis_version=$(redis-server --version | head -n1)
    echo "✅ Redis已安装: $redis_version"
    
    # 检查Redis是否运行
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis服务正在运行"
    else
        echo "⚠️  Redis服务未运行，请启动Redis:"
        echo "   redis-server --port 6379"
    fi
else
    echo "⚠️  Redis未安装，建议安装Redis以获得更好的缓存性能"
    echo "   Ubuntu/Debian: sudo apt-get install redis-server"
    echo "   CentOS/RHEL: sudo yum install redis"
    echo "   macOS: brew install redis"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p backend/data
mkdir -p backend/logs
mkdir -p backend/uploads

echo "✅ 目录创建完成"

# 检查环境配置文件
echo "📋 检查环境配置..."
if [ ! -f "backend/.env" ]; then
    echo "⚠️  后端.env文件不存在，请配置环境变量"
    echo "   参考: backend/.env.example"
fi

if [ ! -f "frontend/.env.development" ]; then
    echo "⚠️  前端.env.development文件不存在，请配置环境变量"
    echo "   参考: frontend/.env.example"
fi

# 显示下一步操作
echo ""
echo "🎉 依赖安装完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 配置环境变量:"
echo "   - 编辑 backend/.env"
echo "   - 设置 TUSHARE_TOKEN=your_actual_token"
echo "   - 设置 USE_REAL_DATA=true"
echo ""
echo "2. 启动Redis服务 (如果未运行):"
echo "   redis-server --port 6379"
echo ""
echo "3. 启动后端服务:"
echo "   cd backend"
if [ "$1" = "--venv" ]; then
    echo "   source venv/bin/activate"
fi
echo "   python main.py"
echo ""
echo "4. 启动前端服务:"
echo "   cd frontend"
echo "   pnpm dev"
echo ""
echo "5. 验证安装:"
echo "   访问 http://localhost:8000/docs (后端API文档)"
echo "   访问 http://localhost:5173 (前端页面)"
echo ""
echo "📖 详细文档: docs/market-api-spec.md"
echo "✅ 验收清单: docs/market-verification-checklist.md"
