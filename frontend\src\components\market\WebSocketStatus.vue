<template>
  <div class="websocket-status">
    <!-- 连接状态指示器 -->
    <div class="status-indicator" :class="connectionStatus">
      <div class="status-dot" :style="{ backgroundColor: connectionStatusColor }"></div>
      <span class="status-text">{{ connectionStatusText }}</span>
      
      <!-- 手动重连按钮 -->
      <el-button
        v-if="canManualReconnect"
        type="primary"
        size="small"
        :loading="isConnecting"
        @click="handleManualReconnect"
        class="reconnect-btn"
      >
        <el-icon><Refresh /></el-icon>
        重连
      </el-button>
    </div>

    <!-- 详细信息（可展开） -->
    <el-collapse v-if="showDetails" v-model="activeCollapse" class="status-details">
      <el-collapse-item title="连接详情" name="connection">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">连接状态:</span>
            <span class="value">{{ connectionStatusText }}</span>
          </div>
          <div class="detail-item">
            <span class="label">重连次数:</span>
            <span class="value">{{ reconnectAttempts }}/{{ maxReconnectAttempts }}</span>
          </div>
          <div class="detail-item">
            <span class="label">订阅数量:</span>
            <span class="value">{{ subscriptions.length }}</span>
          </div>
          <div class="detail-item">
            <span class="label">最后连接:</span>
            <span class="value">{{ formatLastConnection }}</span>
          </div>
        </div>
      </el-collapse-item>

      <el-collapse-item title="性能统计" name="stats">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">接收消息:</span>
            <span class="value">{{ connectionStats.totalMessages }}</span>
          </div>
          <div class="detail-item">
            <span class="label">最后消息:</span>
            <span class="value">{{ formatLastMessage }}</span>
          </div>
          <div class="detail-item">
            <span class="label">活跃订阅:</span>
            <span class="value">{{ latestQuotes.size }}</span>
          </div>
        </div>
      </el-collapse-item>

      <el-collapse-item v-if="subscriptions.length > 0" title="订阅列表" name="subscriptions">
        <div class="subscriptions-list">
          <el-tag
            v-for="symbol in subscriptions"
            :key="symbol"
            :type="latestQuotes.has(symbol) ? 'success' : 'info'"
            size="small"
            class="subscription-tag"
          >
            {{ symbol }}
            <span v-if="latestQuotes.has(symbol)" class="price">
              ¥{{ latestQuotes.get(symbol)?.price?.toFixed(2) }}
            </span>
          </el-tag>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { useMarketWebSocket } from '@/composables/useMarketWebSocket'

interface Props {
  showDetails?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: false,
  compact: false
})

// 使用WebSocket状态管理
const {
  isConnected,
  isConnecting,
  reconnectAttempts,
  maxReconnectAttempts,
  subscriptions,
  latestQuotes,
  connectionStats,
  connectionStatus,
  connectionStatusText,
  connectionStatusColor,
  canManualReconnect,
  manualReconnect,
  lastSuccessfulConnection
} = useMarketWebSocket()

// 本地状态
const activeCollapse = ref<string[]>([])

// 计算属性
const formatLastConnection = computed(() => {
  if (!lastSuccessfulConnection.value) return '从未连接'
  const diff = Date.now() - lastSuccessfulConnection.value
  if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  return new Date(lastSuccessfulConnection.value).toLocaleTimeString()
})

const formatLastMessage = computed(() => {
  if (!connectionStats.value.lastMessageTime) return '无'
  const diff = Date.now() - connectionStats.value.lastMessageTime
  if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`
  return new Date(connectionStats.value.lastMessageTime).toLocaleTimeString()
})

// 方法
const handleManualReconnect = async () => {
  try {
    await manualReconnect()
  } catch (error) {
    ElMessage.error('重连失败，请稍后再试')
  }
}
</script>

<style scoped>
.websocket-status {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  padding: 12px;
  background: var(--el-bg-color);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.connecting .status-dot {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
  flex: 1;
}

.reconnect-btn {
  margin-left: auto;
}

.status-details {
  margin-top: 12px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
}

.detail-item .label {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.detail-item .value {
  color: var(--el-text-color-primary);
  font-size: 13px;
  font-weight: 500;
}

.subscriptions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.subscription-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.subscription-tag .price {
  font-size: 11px;
  opacity: 0.8;
}

/* 紧凑模式 */
.websocket-status.compact {
  padding: 6px 8px;
  border: none;
  background: transparent;
}

.websocket-status.compact .status-indicator {
  font-size: 12px;
}

.websocket-status.compact .status-dot {
  width: 6px;
  height: 6px;
}
</style>
