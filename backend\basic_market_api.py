"""
基础市场数据API
提供前端需要的基本市场数据
"""

from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import List, Optional
import random
import datetime
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="基础市场数据API",
    description="提供基本的市场数据服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟股票数据
MOCK_STOCKS = [
    {"symbol": "000001", "name": "平安银行", "price": 12.45, "change": 0.23, "change_percent": 1.88},
    {"symbol": "000002", "name": "万科A", "price": 18.76, "change": -0.45, "change_percent": -2.34},
    {"symbol": "000858", "name": "五粮液", "price": 168.90, "change": 3.21, "change_percent": 1.94},
    {"symbol": "000876", "name": "新希望", "price": 15.67, "change": 0.89, "change_percent": 6.02},
    {"symbol": "002415", "name": "海康威视", "price": 32.45, "change": -1.23, "change_percent": -3.65},
    {"symbol": "002594", "name": "比亚迪", "price": 245.67, "change": 8.90, "change_percent": 3.76},
    {"symbol": "300059", "name": "东方财富", "price": 19.87, "change": 0.67, "change_percent": 3.49},
    {"symbol": "300750", "name": "宁德时代", "price": 189.45, "change": -5.67, "change_percent": -2.91},
    {"symbol": "600036", "name": "招商银行", "price": 38.90, "change": 1.45, "change_percent": 3.87},
    {"symbol": "600519", "name": "贵州茅台", "price": 1678.90, "change": 23.45, "change_percent": 1.42},
    {"symbol": "600887", "name": "伊利股份", "price": 28.67, "change": -0.78, "change_percent": -2.65},
    {"symbol": "601318", "name": "中国平安", "price": 45.67, "change": 2.34, "change_percent": 5.40},
    {"symbol": "601398", "name": "工商银行", "price": 5.23, "change": 0.12, "change_percent": 2.35},
    {"symbol": "601888", "name": "中国中免", "price": 89.45, "change": -3.21, "change_percent": -3.47},
    {"symbol": "688981", "name": "中芯国际", "price": 56.78, "change": 4.56, "change_percent": 8.73},
]

# 健康检查
@app.get("/api/v1/health")
async def health_check():
    return JSONResponse({
        "status": "ok",
        "message": "基础市场数据API运行正常",
        "version": "1.0.0"
    })

# 市场概览
@app.get("/api/v1/market/overview")
async def market_overview():
    return JSONResponse({
        "success": True,
        "data": {
            "total_stocks": len(MOCK_STOCKS),
            "rising_count": len([s for s in MOCK_STOCKS if s["change"] > 0]),
            "falling_count": len([s for s in MOCK_STOCKS if s["change"] < 0]),
            "unchanged_count": len([s for s in MOCK_STOCKS if s["change"] == 0]),
            "total_volume": random.randint(100000000, 500000000),
            "total_turnover": random.randint(200000000000, 800000000000),
            "timestamp": datetime.datetime.now().isoformat()
        }
    })

# 股票排行榜
@app.get("/api/v1/market/rankings")
async def market_rankings(
    sort_by: str = Query("change_percent", description="排序字段"),
    order: str = Query("desc", description="排序方向"),
    limit: int = Query(10, description="返回数量")
):
    # 排序股票数据
    reverse = order.lower() == "desc"
    sorted_stocks = sorted(MOCK_STOCKS, key=lambda x: x.get(sort_by, 0), reverse=reverse)
    
    return JSONResponse({
        "success": True,
        "data": {
            "stocks": sorted_stocks[:limit],
            "sort_by": sort_by,
            "order": order,
            "total": len(MOCK_STOCKS)
        }
    })

# 板块数据
@app.get("/api/v1/market/sectors")
async def market_sectors():
    sectors = [
        {"name": "银行", "change_percent": 2.34, "stocks_count": 36},
        {"name": "白酒", "change_percent": 1.87, "stocks_count": 18},
        {"name": "新能源", "change_percent": -1.23, "stocks_count": 45},
        {"name": "医药", "change_percent": 0.89, "stocks_count": 67},
        {"name": "科技", "change_percent": 3.45, "stocks_count": 89},
    ]
    
    return JSONResponse({
        "success": True,
        "data": {
            "sectors": sectors,
            "timestamp": datetime.datetime.now().isoformat()
        }
    })

# 市场新闻
@app.get("/api/v1/market/news")
async def market_news(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量")
):
    news = [
        {
            "id": i,
            "title": f"市场新闻标题 {i}",
            "summary": f"这是第{i}条市场新闻的摘要内容...",
            "source": "财经网",
            "publish_time": (datetime.datetime.now() - datetime.timedelta(hours=i)).isoformat(),
            "url": f"https://example.com/news/{i}"
        }
        for i in range(1, 21)
    ]
    
    start = (page - 1) * page_size
    end = start + page_size
    
    return JSONResponse({
        "success": True,
        "data": {
            "news": news[start:end],
            "page": page,
            "page_size": page_size,
            "total": len(news)
        }
    })

# 根路径
@app.get("/")
async def root():
    return JSONResponse({
        "message": "基础市场数据API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/v1/health"
    })

# 启动应用
if __name__ == "__main__":
    print("🚀 启动基础市场数据API...")
    uvicorn.run(
        "basic_market_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
