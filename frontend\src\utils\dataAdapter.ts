/**
 * 数据适配器
 * 处理后端API响应格式与前端类型的转换
 */

import type { QuoteData, KLineData } from '@/types/market'
import type { ApiResponse, BatchApiResponse, KLineApiResponse } from '@/types/api'

/**
 * 适配单股票行情数据
 */
export function adaptQuoteData(rawData: any): QuoteData {
  return {
    symbol: rawData.symbol || rawData.code,
    name: rawData.name,
    currentPrice: rawData.price || rawData.currentPrice,
    previousClose: rawData.pre_close || rawData.previousClose,
    change: rawData.change,
    changePercent: rawData.change_percent || rawData.changePercent,
    high: rawData.high,
    low: rawData.low,
    openPrice: rawData.open || rawData.openPrice,
    volume: rawData.volume,
    amount: rawData.amount,
    turnoverRate: rawData.turnover_rate || rawData.turnoverRate,
    pe: rawData.pe,
    timestamp: rawData.timestamp,
    status: rawData.status === 'TRADING' ? 'trading' : 'closed',
    market: rawData.market,
    sector: rawData.sector
  }
}

/**
 * 适配批量行情数据响应
 */
export function adaptBatchQuotesResponse(response: BatchApiResponse<any>): QuoteData[] {
  if (response.data.quotes) {
    // 新格式：data.quotes
    return response.data.quotes.map(adaptQuoteData)
  } else if (Array.isArray(response.data)) {
    // 旧格式：data直接是数组
    return response.data.map(adaptQuoteData)
  }
  return []
}

/**
 * 适配K线数据
 */
export function adaptKLineData(rawData: any): KLineData {
  return {
    timestamp: rawData.timestamp,
    open: rawData.open,
    high: rawData.high,
    low: rawData.low,
    close: rawData.close,
    volume: rawData.volume,
    turnover: rawData.amount || rawData.turnover || 0
  }
}

/**
 * 适配K线数据响应
 */
export function adaptKLineResponse(response: KLineApiResponse | ApiResponse<any[]>): {
  symbol: string
  period: string
  data: KLineData[]
} {
  if ('data' in response && typeof response.data === 'object' && 'klines' in response.data) {
    // 新格式：data.klines
    return {
      symbol: response.data.symbol,
      period: response.data.period,
      data: response.data.klines.map(adaptKLineData)
    }
  } else if ('data' in response && Array.isArray(response.data)) {
    // 旧格式：data直接是数组
    return {
      symbol: '',
      period: '',
      data: response.data.map(adaptKLineData)
    }
  }
  return {
    symbol: '',
    period: '',
    data: []
  }
}

/**
 * 适配搜索结果
 */
export function adaptSearchResponse(response: ApiResponse<any>): any[] {
  if (response.data.results) {
    // 新格式：data.results
    return response.data.results
  } else if (Array.isArray(response.data)) {
    // 旧格式：data直接是数组
    return response.data
  }
  return []
}

/**
 * 标准化API响应
 * 确保所有响应都符合 ApiResponse 格式
 */
export function normalizeApiResponse<T>(response: any): ApiResponse<T> {
  // 如果已经是标准格式
  if (response.success !== undefined && response.data !== undefined) {
    return {
      success: response.success,
      data: response.data,
      message: response.message,
      timestamp: response.timestamp || new Date().toISOString()
    }
  }

  // 如果是旧格式 BaseResponse
  if (response.code !== undefined && response.data !== undefined) {
    return {
      success: response.code === 200 || response.code === 0,
      data: response.data,
      message: response.message,
      timestamp: response.timestamp ? 
        (typeof response.timestamp === 'number' ? 
          new Date(response.timestamp).toISOString() : 
          response.timestamp) : 
        new Date().toISOString()
    }
  }

  // 默认格式
  return {
    success: true,
    data: response as T,
    timestamp: new Date().toISOString()
  }
}

/**
 * 错误响应适配器
 */
export function adaptErrorResponse(error: any): {
  success: false
  message: string
  timestamp: string
} {
  return {
    success: false,
    message: error?.message || error?.detail || '请求失败',
    timestamp: new Date().toISOString()
  }
}

/**
 * 数据类型验证器
 */
export const validators = {
  /**
   * 验证行情数据
   */
  isValidQuoteData(data: any): data is QuoteData {
    return data && 
           typeof data.symbol === 'string' &&
           typeof data.name === 'string' &&
           typeof data.currentPrice === 'number'
  },

  /**
   * 验证K线数据
   */
  isValidKLineData(data: any): data is KLineData {
    return data &&
           typeof data.timestamp === 'number' &&
           typeof data.open === 'number' &&
           typeof data.high === 'number' &&
           typeof data.low === 'number' &&
           typeof data.close === 'number' &&
           typeof data.volume === 'number'
  },

  /**
   * 验证API响应
   */
  isValidApiResponse(response: any): response is ApiResponse<any> {
    return response &&
           typeof response.success === 'boolean' &&
           response.data !== undefined &&
           typeof response.timestamp === 'string'
  }
}

/**
 * 数据清洗工具
 */
export const dataCleaner = {
  /**
   * 清理数值字段
   */
  cleanNumber(value: any, defaultValue = 0): number {
    if (typeof value === 'number' && !isNaN(value)) {
      return value
    }
    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return isNaN(parsed) ? defaultValue : parsed
    }
    return defaultValue
  },

  /**
   * 清理字符串字段
   */
  cleanString(value: any, defaultValue = ''): string {
    if (typeof value === 'string') {
      return value.trim()
    }
    return defaultValue
  },

  /**
   * 清理时间戳
   */
  cleanTimestamp(value: any): number {
    if (typeof value === 'number') {
      return value
    }
    if (typeof value === 'string') {
      const date = new Date(value)
      return isNaN(date.getTime()) ? Date.now() : date.getTime()
    }
    return Date.now()
  }
}
