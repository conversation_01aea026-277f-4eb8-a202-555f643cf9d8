#!/usr/bin/env python3
"""
MCP P1问题清理脚本
处理项目中的重要但不紧急的问题

P1问题清单:
1. 包管理器冲突问题
2. 服务层命名重叠和混乱
3. 配置文件分散问题
4. 空目录清理
"""

import os
import shutil
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime
import re

class MCPP1CleanupTool:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues_fixed = []
        self.errors = []
        self.warnings = []
        
    def log_fix(self, issue: str, action: str, status: str = "success"):
        """记录修复操作"""
        self.issues_fixed.append({
            "issue": issue,
            "action": action,
            "status": status,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[SUCCESS] {issue}: {action}")
        
    def log_error(self, issue: str, error: str):
        """记录错误"""
        self.errors.append({
            "issue": issue,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[ERROR] {issue}: {error}")
        
    def log_warning(self, issue: str, warning: str):
        """记录警告"""
        self.warnings.append({
            "issue": issue,
            "warning": warning,
            "timestamp": datetime.now().isoformat()
        })
        print(f"[WARNING] {issue}: {warning}")

    def analyze_service_naming_conflicts(self) -> Dict[str, List[str]]:
        """分析服务层命名冲突"""
        services_dir = self.project_root / "backend" / "app" / "services"
        if not services_dir.exists():
            return {}
            
        service_files = list(services_dir.glob("*.py"))
        
        # 按命名模式分组
        naming_groups = {
            "enhanced": [],
            "integrated": [],
            "real_data": [],
            "market_related": [],
            "trading_related": [],
            "service_impl": [],
            "duplicates": []
        }
        
        for file_path in service_files:
            name = file_path.stem
            
            if "enhanced" in name:
                naming_groups["enhanced"].append(name)
            elif "integrated" in name:
                naming_groups["integrated"].append(name)
            elif "real_data" in name:
                naming_groups["real_data"].append(name)
            elif "market" in name:
                naming_groups["market_related"].append(name)
            elif "trading" in name:
                naming_groups["trading_related"].append(name)
            elif name.endswith("_impl"):
                naming_groups["service_impl"].append(name)
                
        # 检测重复功能的服务
        potential_duplicates = []
        market_services = [f for f in service_files if "market" in f.stem and "service" in f.stem]
        if len(market_services) > 3:
            potential_duplicates.extend([f.stem for f in market_services])
            
        trading_services = [f for f in service_files if "trading" in f.stem and "service" in f.stem]
        if len(trading_services) > 2:
            potential_duplicates.extend([f.stem for f in trading_services])
            
        naming_groups["duplicates"] = potential_duplicates
        
        return naming_groups

    def create_service_naming_report(self):
        """创建服务命名分析报告"""
        naming_analysis = self.analyze_service_naming_conflicts()
        
        report_content = """# 服务层命名分析报告

## 当前命名模式分布

"""
        
        for category, files in naming_analysis.items():
            if files:
                report_content += f"### {category.replace('_', ' ').title()}\n"
                for file_name in files:
                    report_content += f"- {file_name}.py\n"
                report_content += "\n"
        
        report_content += """
## 建议的重构方案

### 1. 统一命名规范
- 核心服务: `{domain}_service.py`
- 适配器: `{source}_adapter.py`
- 增强版本: `{domain}_service_enhanced.py`
- 集成服务: `{domain}_facade.py`

### 2. 合并重复功能
- market_service.py (保留)
- market_data_service.py (合并到market_service.py)
- enhanced_market_service.py (重命名为market_service_enhanced.py)
- integrated_market_service.py (重命名为market_facade.py)

### 3. 清理实现类
- trading_service_impl.py -> 合并到trading_service.py
- 移除不必要的_impl后缀

### 4. 数据源适配器重命名
- real_data_source.py -> data_source_adapter.py
- real_data_sources.py -> multi_data_source_adapter.py
"""
        
        report_file = self.project_root / "reports" / "service_naming_analysis.md"
        report_file.parent.mkdir(exist_ok=True)
        
        try:
            report_file.write_text(report_content, encoding='utf-8')
            self.log_fix("P1-service-analysis", f"生成服务命名分析报告: {report_file}")
        except Exception as e:
            self.log_error("P1-service-analysis", f"生成报告失败: {e}")

    def clean_empty_directories(self):
        """清理空目录"""
        empty_dirs_to_check = [
            "backend/app/events",
            "backend/app/constants", 
            "backend/app/workers",
            "backend/app/websocket"
        ]
        
        cleaned_dirs = []
        for dir_path in empty_dirs_to_check:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                try:
                    # 检查是否为空目录（忽略.gitkeep等文件）
                    contents = list(full_path.iterdir())
                    python_files = [f for f in contents if f.suffix == '.py']
                    
                    if not python_files:  # 没有Python文件
                        # 创建.gitkeep以保持目录结构
                        gitkeep_file = full_path / ".gitkeep"
                        gitkeep_file.write_text("# 保持目录结构\n")
                        cleaned_dirs.append(dir_path)
                        self.log_fix("P1-empty-dirs", f"清理空目录并添加.gitkeep: {dir_path}")
                        
                except Exception as e:
                    self.log_error("P1-empty-dirs", f"处理目录失败 {dir_path}: {e}")
        
        if not cleaned_dirs:
            self.log_fix("P1-empty-dirs", "无需清理的空目录")

    def consolidate_docker_configs(self):
        """整合Docker配置文件"""
        # 扫描分散的Docker配置
        docker_files = []
        
        # 查找所有Docker相关文件
        for pattern in ["**/Dockerfile*", "**/docker-compose*.yml", "**/docker-compose*.yaml"]:
            docker_files.extend(self.project_root.glob(pattern))
        
        # 按类型分组
        config_groups = {
            "dockerfiles": [f for f in docker_files if "Dockerfile" in f.name],
            "compose_files": [f for f in docker_files if "docker-compose" in f.name],
            "nginx_configs": list(self.project_root.glob("**/nginx*.conf"))
        }
        
        # 创建整合报告
        consolidation_plan = """# Docker配置整合计划

## 当前配置分布

"""
        
        for category, files in config_groups.items():
            if files:
                consolidation_plan += f"### {category.replace('_', ' ').title()}\n"
                for config_file in files:
                    relative_path = config_file.relative_to(self.project_root)
                    consolidation_plan += f"- {relative_path}\n"
                consolidation_plan += "\n"
        
        consolidation_plan += """
## 建议的整合方案

### 目标结构
```
docker/
├── backend/
│   ├── Dockerfile
│   └── Dockerfile.prod
├── frontend/
│   ├── Dockerfile
│   └── Dockerfile.prod
├── nginx/
│   ├── nginx.conf
│   └── nginx.prod.conf
├── compose/
│   ├── docker-compose.dev.yml
│   ├── docker-compose.staging.yml
│   └── docker-compose.prod.yml
└── README.md
```

### 整合步骤
1. 移动所有Dockerfile到docker/目录
2. 合并重复的docker-compose文件
3. 统一nginx配置到docker/nginx/
4. 更新相关脚本中的路径引用
"""
        
        plan_file = self.project_root / "reports" / "docker_consolidation_plan.md"
        plan_file.parent.mkdir(exist_ok=True)
        
        try:
            plan_file.write_text(consolidation_plan, encoding='utf-8')
            self.log_fix("P1-docker-consolidation", f"生成Docker整合计划: {plan_file}")
        except Exception as e:
            self.log_error("P1-docker-consolidation", f"生成计划失败: {e}")

    def create_package_manager_guide(self):
        """创建包管理器使用指南"""
        guide_content = """# 包管理器使用指南

## 当前状态
项目统一使用 **pnpm** 作为前端包管理器

## 为什么选择pnpm？
1. **性能优势**: 安装速度比npm快2-3倍
2. **磁盘效率**: 通过硬链接节省磁盘空间
3. **严格依赖**: 避免幽灵依赖问题
4. **Monorepo支持**: 更好的工作空间支持

## 使用指南

### 安装pnpm
```bash
# 通过npm安装
npm install -g pnpm

# 或使用winget (Windows)
winget install pnpm

# 验证安装
pnpm --version
```

### 项目命令
```bash
# 安装依赖
cd frontend && pnpm install

# 开发模式
pnpm dev

# 构建项目
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint
```

### 禁用其他包管理器
项目已配置为仅使用pnpm：
- `.gitignore` 忽略 `package-lock.json` 和 `yarn.lock`
- `package.json` 中配置了engines限制

### 故障排除
如果遇到依赖问题：
```bash
# 清理缓存
pnpm store prune

# 重新安装
rm -rf node_modules
pnpm install

# 更新依赖
pnpm update
```
"""
        
        guide_file = self.project_root / "docs" / "package_manager_guide.md"
        guide_file.parent.mkdir(exist_ok=True)
        
        try:
            guide_file.write_text(guide_content, encoding='utf-8')
            self.log_fix("P1-package-guide", f"创建包管理器指南: {guide_file}")
        except Exception as e:
            self.log_error("P1-package-guide", f"创建指南失败: {e}")

    def create_project_structure_standard(self):
        """创建项目结构标准"""
        structure_doc = """# 项目结构标准

## 总体架构
```
quant014/                           # 项目根目录
├── backend/                        # 后端Python应用
│   ├── app/                       # FastAPI应用核心
│   │   ├── api/v1/               # API路由层
│   │   ├── core/                 # 核心基础设施
│   │   ├── services/             # 业务服务层
│   │   ├── models/               # 数据模型
│   │   ├── schemas/              # 数据验证模式
│   │   └── utils/                # 工具函数
│   ├── migrations/               # 数据库迁移
│   ├── tests/                    # 后端测试
│   └── requirements.txt          # Python依赖
├── frontend/                       # 前端Vue应用
│   ├── src/                      # 源码目录
│   │   ├── api/                  # API接口层
│   │   ├── components/           # 可复用组件
│   │   ├── views/                # 页面视图
│   │   ├── stores/               # 状态管理
│   │   ├── router/               # 路由配置
│   │   └── utils/                # 工具函数
│   ├── public/                   # 静态资源
│   └── package.json              # 前端依赖
├── docker/                         # 容器化配置
│   ├── backend/                  # 后端Docker文件
│   ├── frontend/                 # 前端Docker文件
│   ├── nginx/                    # Nginx配置
│   └── compose/                  # Docker Compose文件
├── docs/                          # 项目文档
├── scripts/                       # 自动化脚本
├── monitoring/                    # 监控配置
└── reports/                       # 分析报告
```

## 命名规范

### 文件命名
- **Python文件**: snake_case (例：user_service.py)
- **Vue组件**: PascalCase (例：UserProfile.vue)
- **普通文件**: kebab-case (例：docker-compose.yml)

### 目录命名
- **英文小写**: 使用英文小写字母
- **下划线分隔**: snake_case (例：user_management)
- **功能导向**: 按功能而非技术分组

### 服务层命名规范
- **核心服务**: `{domain}_service.py`
- **数据适配器**: `{source}_adapter.py`
- **增强服务**: `{domain}_service_enhanced.py`
- **门面服务**: `{domain}_facade.py`

## 分层原则

### 后端分层
1. **API层** (api/): 处理HTTP请求和响应
2. **服务层** (services/): 业务逻辑实现
3. **数据层** (models/): 数据模型和数据库操作
4. **工具层** (utils/): 通用工具和帮助函数

### 前端分层
1. **视图层** (views/): 页面组件
2. **组件层** (components/): 可复用组件
3. **服务层** (api/, stores/): 数据获取和状态管理
4. **工具层** (utils/): 通用工具函数

## 依赖管理原则
- **后端**: 使用requirements.txt，Python 3.10.13
- **前端**: 使用pnpm，Node.js >= 18.0.0
- **容器**: 使用Docker，统一运行环境
- **监控**: 使用Prometheus + Grafana
"""
        
        standard_file = self.project_root / "docs" / "project_structure_standard.md"
        standard_file.parent.mkdir(exist_ok=True)
        
        try:
            standard_file.write_text(structure_doc, encoding='utf-8')
            self.log_fix("P1-structure-standard", f"创建项目结构标准: {standard_file}")
        except Exception as e:
            self.log_error("P1-structure-standard", f"创建标准失败: {e}")

    def run_all_fixes(self):
        """执行所有P1修复"""
        print("开始MCP P1问题修复...")
        print("=" * 50)
        
        # 执行所有修复操作
        self.create_service_naming_report()
        self.clean_empty_directories()
        self.consolidate_docker_configs()
        self.create_package_manager_guide()
        self.create_project_structure_standard()
        
        # 生成修复报告
        self.generate_report()

    def generate_report(self):
        """生成修复报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_fixes": len(self.issues_fixed),
            "total_errors": len(self.errors),
            "total_warnings": len(self.warnings),
            "fixes": self.issues_fixed,
            "errors": self.errors,
            "warnings": self.warnings
        }
        
        # 保存JSON报告
        report_file = self.project_root / "reports" / "mcp_p1_cleanup_report.json"
        report_file.parent.mkdir(exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存报告失败: {e}")
        
        # 打印摘要
        print("\n" + "=" * 50)
        print("MCP P1修复摘要")
        print("=" * 50)
        print(f"成功修复: {len(self.issues_fixed)}个问题")
        print(f"修复失败: {len(self.errors)}个问题")
        print(f"警告信息: {len(self.warnings)}个")
        
        if self.issues_fixed:
            print("\n修复的问题:")
            for fix in self.issues_fixed:
                print(f"  - {fix['issue']}: {fix['action']}")
        
        if self.errors:
            print("\n需要手动处理的问题:")
            for error in self.errors:
                print(f"  - {error['issue']}: {error['error']}")
                
        if self.warnings:
            print("\n警告信息:")
            for warning in self.warnings:
                print(f"  - {warning['issue']}: {warning['warning']}")
        
        print(f"\n详细报告: {report_file}")

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    cleaner = MCPP1CleanupTool(project_root)
    cleaner.run_all_fixes()

if __name__ == "__main__":
    main()