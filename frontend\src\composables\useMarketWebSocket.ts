/**
 * 市场WebSocket连接状态管理
 * 提供连接状态监控和手动重连功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { marketWebSocketService } from '@/services/market-websocket.service'
import type { QuoteData } from '@/types/market'

export function useMarketWebSocket() {
  // 连接状态
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(10)
  const lastError = ref<string | null>(null)
  const lastSuccessfulConnection = ref(0)
  const subscriptions = ref<string[]>([])

  // 实时数据
  const latestQuotes = ref<Map<string, QuoteData>>(new Map())
  const connectionStats = ref({
    totalMessages: 0,
    lastMessageTime: 0,
    averageLatency: 0
  })

  // 计算属性
  const connectionStatus = computed(() => {
    if (isConnecting.value) return 'connecting'
    if (isConnected.value) return 'connected'
    if (reconnectAttempts.value >= maxReconnectAttempts.value) return 'failed'
    return 'disconnected'
  })

  const canManualReconnect = computed(() => {
    return !isConnecting.value && (!isConnected.value || reconnectAttempts.value >= maxReconnectAttempts.value)
  })

  const connectionStatusText = computed(() => {
    switch (connectionStatus.value) {
      case 'connecting':
        return `连接中... (${reconnectAttempts.value}/${maxReconnectAttempts.value})`
      case 'connected':
        return '已连接'
      case 'failed':
        return '连接失败'
      case 'disconnected':
        return '已断开'
      default:
        return '未知状态'
    }
  })

  const connectionStatusColor = computed(() => {
    switch (connectionStatus.value) {
      case 'connecting':
        return '#409EFF'  // 蓝色
      case 'connected':
        return '#67C23A'  // 绿色
      case 'failed':
        return '#F56C6C'  // 红色
      case 'disconnected':
        return '#909399'  // 灰色
      default:
        return '#909399'
    }
  })

  // 事件处理器
  const handleConnected = () => {
    isConnected.value = true
    isConnecting.value = false
    reconnectAttempts.value = 0
    lastError.value = null
    lastSuccessfulConnection.value = Date.now()
    updateConnectionInfo()
  }

  const handleDisconnected = () => {
    isConnected.value = false
    isConnecting.value = false
    updateConnectionInfo()
  }

  const handleError = (error: Event) => {
    lastError.value = error.toString()
    isConnecting.value = false
    console.error('Market WebSocket error:', error)
  }

  const handleQuote = (quote: QuoteData) => {
    latestQuotes.value.set(quote.symbol, quote)
    connectionStats.value.totalMessages++
    connectionStats.value.lastMessageTime = Date.now()
  }

  const handleQuotes = (quotes: QuoteData[]) => {
    quotes.forEach(quote => {
      latestQuotes.value.set(quote.symbol, quote)
    })
    connectionStats.value.totalMessages += quotes.length
    connectionStats.value.lastMessageTime = Date.now()
  }

  // 更新连接信息
  const updateConnectionInfo = () => {
    const info = marketWebSocketService.getConnectionInfo()
    reconnectAttempts.value = info.reconnectAttempts
    maxReconnectAttempts.value = info.maxReconnectAttempts
    subscriptions.value = info.subscriptions
    lastSuccessfulConnection.value = info.lastSuccessfulConnection
  }

  // 手动重连
  const manualReconnect = async () => {
    try {
      isConnecting.value = true
      await marketWebSocketService.reconnect()
      ElMessage.success('正在重新连接...')
    } catch (error) {
      console.error('Manual reconnect failed:', error)
      ElMessage.error('重连失败，请稍后再试')
      isConnecting.value = false
    }
  }

  // 订阅股票
  const subscribe = (symbol: string) => {
    marketWebSocketService.subscribe(symbol)
    updateConnectionInfo()
  }

  // 取消订阅
  const unsubscribe = (symbol: string) => {
    marketWebSocketService.unsubscribe(symbol)
    latestQuotes.value.delete(symbol)
    updateConnectionInfo()
  }

  // 批量订阅
  const subscribeMultiple = (symbols: string[]) => {
    marketWebSocketService.subscribeMultiple(symbols)
    updateConnectionInfo()
  }

  // 获取最新报价
  const getLatestQuote = (symbol: string): QuoteData | null => {
    return latestQuotes.value.get(symbol) || null
  }

  // 获取所有最新报价
  const getAllLatestQuotes = (): QuoteData[] => {
    return Array.from(latestQuotes.value.values())
  }

  // 显示连接状态通知
  const showConnectionNotification = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts.value) {
      ElNotification({
        title: '连接失败',
        message: '市场数据连接失败，点击重连按钮手动重试',
        type: 'error',
        duration: 0,
        position: 'bottom-right'
      })
    }
  }

  // 生命周期
  onMounted(() => {
    // 注册事件监听器
    marketWebSocketService.on('connected', handleConnected)
    marketWebSocketService.on('disconnected', handleDisconnected)
    marketWebSocketService.on('error', handleError)
    marketWebSocketService.on('quote', handleQuote)
    marketWebSocketService.on('quotes', handleQuotes)

    // 初始化连接状态
    updateConnectionInfo()
    isConnected.value = marketWebSocketService.isConnected()

    // 监控连接失败
    const checkConnectionFailure = setInterval(() => {
      updateConnectionInfo()
      if (reconnectAttempts.value >= maxReconnectAttempts.value && !isConnected.value) {
        showConnectionNotification()
        clearInterval(checkConnectionFailure)
      }
    }, 5000)

    // 清理定时器
    onUnmounted(() => {
      clearInterval(checkConnectionFailure)
    })
  })

  onUnmounted(() => {
    // 移除事件监听器
    marketWebSocketService.off('connected', handleConnected)
    marketWebSocketService.off('disconnected', handleDisconnected)
    marketWebSocketService.off('error', handleError)
    marketWebSocketService.off('quote', handleQuote)
    marketWebSocketService.off('quotes', handleQuotes)
  })

  return {
    // 状态
    isConnected,
    isConnecting,
    reconnectAttempts,
    maxReconnectAttempts,
    lastError,
    lastSuccessfulConnection,
    subscriptions,
    latestQuotes,
    connectionStats,

    // 计算属性
    connectionStatus,
    connectionStatusText,
    connectionStatusColor,
    canManualReconnect,

    // 方法
    manualReconnect,
    subscribe,
    unsubscribe,
    subscribeMultiple,
    getLatestQuote,
    getAllLatestQuotes,
    updateConnectionInfo
  }
}
