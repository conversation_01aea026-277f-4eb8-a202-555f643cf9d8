"""
增强版CTP交易服务
集成SimNow仿真和真实交易环境
"""

import asyncio
import logging
import time
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Callable
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class OrderStatus(str, Enum):
    """订单状态"""
    PENDING = "pending"           # 待报
    SUBMITTED = "submitted"       # 已报
    PARTIAL_FILLED = "partial_filled"  # 部分成交
    FILLED = "filled"            # 全部成交
    CANCELLED = "cancelled"      # 已撤销
    REJECTED = "rejected"        # 已拒绝
    ERROR = "error"              # 错误


class OrderDirection(str, Enum):
    """订单方向"""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(str, Enum):
    """订单类型"""
    LIMIT = "LIMIT"      # 限价单
    MARKET = "MARKET"    # 市价单
    STOP = "STOP"        # 止损单
    STOP_LIMIT = "STOP_LIMIT"  # 止损限价单


class OrderRequest(BaseModel):
    """订单请求"""
    symbol: str
    direction: OrderDirection
    order_type: OrderType
    volume: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancel


class OrderResponse(BaseModel):
    """订单响应"""
    success: bool
    message: str
    order_id: Optional[str] = None
    data: Optional[Dict] = None


class TradeData(BaseModel):
    """成交数据"""
    trade_id: str
    order_id: str
    symbol: str
    direction: OrderDirection
    volume: int
    price: float
    trade_time: datetime
    commission: float = 0.0


class PositionData(BaseModel):
    """持仓数据"""
    symbol: str
    direction: OrderDirection
    volume: int
    avg_price: float
    market_value: float
    pnl: float
    pnl_ratio: float


class AccountData(BaseModel):
    """账户数据"""
    account_id: str
    balance: float
    available: float
    margin: float
    commission: float
    pnl: float
    total_asset: float


class EnhancedCTPService:
    """增强版CTP交易服务"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._get_default_config()
        self.is_connected = False
        self.is_logged_in = False
        
        # 数据存储
        self.orders: Dict[str, Dict] = {}
        self.trades: Dict[str, TradeData] = {}
        self.positions: Dict[str, PositionData] = {}
        self.account: Optional[AccountData] = None
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            "on_order_update": [],
            "on_trade": [],
            "on_position_update": [],
            "on_account_update": [],
            "on_tick": [],
            "on_error": []
        }
        
        # 订单计数器
        self.order_counter = 0
        
        logger.info("Enhanced CTP Service initialized")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "broker_id": "9999",
            "user_id": "",
            "password": "",
            "auth_code": "****************",
            "app_id": "simnow_client_test",
            "trade_front": "tcp://180.168.146.187:10130",
            "md_front": "tcp://180.168.146.187:10131",
            "simulation_mode": True,
            "initial_balance": 1000000.0,
            "commission_rate": 0.0003
        }
    
    async def initialize(self) -> bool:
        """初始化CTP连接"""
        try:
            logger.info("Initializing CTP connection...")
            
            # 模拟连接过程
            await asyncio.sleep(1)
            
            # 初始化账户数据
            self.account = AccountData(
                account_id=self.config["user_id"],
                balance=self.config["initial_balance"],
                available=self.config["initial_balance"],
                margin=0.0,
                commission=0.0,
                pnl=0.0,
                total_asset=self.config["initial_balance"]
            )
            
            self.is_connected = True
            self.is_logged_in = True
            
            logger.info("CTP connection initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize CTP connection: {e}")
            return False
    
    async def submit_order(self, order_request: OrderRequest, user_id: int) -> OrderResponse:
        """提交订单"""
        try:
            if not self.is_connected:
                return OrderResponse(
                    success=False,
                    message="CTP未连接"
                )
            
            # 生成订单ID
            self.order_counter += 1
            order_id = f"ORD{int(time.time())}{self.order_counter:04d}"
            
            # 风险检查
            risk_check = await self._check_order_risk(order_request)
            if not risk_check["passed"]:
                return OrderResponse(
                    success=False,
                    message=f"风险检查失败: {risk_check['message']}"
                )
            
            # 创建订单数据
            order_data = {
                "order_id": order_id,
                "user_id": user_id,
                "symbol": order_request.symbol,
                "direction": order_request.direction,
                "order_type": order_request.order_type,
                "volume": order_request.volume,
                "price": order_request.price,
                "status": OrderStatus.SUBMITTED,
                "filled_volume": 0,
                "avg_price": 0.0,
                "create_time": datetime.now(),
                "update_time": datetime.now()
            }
            
            # 保存订单
            self.orders[order_id] = order_data
            
            # 模拟订单处理
            asyncio.create_task(self._simulate_order_execution(order_id))
            
            # 触发订单更新回调
            await self._trigger_callback("on_order_update", order_data)
            
            logger.info(f"Order submitted successfully: {order_id}")
            
            return OrderResponse(
                success=True,
                message="订单提交成功",
                order_id=order_id,
                data=order_data
            )
            
        except Exception as e:
            logger.error(f"Failed to submit order: {e}")
            return OrderResponse(
                success=False,
                message=f"订单提交失败: {str(e)}"
            )
    
    async def cancel_order(self, order_id: str) -> OrderResponse:
        """撤销订单"""
        try:
            if order_id not in self.orders:
                return OrderResponse(
                    success=False,
                    message="订单不存在"
                )
            
            order = self.orders[order_id]
            
            if order["status"] in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                return OrderResponse(
                    success=False,
                    message="订单已完成或已撤销"
                )
            
            # 更新订单状态
            order["status"] = OrderStatus.CANCELLED
            order["update_time"] = datetime.now()
            
            # 触发订单更新回调
            await self._trigger_callback("on_order_update", order)
            
            logger.info(f"Order cancelled: {order_id}")
            
            return OrderResponse(
                success=True,
                message="订单撤销成功",
                order_id=order_id
            )
            
        except Exception as e:
            logger.error(f"Failed to cancel order: {e}")
            return OrderResponse(
                success=False,
                message=f"撤单失败: {str(e)}"
            )
    
    async def query_orders(self, user_id: int) -> List[Dict]:
        """查询订单"""
        try:
            user_orders = [
                order for order in self.orders.values()
                if order["user_id"] == user_id
            ]
            return user_orders
        except Exception as e:
            logger.error(f"Failed to query orders: {e}")
            return []
    
    async def query_positions(self, user_id: int) -> List[PositionData]:
        """查询持仓"""
        try:
            user_positions = [
                position for position in self.positions.values()
            ]
            return user_positions
        except Exception as e:
            logger.error(f"Failed to query positions: {e}")
            return []
    
    async def query_account(self, user_id: int) -> Optional[AccountData]:
        """查询账户"""
        try:
            return self.account
        except Exception as e:
            logger.error(f"Failed to query account: {e}")
            return None
    
    async def _check_order_risk(self, order_request: OrderRequest) -> Dict:
        """检查订单风险"""
        try:
            # 导入风险控制服务
            from app.services.risk_control_service import get_risk_service

            # 基础风险检查
            if not self.account:
                return {"passed": False, "message": "账户信息不可用"}

            # 准备订单数据
            order_data = {
                "symbol": order_request.symbol,
                "direction": order_request.direction.value,
                "order_type": order_request.order_type.value,
                "volume": order_request.volume,
                "price": order_request.price or 3500.0,  # 市价单使用估算价格
            }

            # 准备账户数据
            account_data = {
                "available": self.account.available,
                "balance": self.account.balance,
                "total_asset": self.account.total_asset,
                "margin": self.account.margin,
                "pnl": self.account.pnl
            }

            # 执行风险检查
            risk_service = get_risk_service()
            risk_result = await risk_service.check_order_risk(order_data, account_data)

            return {
                "passed": risk_result.passed,
                "message": risk_result.message,
                "risk_level": risk_result.risk_level.value,
                "risk_type": risk_result.risk_type.value,
                "details": risk_result.details,
                "suggestions": risk_result.suggestions
            }

        except Exception as e:
            logger.error(f"Risk check error: {e}")
            return {"passed": False, "message": f"风险检查异常: {str(e)}"}
    
    async def _simulate_order_execution(self, order_id: str):
        """模拟订单执行"""
        try:
            await asyncio.sleep(1)  # 模拟处理延迟
            
            order = self.orders.get(order_id)
            if not order or order["status"] != OrderStatus.SUBMITTED:
                return
            
            # 模拟成交
            trade_id = f"TRD{int(time.time())}{order_id[-4:]}"
            trade_price = order["price"] or 3500.0  # 简化价格
            
            # 创建成交记录
            trade_data = TradeData(
                trade_id=trade_id,
                order_id=order_id,
                symbol=order["symbol"],
                direction=order["direction"],
                volume=order["volume"],
                price=trade_price,
                trade_time=datetime.now(),
                commission=trade_price * order["volume"] * self.config["commission_rate"]
            )
            
            # 保存成交记录
            self.trades[trade_id] = trade_data
            
            # 更新订单状态
            order["status"] = OrderStatus.FILLED
            order["filled_volume"] = order["volume"]
            order["avg_price"] = trade_price
            order["update_time"] = datetime.now()
            
            # 更新持仓
            await self._update_position(trade_data)
            
            # 更新账户
            await self._update_account(trade_data)
            
            # 触发回调
            await self._trigger_callback("on_trade", trade_data.dict())
            await self._trigger_callback("on_order_update", order)
            
            logger.info(f"Order executed: {order_id}, Trade: {trade_id}")
            
        except Exception as e:
            logger.error(f"Order execution simulation error: {e}")
    
    async def _update_position(self, trade_data: TradeData):
        """更新持仓"""
        try:
            position_key = f"{trade_data.symbol}_{trade_data.direction}"
            
            if position_key in self.positions:
                # 更新现有持仓
                position = self.positions[position_key]
                total_volume = position.volume + trade_data.volume
                total_cost = position.avg_price * position.volume + trade_data.price * trade_data.volume
                position.avg_price = total_cost / total_volume
                position.volume = total_volume
                position.market_value = position.volume * trade_data.price
            else:
                # 创建新持仓
                position = PositionData(
                    symbol=trade_data.symbol,
                    direction=trade_data.direction,
                    volume=trade_data.volume,
                    avg_price=trade_data.price,
                    market_value=trade_data.volume * trade_data.price,
                    pnl=0.0,
                    pnl_ratio=0.0
                )
                self.positions[position_key] = position
            
            # 触发持仓更新回调
            await self._trigger_callback("on_position_update", position.dict())
            
        except Exception as e:
            logger.error(f"Position update error: {e}")
    
    async def _update_account(self, trade_data: TradeData):
        """更新账户"""
        try:
            if not self.account:
                return
            
            trade_amount = trade_data.price * trade_data.volume
            commission = trade_data.commission
            
            if trade_data.direction == OrderDirection.BUY:
                # 买入：减少可用资金
                self.account.available -= (trade_amount + commission)
                self.account.commission += commission
            else:
                # 卖出：增加可用资金
                self.account.available += (trade_amount - commission)
                self.account.commission += commission
            
            # 触发账户更新回调
            await self._trigger_callback("on_account_update", self.account.dict())
            
        except Exception as e:
            logger.error(f"Account update error: {e}")
    
    async def _trigger_callback(self, event_type: str, data: Dict):
        """触发回调函数"""
        try:
            callbacks = self.callbacks.get(event_type, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"Callback error for {event_type}: {e}")
        except Exception as e:
            logger.error(f"Trigger callback error: {e}")
    
    def register_callback(self, event_type: str, callback: Callable):
        """注册回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
        else:
            logger.warning(f"Unknown event type: {event_type}")
    
    async def disconnect(self):
        """断开连接"""
        try:
            self.is_connected = False
            self.is_logged_in = False
            logger.info("CTP connection disconnected")
        except Exception as e:
            logger.error(f"Disconnect error: {e}")


# 全局CTP服务实例
_ctp_service: Optional[EnhancedCTPService] = None


def get_ctp_service() -> EnhancedCTPService:
    """获取CTP服务实例"""
    global _ctp_service
    if _ctp_service is None:
        _ctp_service = EnhancedCTPService()
    return _ctp_service


async def initialize_ctp_service(config: Optional[Dict] = None) -> bool:
    """初始化CTP服务"""
    service = get_ctp_service()
    if config:
        service.config.update(config)
    return await service.initialize()
