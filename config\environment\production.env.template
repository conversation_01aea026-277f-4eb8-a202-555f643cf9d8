# 生产环境配置文件
# 复制为 .env.production 并根据需要修改

# === 数据库配置 ===
DATABASE_URL=postgresql+asyncpg://username:password@db_host:5432/quant_prod
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# === Redis配置 ===
REDIS_URL=redis://redis_host:6379/0
REDIS_PASSWORD=your-redis-password
REDIS_POOL_SIZE=10

# === API配置 ===
API_V1_PREFIX=/api/v1
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false

# === 安全配置 ===
SECRET_KEY=your-production-secret-key-must-be-secure
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256

# === 外部数据源 ===
TUSHARE_TOKEN=your-production-tushare-token
AKSHARE_ENABLED=true

# === 日志配置 ===
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/quant/app.log

# === 前端配置 ===
FRONTEND_URL=https://your-domain.com
CORS_ORIGINS=["https://your-domain.com"]

# === 监控配置 ===
ENABLE_MONITORING=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_URL=http://grafana:3000

# === 性能配置 ===
WORKER_PROCESSES=4
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65
