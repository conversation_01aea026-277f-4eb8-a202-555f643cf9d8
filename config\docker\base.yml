# Docker Compose 基础配置
# 定义所有环境通用的基础服务配置

version: '3.8'

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

x-restart-policy: &default-restart
  restart: unless-stopped

networks:
  quant-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  app_logs:
    driver: local