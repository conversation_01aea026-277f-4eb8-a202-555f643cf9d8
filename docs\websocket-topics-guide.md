# WebSocket主题使用指南

## 🎯 主题命名规范

### 1. 全量市场数据
- **主题**: `market:all`
- **用途**: 接收市场整体数据，包括主要指数、热门股票等
- **推送频率**: 每秒1次
- **数据格式**:
```json
{
  "type": "market_overview",
  "topic": "market:all", 
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "indices": [...],
    "stocks": [...],
    "sectors": [...]
  }
}
```

### 2. 单股票实时数据
- **主题**: `market:{symbol}`
- **用途**: 接收特定股票的实时tick数据
- **推送频率**: 有变化时推送
- **数据格式**:
```json
{
  "type": "tick",
  "topic": "market:000001",
  "symbol": "000001",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "currentPrice": 12.34,
    "change": 0.12,
    "changePercent": 0.98,
    "volume": 1234567
  }
}
```

### 3. K线数据推送
- **主题**: `market:{symbol}:kline:{interval}`
- **用途**: 接收K线数据更新
- **推送频率**: K线周期结束时推送
- **数据格式**:
```json
{
  "type": "kline",
  "topic": "market:000001:kline:1d",
  "symbol": "000001",
  "interval": "1d",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "open": 12.00,
    "high": 12.50,
    "low": 11.80,
    "close": 12.34,
    "volume": 1234567
  }
}
```

## 🔌 前端订阅示例

### Vue组合式API使用
```typescript
import { useWebSocket } from '@/composables/useWebSocket'
import { WS_TOPICS, createSubscribeMessage } from '@/api/marketApiAdapter'

export default {
  setup() {
    const { connect, subscribe, unsubscribe, onMessage } = useWebSocket()
    
    // 连接WebSocket
    connect('ws://localhost:8000/api/v1/ws')
    
    // 订阅全量市场数据
    subscribe([WS_TOPICS.MARKET_ALL])
    
    // 订阅特定股票
    subscribe([WS_TOPICS.MARKET_SYMBOL('000001')])
    
    // 监听消息
    onMessage((data) => {
      if (data.type === 'tick') {
        // 处理实时行情
        updateStockPrice(data.symbol, data.data)
      } else if (data.type === 'market_overview') {
        // 处理市场概览
        updateMarketOverview(data.data)
      }
    })
    
    return { ... }
  }
}
```

### 在Pinia Store中使用
```typescript
// stores/market.ts
import { defineStore } from 'pinia'
import { useWebSocket } from '@/composables/useWebSocket'
import { WS_TOPICS } from '@/api/marketApiAdapter'

export const useMarketStore = defineStore('market', () => {
  const { subscribe, onMessage } = useWebSocket()
  
  // 初始化WebSocket订阅
  const initWebSocket = () => {
    // 订阅全量数据
    subscribe([WS_TOPICS.MARKET_ALL])
    
    // 监听消息并更新store
    onMessage((data) => {
      if (data.topic === WS_TOPICS.MARKET_ALL) {
        updateMarketData(data.data)
      }
    })
  }
  
  return { initWebSocket, ... }
})
```

## 🔧 后端推送实现

### 推送全量市场数据
```python
# 在后端服务中
await enhanced_manager.broadcast_to_topic("market:all", {
    "type": "market_overview",
    "timestamp": datetime.now().isoformat(),
    "data": market_data
})
```

### 推送单股票数据
```python
await enhanced_manager.broadcast_to_topic(f"market:{symbol}", {
    "type": "tick", 
    "symbol": symbol,
    "timestamp": datetime.now().isoformat(),
    "data": stock_data
})
```

## 📊 性能优化建议

1. **订阅管理**: 只订阅需要的主题，及时取消不需要的订阅
2. **消息过滤**: 在前端对消息进行适当的过滤和去重
3. **批量更新**: 将多个小更新合并为批量更新
4. **内存管理**: 定期清理过期的历史数据

## 🚨 错误处理

### 连接断开重连
```typescript
const { connect, isConnected } = useWebSocket()

// 监听连接状态
watch(isConnected, (connected) => {
  if (!connected) {
    // 重新连接并重新订阅
    setTimeout(() => {
      connect('ws://localhost:8000/api/v1/ws')
      // 重新订阅之前的主题
    }, 3000)
  }
})
```

### 消息处理错误
```typescript
onMessage((data) => {
  try {
    // 处理消息
    handleMessage(data)
  } catch (error) {
    console.error('处理WebSocket消息失败:', error)
    // 记录错误但不中断其他消息处理
  }
})
```
