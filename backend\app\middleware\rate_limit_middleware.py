"""
限流中间件
为市场数据API提供可配置的限流功能
"""

import time
import asyncio
from typing import Dict, Op<PERSON>, <PERSON><PERSON>
from collections import defaultdict, deque
from fastapi import HTTPException, status, Request
from fastapi.responses import JSONResponse
import logging

from app.core.config import get_settings
from app.core.optional_auth import AuthConfig

logger = logging.getLogger(__name__)
settings = get_settings()


class RateLimiter:
    """基于滑动窗口的限流器"""
    
    def __init__(self, max_requests: int, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, deque] = defaultdict(deque)
        self._lock = asyncio.Lock()
    
    async def is_allowed(self, key: str) -> Tuple[bool, Dict[str, int]]:
        """
        检查是否允许请求
        
        Args:
            key: 限流键（通常是用户ID或IP地址）
            
        Returns:
            (是否允许, 限流信息)
        """
        async with self._lock:
            now = time.time()
            window_start = now - self.window_seconds
            
            # 清理过期的请求记录
            request_times = self.requests[key]
            while request_times and request_times[0] < window_start:
                request_times.popleft()
            
            # 检查是否超过限制
            current_requests = len(request_times)
            allowed = current_requests < self.max_requests
            
            if allowed:
                request_times.append(now)
            
            # 计算重置时间
            reset_time = int(window_start + self.window_seconds) if request_times else int(now)
            remaining = max(0, self.max_requests - current_requests - (1 if allowed else 0))
            
            return allowed, {
                "limit": self.max_requests,
                "remaining": remaining,
                "reset": reset_time,
                "window": self.window_seconds
            }


class MarketRateLimitMiddleware:
    """市场数据API限流中间件"""
    
    def __init__(self):
        self.enabled = AuthConfig.is_rate_limit_enabled()
        self.rate_limits = AuthConfig.get_rate_limits()
        
        # 创建不同类型的限流器
        self.limiters = {
            "realtime": RateLimiter(self.rate_limits["realtime"], 60),
            "historical": RateLimiter(self.rate_limits["historical"], 60),
            "admin": RateLimiter(self.rate_limits["admin"], 60)
        }
        
        logger.info(f"Rate limiting {'enabled' if self.enabled else 'disabled'}")
        if self.enabled:
            logger.info(f"Rate limits: {self.rate_limits}")
    
    def get_rate_limit_type(self, path: str) -> Optional[str]:
        """根据路径确定限流类型"""
        if not self.enabled:
            return None
            
        # 实时行情端点
        if any(pattern in path for pattern in [
            "/quotes/realtime",
            "/quotes/",
            "/market/overview",
            "/market/depth"
        ]):
            return "realtime"
        
        # 历史数据端点
        elif any(pattern in path for pattern in [
            "/historical/",
            "/kline/",
            "/search"
        ]):
            return "historical"
        
        # 管理端点
        elif any(pattern in path for pattern in [
            "/cache/clear",
            "/cache/warmup",
            "/rebuild"
        ]):
            return "admin"
        
        return None
    
    def get_rate_limit_key(self, request: Request, user_id: Optional[str] = None) -> str:
        """生成限流键"""
        if user_id:
            return f"user:{user_id}"
        else:
            # 使用IP地址作为匿名用户的限流键
            client_ip = request.client.host if request.client else "unknown"
            return f"ip:{client_ip}"
    
    async def check_rate_limit(
        self, 
        request: Request, 
        user_id: Optional[str] = None
    ) -> Optional[JSONResponse]:
        """
        检查限流
        
        Returns:
            如果被限流，返回429响应；否则返回None
        """
        if not self.enabled:
            return None
        
        rate_limit_type = self.get_rate_limit_type(request.url.path)
        if not rate_limit_type:
            return None
        
        limiter = self.limiters[rate_limit_type]
        rate_limit_key = self.get_rate_limit_key(request, user_id)
        
        allowed, info = await limiter.is_allowed(rate_limit_key)
        
        # 添加限流头部信息
        headers = {
            "X-RateLimit-Limit": str(info["limit"]),
            "X-RateLimit-Remaining": str(info["remaining"]),
            "X-RateLimit-Reset": str(info["reset"]),
            "X-RateLimit-Window": str(info["window"])
        }
        
        if not allowed:
            logger.warning(
                f"Rate limit exceeded for {rate_limit_key} on {request.url.path}",
                extra={
                    "rate_limit_key": rate_limit_key,
                    "path": request.url.path,
                    "rate_limit_type": rate_limit_type,
                    "limit_info": info
                }
            )
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {info['limit']} per {info['window']} seconds",
                    "type": rate_limit_type,
                    "retry_after": info["reset"] - int(time.time())
                },
                headers=headers
            )
        
        # 请求被允许，记录限流信息到响应头
        request.state.rate_limit_headers = headers
        return None
    
    def add_rate_limit_headers(self, response, request: Request):
        """添加限流头部到响应"""
        if hasattr(request.state, "rate_limit_headers"):
            for key, value in request.state.rate_limit_headers.items():
                response.headers[key] = value


# 全局限流中间件实例
market_rate_limiter = MarketRateLimitMiddleware()
