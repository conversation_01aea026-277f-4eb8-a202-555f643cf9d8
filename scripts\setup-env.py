#!/usr/bin/env python3
"""
环境配置设置脚本
帮助用户快速设置开发或生产环境
"""

import os
import shutil
import sys
from pathlib import Path

def setup_environment(env_type: str = "development"):
    """设置环境配置"""
    
    project_root = Path(__file__).parent.parent
    
    # 环境配置文件映射
    env_files = {
        "development": ".env.development",
        "production": ".env.production",
        "template": ".env.template"
    }
    
    if env_type not in env_files:
        print(f"❌ 不支持的环境类型: {env_type}")
        print(f"支持的环境类型: {', '.join(env_files.keys())}")
        return False
    
    source_file = project_root / env_files[env_type]
    target_file = project_root / ".env"
    
    if not source_file.exists():
        print(f"❌ 源配置文件不存在: {source_file}")
        return False
    
    # 备份现有的.env文件
    if target_file.exists():
        backup_file = project_root / f".env.backup.{env_type}"
        shutil.copy2(target_file, backup_file)
        print(f"📦 已备份现有配置到: {backup_file}")
    
    # 复制配置文件
    shutil.copy2(source_file, target_file)
    print(f"✅ 已设置 {env_type} 环境配置")
    
    # 提示用户需要修改的配置
    if env_type == "production":
        print("\n⚠️  生产环境配置提醒:")
        print("   - 请修改 SECRET_KEY 和 ENCRYPTION_MASTER_KEY")
        print("   - 请设置正确的 TUSHARE_TOKEN")
        print("   - 请配置数据库连接信息")
        print("   - 请设置正确的域名和CORS配置")
    elif env_type == "development":
        print("\n💡 开发环境配置提醒:")
        print("   - 如需使用真实数据，请设置 TUSHARE_TOKEN")
        print("   - 默认使用SQLite数据库，无需额外配置")
        print("   - 默认使用Mock数据，可正常开发测试")
    
    return True

def check_environment():
    """检查环境配置"""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    
    if not env_file.exists():
        print("❌ 未找到 .env 配置文件")
        print("请运行: python scripts/setup-env.py development")
        return False
    
    print("✅ 找到环境配置文件")
    
    # 检查关键配置
    required_vars = [
        "PROJECT_NAME",
        "SECRET_KEY", 
        "DATABASE_URL",
        "DEBUG"
    ]
    
    missing_vars = []
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少必要配置: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python scripts/setup-env.py development  # 设置开发环境")
        print("  python scripts/setup-env.py production   # 设置生产环境")
        print("  python scripts/setup-env.py check        # 检查当前配置")
        return
    
    command = sys.argv[1]
    
    if command == "check":
        check_environment()
    elif command in ["development", "production", "template"]:
        setup_environment(command)
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()
