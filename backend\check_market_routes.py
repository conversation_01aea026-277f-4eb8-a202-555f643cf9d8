import requests

response = requests.get('http://localhost:8000/api/v1/routes')
data = response.json()

print("All routes containing 'market':")
market_routes = [r for r in data['routes'] if 'market' in r['path']]
for route in market_routes:
    print(f"  {route['methods']} {route['path']}")

print(f"\nTotal market routes: {len(market_routes)}")

print("\nAll routes containing 'ws':")
ws_routes = [r for r in data['routes'] if '/ws' in r['path']]
for route in ws_routes:
    print(f"  {route['methods']} {route['path']}")

print(f"\nTotal ws routes: {len(ws_routes)}")

print(f"\nTotal routes: {data['total_routes']}")
