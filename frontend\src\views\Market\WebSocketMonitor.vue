<template>
  <div class="websocket-monitor">
    <el-card class="monitor-card">
      <template #header>
        <div class="card-header">
          <h3>WebSocket 性能监控</h3>
          <div class="header-actions">
            <el-button @click="refreshData" :loading="loading" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </div>
      </template>

      <!-- 连接状态概览 -->
      <div class="status-overview">
        <WebSocketStatus :show-details="true" />
      </div>

      <!-- 性能指标 -->
      <el-row :gutter="20" class="metrics-row">
        <el-col :span="8">
          <el-card class="metric-card">
            <el-statistic
              title="活跃连接数"
              :value="serverStats.active_connections"
              suffix="个"
            />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="metric-card">
            <el-statistic
              title="总订阅数"
              :value="serverStats.total_subscriptions"
              suffix="个"
            />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="metric-card">
            <el-statistic
              title="推送频率"
              :value="serverStats.performance?.activity_level || 0"
              :precision="3"
              suffix="次/秒"
            />
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细性能数据 -->
      <el-tabs v-model="activeTab" class="performance-tabs">
        <el-tab-pane label="推送性能" name="push">
          <div class="performance-grid">
            <div class="performance-item">
              <span class="label">自适应推送间隔:</span>
              <span class="value">{{ serverStats.performance?.adaptive_interval?.toFixed(3) }}s</span>
            </div>
            <div class="performance-item">
              <span class="label">总推送次数:</span>
              <span class="value">{{ serverStats.performance?.total_pushes || 0 }}</span>
            </div>
            <div class="performance-item">
              <span class="label">变化驱动推送:</span>
              <span class="value">{{ serverStats.performance?.change_driven_pushes || 0 }}</span>
            </div>
            <div class="performance-item">
              <span class="label">心跳推送:</span>
              <span class="value">{{ serverStats.performance?.heartbeat_pushes || 0 }}</span>
            </div>
            <div class="performance-item">
              <span class="label">最后推送时间:</span>
              <span class="value">{{ formatTimestamp(serverStats.performance?.last_push_time) }}</span>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="队列状态" name="queue">
          <div class="performance-grid">
            <div class="performance-item">
              <span class="label">队列总大小:</span>
              <span class="value">{{ serverStats.queues?.total_queue_size || 0 }}</span>
            </div>
            <div class="performance-item">
              <span class="label">丢弃消息数:</span>
              <span class="value">{{ serverStats.queues?.total_dropped_messages || 0 }}</span>
            </div>
            <div class="performance-item">
              <span class="label">队列最大容量:</span>
              <span class="value">{{ serverStats.queues?.queue_max_size || 0 }}</span>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="配置参数" name="config">
          <div class="config-grid">
            <div class="config-item">
              <span class="label">最小推送间隔:</span>
              <el-input-number
                v-model="configForm.min_push_interval"
                :min="0.05"
                :max="1"
                :step="0.05"
                :precision="3"
                size="small"
              />
              <span class="unit">秒</span>
            </div>
            <div class="config-item">
              <span class="label">最大推送间隔:</span>
              <el-input-number
                v-model="configForm.max_push_interval"
                :min="1"
                :max="10"
                :step="0.5"
                :precision="1"
                size="small"
              />
              <span class="unit">秒</span>
            </div>
            <div class="config-item">
              <span class="label">心跳间隔:</span>
              <el-input-number
                v-model="configForm.heartbeat_interval"
                :min="0.5"
                :max="5"
                :step="0.1"
                :precision="1"
                size="small"
              />
              <span class="unit">秒</span>
            </div>
            <div class="config-item">
              <span class="label">队列最大大小:</span>
              <el-input-number
                v-model="configForm.queue_max_size"
                :min="10"
                :max="1000"
                :step="10"
                size="small"
              />
              <span class="unit">条</span>
            </div>
          </div>
          <div class="config-actions">
            <el-button type="primary" @click="updateConfig" :loading="configLoading">
              应用配置
            </el-button>
            <el-button @click="resetConfig">
              重置
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 实时日志 -->
      <el-card class="log-card">
        <template #header>
          <div class="log-header">
            <span>实时日志</span>
            <el-button @click="clearLogs" size="small" type="danger" plain>
              清空日志
            </el-button>
          </div>
        </template>
        <div class="log-container">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="log-entry"
            :class="log.level"
          >
            <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import WebSocketStatus from '@/components/market/WebSocketStatus.vue'
import { useMarketWebSocket } from '@/composables/useMarketWebSocket'
import { marketApi } from '@/api/market'

// 状态
const loading = ref(false)
const configLoading = ref(false)
const autoRefresh = ref(true)
const activeTab = ref('push')
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 服务器统计数据
const serverStats = ref<any>({})

// 配置表单
const configForm = reactive({
  min_push_interval: 0.2,
  max_push_interval: 3.0,
  heartbeat_interval: 1.0,
  queue_max_size: 100
})

// 日志
interface LogEntry {
  timestamp: number
  level: 'info' | 'warn' | 'error'
  message: string
}

const logs = ref<LogEntry[]>([])
const maxLogs = 100

// 使用WebSocket状态
const { connectionStats } = useMarketWebSocket()

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/v1/ws/market/status')
    serverStats.value = await response.json()
    
    // 更新配置表单
    if (serverStats.value.config) {
      Object.assign(configForm, serverStats.value.config)
    }
    
    addLog('info', '数据刷新成功')
  } catch (error) {
    console.error('Failed to fetch server stats:', error)
    addLog('error', '获取服务器统计失败')
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const updateConfig = async () => {
  configLoading.value = true
  try {
    const response = await fetch('/api/v1/ws/market/config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configForm)
    })
    
    if (response.ok) {
      const result = await response.json()
      ElMessage.success('配置更新成功')
      addLog('info', `配置已更新: ${JSON.stringify(result.updated)}`)
      await refreshData()
    } else {
      throw new Error('配置更新失败')
    }
  } catch (error) {
    console.error('Failed to update config:', error)
    addLog('error', '配置更新失败')
    ElMessage.error('配置更新失败')
  } finally {
    configLoading.value = false
  }
}

const resetConfig = () => {
  Object.assign(configForm, {
    min_push_interval: 0.2,
    max_push_interval: 3.0,
    heartbeat_interval: 1.0,
    queue_max_size: 100
  })
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh()
  refreshTimer.value = setInterval(refreshData, 5000)
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const addLog = (level: LogEntry['level'], message: string) => {
  logs.value.unshift({
    timestamp: Date.now(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > maxLogs) {
    logs.value = logs.value.slice(0, maxLogs)
  }
}

const clearLogs = () => {
  logs.value = []
}

const formatTimestamp = (timestamp?: number) => {
  if (!timestamp) return '无'
  return new Date(timestamp * 1000).toLocaleTimeString()
}

const formatLogTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  refreshData()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
  
  addLog('info', 'WebSocket监控器已启动')
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.websocket-monitor {
  padding: 20px;
}

.monitor-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-overview {
  margin-bottom: 20px;
}

.metrics-row {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
}

.performance-tabs {
  margin-bottom: 20px;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

.performance-item .label {
  color: var(--el-text-color-secondary);
}

.performance-item .value {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-item .label {
  min-width: 120px;
  color: var(--el-text-color-secondary);
}

.config-item .unit {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.config-actions {
  display: flex;
  gap: 12px;
}

.log-card {
  margin-top: 20px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.log-time {
  color: var(--el-text-color-secondary);
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-level.info {
  color: var(--el-color-primary);
}

.log-level.warn {
  color: var(--el-color-warning);
}

.log-level.error {
  color: var(--el-color-danger);
}

.log-message {
  flex: 1;
  color: var(--el-text-color-primary);
}
</style>
