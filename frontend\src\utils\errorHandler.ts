/**
 * 前端统一错误处理工具
 * P0 修复版本
 */
import { ElMessage, ElNotification } from 'element-plus'

export interface ErrorContext {
  component?: string
  action?: string
  data?: any
}

export class ErrorHandler {
  /**
   * 处理API错误
   */
  static handleApiError(error: any, context: ErrorContext = {}) {
    console.error('API错误:', error, context)
    
    // 网络错误
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络状态')
      return
    }
    
    const status = error.response?.status
    const data = error.response?.data
    
    switch (status) {
      case 404:
        ElMessage.warning(data?.detail || '请求的资源不存在')
        break
      case 408:
        ElMessage.warning('请求超时，请稍后重试')
        break
      case 413:
        ElMessage.warning('数据量过大，请缩小查询范围')
        break
      case 429:
        ElMessage.warning('请求过于频繁，请稍后再试')
        break
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
      default:
        ElMessage.error(data?.detail || '操作失败，请稍后重试')
    }
  }
  
  /**
   * 处理图表错误
   */
  static handleChartError(error: any, context: ErrorContext = {}) {
    console.error('图表错误:', error, context)
    
    if (error.message?.includes('容器')) {
      ElMessage.warning('图表容器未准备好，正在重试...')
    } else if (error.message?.includes('数据')) {
      ElMessage.warning('图表数据格式错误')
    } else {
      ElMessage.error('图表加载失败，请刷新页面')
    }
  }
  
  /**
   * 处理WebSocket错误
   */
  static handleWebSocketError(error: any, context: ErrorContext = {}) {
    console.error('WebSocket错误:', error, context)
    
    ElNotification({
      title: '实时连接异常',
      message: '实时数据连接中断，正在尝试重新连接...',
      type: 'warning',
      duration: 3000
    })
  }
  
  /**
   * 处理文件操作错误
   */
  static handleFileError(error: any, context: ErrorContext = {}) {
    console.error('文件操作错误:', error, context)
    
    if (error.message?.includes('size')) {
      ElMessage.warning('文件过大，请选择较小的文件')
    } else if (error.message?.includes('format')) {
      ElMessage.warning('文件格式不支持')
    } else {
      ElMessage.error('文件操作失败')
    }
  }
  
  /**
   * 处理内存错误
   */
  static handleMemoryError(error: any, context: ErrorContext = {}) {
    console.error('内存错误:', error, context)
    
    ElNotification({
      title: '数据量过大',
      message: '请缩小查询范围或分批处理数据',
      type: 'warning',
      duration: 5000
    })
  }
  
  /**
   * 通用错误处理
   */
  static handleError(error: any, context: ErrorContext = {}) {
    console.error('通用错误:', error, context)
    
    // 判断错误类型
    if (error.name === 'ChartError' || context.component?.includes('chart')) {
      this.handleChartError(error, context)
    } else if (error.name === 'WebSocketError' || context.component?.includes('websocket')) {
      this.handleWebSocketError(error, context)
    } else if (error.name === 'FileError' || context.action?.includes('file')) {
      this.handleFileError(error, context)
    } else if (error.name === 'MemoryError' || error.message?.includes('memory')) {
      this.handleMemoryError(error, context)
    } else if (error.response || error.request) {
      this.handleApiError(error, context)
    } else {
      ElMessage.error(error.message || '未知错误')
    }
  }
}

/**
 * 安全执行异步函数
 */
export async function safeExecute<T>(
  fn: () => Promise<T>,
  context: ErrorContext = {},
  showError = true
): Promise<T | null> {
  try {
    return await fn()
  } catch (error) {
    if (showError) {
      ErrorHandler.handleError(error, context)
    }
    return null
  }
}

/**
 * 安全执行同步函数
 */
export function safeExecuteSync<T>(
  fn: () => T,
  context: ErrorContext = {},
  showError = true
): T | null {
  try {
    return fn()
  } catch (error) {
    if (showError) {
      ErrorHandler.handleError(error, context)
    }
    return null
  }
}

/**
 * 重试机制
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000,
  context: ErrorContext = {}
): Promise<T | null> {
  let lastError: any
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (i < maxRetries - 1) {
        console.warn(`第${i + 1}次重试失败，${delay}ms后重试:`, error)
        await new Promise(resolve => setTimeout(resolve, delay))
        delay *= 2 // 指数退避
      }
    }
  }
  
  // 所有重试都失败
  console.error(`${maxRetries}次重试后仍然失败:`, lastError)
  ErrorHandler.handleError(lastError, { ...context, action: 'retry_failed' })
  return null
}

/**
 * 超时包装器
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage = '操作超时'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs)
    })
  ])
}