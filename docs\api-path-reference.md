# 行情中心API路径参考

## 🎯 权威路径表（增强版为主）

### 增强版API（主要使用）
**前缀**: `/api/v1/market/enhanced-market`

| 功能 | 方法 | 路径 | 参数 |
|------|------|------|------|
| 股票详情 | GET | `/stock/{symbol}` | symbol: 股票代码 |
| K线数据 | GET | `/kline/{symbol}` | period, limit |
| 批量行情 | GET | `/quotes` | symbols: 逗号分隔 |
| 股票搜索 | GET | `/search` | query, limit |
| 自选股列表 | GET | `/watchlist` | - |
| 添加自选股 | POST | `/watchlist` | symbol |
| 移除自选股 | DELETE | `/watchlist/{symbol}` | symbol |
| 股票分析 | GET | `/analysis/{symbol}` | symbol, date |
| 健康检查 | GET | `/health` | - |

### 基础版API（fallback）
**前缀**: `/api/v1/market`

| 功能 | 方法 | 路径 | 参数 |
|------|------|------|------|
| 股票列表 | GET | `/stocks/list` | market, skip, limit |
| 实时行情 | GET | `/quotes/realtime` | symbols |
| 单股行情 | GET | `/quotes/{symbol}` | symbol |
| K线数据 | GET | `/kline/{symbol}` | period, start_date, end_date |
| 市场概览 | GET | `/overview` | - |
| 板块表现 | GET | `/sectors/performance` | - |
| 股票搜索 | GET | `/search` | keyword, limit |
| 市场深度 | GET | `/depth/{symbol}` | symbol |
| 逐笔数据 | GET | `/ticks/{symbol}` | limit |

## 🔌 WebSocket主题规范

### 订阅消息格式
```json
{
  "type": "subscribe",
  "data": {
    "topics": ["market:all", "market:000001", "market:000001:kline:1d"]
  }
}
```

### 推送消息格式
```json
{
  "type": "tick|kline",
  "symbol": "000001",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": { ... }
}
```

### 主题命名规范
- 全量行情: `market:all`
- 单股tick: `market:{symbol}`
- K线推送: `market:{symbol}:kline:{interval}`

## 🔄 数据源降级策略

1. **增强版API优先**: 使用真实数据源
2. **基础版API备选**: 增强版失败时降级
3. **Mock数据兜底**: 所有真实数据源不可用时

## 📝 前端适配说明

### API调用优先级
```javascript
// 1. 尝试增强版API
try {
  const response = await api.get('/market/enhanced-market/stock/000001')
  return response.data
} catch (error) {
  // 2. 降级到基础版API
  try {
    const response = await api.get('/market/quotes/000001')
    return response.data
  } catch (fallbackError) {
    // 3. 使用Mock数据
    return mockData
  }
}
```

### WebSocket连接
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8000/api/v1/ws')

// 订阅主题
ws.send(JSON.stringify({
  type: 'subscribe',
  data: { topics: ['market:all'] }
}))
```

## 🚀 部署检查清单

- [ ] 后端.env配置: `USE_REAL_DATA=true`
- [ ] 前端.env配置: API_BASE_URL正确
- [ ] WebSocket服务启动
- [ ] 数据源依赖安装: `pip install tushare akshare`
- [ ] API路径测试: 手动验证关键接口
- [ ] WebSocket测试: 验证订阅和推送
