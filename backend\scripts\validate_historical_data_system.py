"""
历史数据系统验证脚本
验证数据存储、缓存、API性能等功能
"""

import asyncio
import time
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
import pandas as pd
import aiohttp
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.unified_cache import unified_cache, CacheType, CacheConfig
from app.core.database import get_db
from app.services.enhanced_historical_service import enhanced_historical_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HistoricalDataValidator:
    """历史数据系统验证器"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000/api/v1/enhanced-historical"
        self.test_results = {
            'cache_performance': {},
            'api_performance': {},
            'data_integrity': {},
            'system_health': {}
        }
        self.session = None
    
    async def initialize(self):
        """初始化验证器"""
        # 初始化缓存系统
        await unified_cache.initialize()
        
        # 创建HTTP会话
        self.session = aiohttp.ClientSession()
        
        logger.info("✅ 验证器初始化完成")
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
        logger.info("🧹 资源清理完成")
    
    async def validate_cache_system(self) -> Dict[str, Any]:
        """验证缓存系统性能"""
        logger.info("🔄 开始验证缓存系统...")
        
        results = {}
        
        # 测试1: 基本读写性能
        write_times = []
        read_times = []
        
        for i in range(100):
            test_data = {
                'symbol': f'TEST{i:03d}',
                'data': f'test_data_{i}',
                'timestamp': time.time()
            }
            
            # 测试写入
            start_time = time.perf_counter()
            await unified_cache.set(CacheType.HISTORICAL, test_data, f'test_{i}')
            write_time = time.perf_counter() - start_time
            write_times.append(write_time)
            
            # 测试读取
            start_time = time.perf_counter()
            cached_data = await unified_cache.get(CacheType.HISTORICAL, f'test_{i}')
            read_time = time.perf_counter() - start_time
            read_times.append(read_time)
            
            # 验证数据完整性
            assert cached_data == test_data, f"缓存数据不匹配: {i}"
        
        results['basic_rw_performance'] = {
            'avg_write_time': sum(write_times) / len(write_times) * 1000,  # ms
            'max_write_time': max(write_times) * 1000,
            'avg_read_time': sum(read_times) / len(read_times) * 1000,
            'max_read_time': max(read_times) * 1000,
            'total_operations': 200
        }
        
        # 测试2: 并发访问性能
        async def concurrent_cache_operation(index):
            data = {'concurrent_test': index}
            await unified_cache.set(CacheType.HISTORICAL, data, f'concurrent_{index}')
            result = await unified_cache.get(CacheType.HISTORICAL, f'concurrent_{index}')
            return result
        
        start_time = time.perf_counter()
        tasks = [concurrent_cache_operation(i) for i in range(50)]
        concurrent_results = await asyncio.gather(*tasks)
        concurrent_time = time.perf_counter() - start_time
        
        results['concurrent_performance'] = {
            'total_time': concurrent_time * 1000,  # ms
            'operations_per_second': len(tasks) / concurrent_time,
            'success_rate': len([r for r in concurrent_results if r is not None]) / len(tasks)
        }
        
        # 测试3: 缓存命中率
        hit_count = 0
        miss_count = 0
        
        for i in range(20):
            # 50%的概率访问已存在的键
            if i % 2 == 0:
                result = await unified_cache.get(CacheType.HISTORICAL, f'test_{i}')
                if result is not None:
                    hit_count += 1
                else:
                    miss_count += 1
            else:
                result = await unified_cache.get(CacheType.HISTORICAL, f'nonexistent_{i}')
                if result is None:
                    miss_count += 1
                else:
                    hit_count += 1
        
        results['cache_hit_rate'] = {
            'hits': hit_count,
            'misses': miss_count,
            'hit_rate': hit_count / (hit_count + miss_count) if (hit_count + miss_count) > 0 else 0
        }
        
        # 测试4: TTL功能
        await unified_cache.set(CacheType.REALTIME, {'ttl_test': True}, 'ttl_test', expire=1)
        
        # 立即检查
        immediate_result = await unified_cache.get(CacheType.REALTIME, 'ttl_test')
        
        # 等待过期
        await asyncio.sleep(2)
        
        # 检查过期
        expired_result = await unified_cache.get(CacheType.REALTIME, 'ttl_test')
        
        results['ttl_functionality'] = {
            'immediate_access': immediate_result is not None,
            'expired_access': expired_result is None,
            'ttl_working': immediate_result is not None and expired_result is None
        }
        
        logger.info("✅ 缓存系统验证完成")
        return results
    
    async def validate_api_performance(self) -> Dict[str, Any]:
        """验证API性能"""
        logger.info("🌐 开始验证API性能...")
        
        if not self.session:
            raise RuntimeError("HTTP会话未初始化")
        
        results = {}
        
        # 测试用的token（实际使用中需要真实token）
        headers = {'Authorization': 'Bearer test_token'}
        
        try:
            # 测试1: 股票列表查询性能
            start_time = time.perf_counter()
            
            async with self.session.get(
                f"{self.api_base_url}/historical/stocks",
                headers=headers,
                params={'page': 1, 'page_size': 50}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    api_time = time.perf_counter() - start_time
                    
                    results['stock_list_query'] = {
                        'response_time': api_time * 1000,  # ms
                        'status_code': response.status,
                        'data_count': len(data.get('data', {}).get('stocks', [])),
                        'cached': data.get('cached', False)
                    }
                else:
                    results['stock_list_query'] = {
                        'error': f"HTTP {response.status}",
                        'response_time': time.perf_counter() - start_time * 1000
                    }
            
            # 测试2: 搜索功能性能
            search_queries = ['平安', '银行', '000001', '科技']
            search_results = []
            
            for query in search_queries:
                start_time = time.perf_counter()
                
                async with self.session.get(
                    f"{self.api_base_url}/historical/search",
                    headers=headers,
                    params={'keyword': query, 'limit': 20}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        search_time = time.perf_counter() - start_time
                        
                        search_results.append({
                            'query': query,
                            'response_time': search_time * 1000,
                            'result_count': len(data.get('data', [])),
                            'cached': data.get('cached', False)
                        })
                    else:
                        search_results.append({
                            'query': query,
                            'error': f"HTTP {response.status}",
                            'response_time': time.perf_counter() - start_time * 1000
                        })
            
            results['search_performance'] = {
                'queries': search_results,
                'avg_response_time': sum(r.get('response_time', 0) for r in search_results) / len(search_results),
                'success_rate': len([r for r in search_results if 'error' not in r]) / len(search_results)
            }
            
            # 测试3: 并发API请求
            async def concurrent_api_request(index):
                try:
                    async with self.session.get(
                        f"{self.api_base_url}/historical/stats",
                        headers=headers
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return {'success': True, 'cached': data.get('cached', False)}
                        else:
                            return {'success': False, 'status': response.status}
                except Exception as e:
                    return {'success': False, 'error': str(e)}
            
            start_time = time.perf_counter()
            concurrent_tasks = [concurrent_api_request(i) for i in range(10)]
            concurrent_api_results = await asyncio.gather(*concurrent_tasks)
            concurrent_api_time = time.perf_counter() - start_time
            
            results['concurrent_api_performance'] = {
                'total_time': concurrent_api_time * 1000,  # ms
                'requests_per_second': len(concurrent_tasks) / concurrent_api_time,
                'success_rate': len([r for r in concurrent_api_results if r.get('success')]) / len(concurrent_api_results),
                'cache_hit_rate': len([r for r in concurrent_api_results if r.get('cached')]) / len(concurrent_api_results)
            }
            
        except Exception as e:
            logger.error(f"API性能测试失败: {e}")
            results['error'] = str(e)
        
        logger.info("✅ API性能验证完成")
        return results
    
    async def validate_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        logger.info("🔍 开始验证数据完整性...")
        
        results = {}
        
        # 测试1: CSV数据清洗功能
        test_csv_data = pd.DataFrame({
            '日期': ['2023-01-01', '2023-01-02', '2023-01-03', 'invalid_date'],
            '开盘价': [10.0, 10.5, -1.0, 11.0],  # 包含负价格
            '收盘价': [10.5, 11.0, 10.8, 11.2],
            '最高价': [10.8, 11.2, 11.3, 11.5],
            '最低价': [9.8, 10.3, 12.0, 10.8],  # 包含不合理的最低价
            '成交量(手)': [1000000, 1200000, -500000, 800000],  # 包含负成交量
            '成交额(元)': [10500000, 13200000, 8640000, 8960000]
        })
        
        try:
            cleaned_data = enhanced_historical_service._clean_csv_data(test_csv_data)
            
            results['csv_data_cleaning'] = {
                'original_rows': len(test_csv_data),
                'cleaned_rows': len(cleaned_data),
                'invalid_rows_filtered': len(test_csv_data) - len(cleaned_data),
                'data_quality_check': len(cleaned_data) > 0 and len(cleaned_data) < len(test_csv_data)
            }
            
            # 验证清洗后数据的合理性
            if len(cleaned_data) > 0:
                price_validation = []
                for _, row in cleaned_data.iterrows():
                    price_validation.append({
                        'positive_prices': all(row[col] > 0 for col in ['open_price', 'close_price', 'high_price', 'low_price']),
                        'high_low_consistency': row['high_price'] >= row['low_price'],
                        'volume_positive': row['volume'] >= 0,
                        'amount_positive': row['amount'] >= 0
                    })
                
                results['price_validation'] = {
                    'all_positive_prices': all(v['positive_prices'] for v in price_validation),
                    'high_low_consistent': all(v['high_low_consistency'] for v in price_validation),
                    'volume_valid': all(v['volume_positive'] for v in price_validation),
                    'amount_valid': all(v['amount_positive'] for v in price_validation)
                }
            
        except Exception as e:
            results['csv_data_cleaning'] = {'error': str(e)}
        
        # 测试2: 市场代码识别
        test_symbols = ['000001', '600000', '300001', '688001', '430001']
        market_recognition = []
        
        for symbol in test_symbols:
            market = enhanced_historical_service._determine_market(symbol)
            market_recognition.append({
                'symbol': symbol,
                'market': market,
                'valid': market in ['SH', 'SZ', 'BJ']
            })
        
        results['market_recognition'] = {
            'test_cases': market_recognition,
            'accuracy': len([m for m in market_recognition if m['valid']]) / len(market_recognition)
        }
        
        # 测试3: 行业分类
        test_names = ['平安银行', '中国石油', '腾讯控股', '五粮液', '比亚迪']
        industry_classification = []
        
        for name in test_names:
            industry = enhanced_historical_service._determine_industry(name)
            industry_classification.append({
                'name': name,
                'industry': industry,
                'classified': industry != '其他'
            })
        
        results['industry_classification'] = {
            'test_cases': industry_classification,
            'classification_rate': len([i for i in industry_classification if i['classified']]) / len(industry_classification)
        }
        
        logger.info("✅ 数据完整性验证完成")
        return results
    
    async def validate_system_health(self) -> Dict[str, Any]:
        """验证系统健康状态"""
        logger.info("🏥 开始验证系统健康状态...")
        
        results = {}
        
        # 测试1: 缓存系统健康
        try:
            cache_stats = await unified_cache.get_stats()
            results['cache_health'] = {
                'operational': True,
                'backend_available': cache_stats.get('primary_backend') is not None,
                'fallback_available': cache_stats.get('fallback_backend') is not None,
                'stats': cache_stats
            }
        except Exception as e:
            results['cache_health'] = {
                'operational': False,
                'error': str(e)
            }
        
        # 测试2: 配置验证
        config_checks = []
        
        # 检查缓存TTL配置
        for cache_type in CacheType:
            ttl = CacheConfig.get_ttl(cache_type)
            config_checks.append({
                'cache_type': cache_type.value,
                'ttl': ttl,
                'valid': ttl > 0
            })
        
        results['configuration'] = {
            'cache_ttl_configs': config_checks,
            'all_configs_valid': all(c['valid'] for c in config_checks)
        }
        
        # 测试3: 内存使用情况
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        results['resource_usage'] = {
            'memory_rss': memory_info.rss / 1024 / 1024,  # MB
            'memory_vms': memory_info.vms / 1024 / 1024,  # MB
            'cpu_percent': process.cpu_percent(),
            'memory_healthy': memory_info.rss < 1024 * 1024 * 1024  # < 1GB
        }
        
        logger.info("✅ 系统健康状态验证完成")
        return results
    
    async def run_full_validation(self) -> Dict[str, Any]:
        """运行完整验证"""
        logger.info("🚀 开始完整系统验证...")
        
        await self.initialize()
        
        try:
            # 收集所有测试结果
            self.test_results['cache_performance'] = await self.validate_cache_system()
            self.test_results['api_performance'] = await self.validate_api_performance()
            self.test_results['data_integrity'] = await self.validate_data_integrity()
            self.test_results['system_health'] = await self.validate_system_health()
            
            # 生成总体评分
            overall_score = self.calculate_overall_score()
            self.test_results['overall_assessment'] = overall_score
            
            # 生成报告
            self.generate_report()
            
            logger.info("✅ 完整系统验证完成")
            
        except Exception as e:
            logger.error(f"验证过程中发生错误: {e}")
            self.test_results['validation_error'] = str(e)
        
        finally:
            await self.cleanup()
        
        return self.test_results
    
    def calculate_overall_score(self) -> Dict[str, Any]:
        """计算总体评分"""
        scores = {}
        
        # 缓存性能评分
        cache_perf = self.test_results.get('cache_performance', {})
        if 'basic_rw_performance' in cache_perf:
            avg_read_time = cache_perf['basic_rw_performance'].get('avg_read_time', 999)
            avg_write_time = cache_perf['basic_rw_performance'].get('avg_write_time', 999)
            
            # 读写时间小于1ms得满分，大于10ms得0分
            read_score = max(0, min(100, (10 - avg_read_time) * 10))
            write_score = max(0, min(100, (10 - avg_write_time) * 10))
            scores['cache_performance'] = (read_score + write_score) / 2
        
        # API性能评分
        api_perf = self.test_results.get('api_performance', {})
        if 'stock_list_query' in api_perf:
            response_time = api_perf['stock_list_query'].get('response_time', 9999)
            # 响应时间小于100ms得满分，大于1000ms得0分
            scores['api_performance'] = max(0, min(100, (1000 - response_time) / 10))
        
        # 数据完整性评分
        data_integrity = self.test_results.get('data_integrity', {})
        integrity_score = 0
        if 'csv_data_cleaning' in data_integrity:
            integrity_score += 30 if data_integrity['csv_data_cleaning'].get('data_quality_check') else 0
        if 'market_recognition' in data_integrity:
            accuracy = data_integrity['market_recognition'].get('accuracy', 0)
            integrity_score += accuracy * 35
        if 'industry_classification' in data_integrity:
            classification_rate = data_integrity['industry_classification'].get('classification_rate', 0)
            integrity_score += classification_rate * 35
        scores['data_integrity'] = integrity_score
        
        # 系统健康评分
        system_health = self.test_results.get('system_health', {})
        health_score = 0
        if system_health.get('cache_health', {}).get('operational'):
            health_score += 50
        if system_health.get('resource_usage', {}).get('memory_healthy'):
            health_score += 50
        scores['system_health'] = health_score
        
        # 计算总分
        overall_score = sum(scores.values()) / len(scores) if scores else 0
        
        return {
            'component_scores': scores,
            'overall_score': overall_score,
            'grade': self.get_grade(overall_score),
            'recommendations': self.get_recommendations(scores)
        }
    
    def get_grade(self, score: float) -> str:
        """根据分数获取等级"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B'
        elif score >= 60:
            return 'C'
        else:
            return 'D'
    
    def get_recommendations(self, scores: Dict[str, float]) -> List[str]:
        """根据分数生成改进建议"""
        recommendations = []
        
        if scores.get('cache_performance', 0) < 70:
            recommendations.append("建议优化缓存性能，考虑使用更快的缓存后端或调整缓存策略")
        
        if scores.get('api_performance', 0) < 70:
            recommendations.append("建议优化API响应时间，检查数据库查询和网络延迟")
        
        if scores.get('data_integrity', 0) < 80:
            recommendations.append("建议加强数据验证和清洗规则，提高数据质量")
        
        if scores.get('system_health', 0) < 80:
            recommendations.append("建议检查系统资源使用情况，优化内存管理")
        
        if not recommendations:
            recommendations.append("系统运行良好，建议继续监控性能指标")
        
        return recommendations
    
    def generate_report(self):
        """生成验证报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"validation_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成简要报告
        overall = self.test_results.get('overall_assessment', {})
        print("\n" + "="*60)
        print("📊 历史数据系统验证报告")
        print("="*60)
        print(f"总体评分: {overall.get('overall_score', 0):.1f}/100 ({overall.get('grade', 'N/A')})")
        print("\n组件评分:")
        for component, score in overall.get('component_scores', {}).items():
            print(f"  {component}: {score:.1f}/100")
        
        print("\n改进建议:")
        for rec in overall.get('recommendations', []):
            print(f"  • {rec}")
        
        print(f"\n详细报告已保存至: {report_file}")
        print("="*60)


async def main():
    """主函数"""
    print("🚀 启动历史数据系统验证...")
    
    validator = HistoricalDataValidator()
    
    try:
        results = await validator.run_full_validation()
        
        # 检查是否有严重错误
        if 'validation_error' in results:
            print(f"❌ 验证失败: {results['validation_error']}")
            return 1
        
        # 检查总体评分
        overall_score = results.get('overall_assessment', {}).get('overall_score', 0)
        if overall_score >= 80:
            print("✅ 系统验证通过，运行状况良好")
            return 0
        elif overall_score >= 60:
            print("⚠️ 系统基本可用，但需要优化")
            return 0
        else:
            print("❌ 系统存在严重问题，需要立即处理")
            return 1
            
    except Exception as e:
        print(f"❌ 验证过程中发生未预期的错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())