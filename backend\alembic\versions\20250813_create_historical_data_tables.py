"""create historical data tables

Revision ID: 20250813_historical_data
Revises: 
Create Date: 2025-08-13 17:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250813_historical_data'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Create historical data tables with optimized indexes"""
    
    # Create historical_kline_data table
    op.create_table(
        'historical_kline_data',
        sa.Column('symbol', sa.String(20), nullable=False, comment='股票代码'),
        sa.Column('trade_date', sa.DateTime(), nullable=False, comment='交易日期'),
        sa.Column('open_price', sa.Float(), nullable=False, comment='开盘价'),
        sa.Column('close_price', sa.Float(), nullable=False, comment='收盘价'),
        sa.Column('high_price', sa.Float(), nullable=False, comment='最高价'),
        sa.Column('low_price', sa.Float(), nullable=False, comment='最低价'),
        sa.Column('volume', sa.BigInteger(), nullable=False, default=0, comment='成交量(手)'),
        sa.Column('amount', sa.Float(), nullable=False, default=0, comment='成交额(元)'),
        sa.Column('change_amount', sa.Float(), comment='涨跌额'),
        sa.Column('change_percent', sa.Float(), comment='涨跌幅(%)'),
        sa.Column('amplitude', sa.Float(), comment='振幅(%)'),
        sa.Column('turnover_rate', sa.Float(), comment='换手率(%)'),
        sa.Column('ma5', sa.Float(), comment='5日均线'),
        sa.Column('ma10', sa.Float(), comment='10日均线'),
        sa.Column('ma20', sa.Float(), comment='20日均线'),
        sa.Column('ma60', sa.Float(), comment='60日均线'),
        sa.Column('data_source', sa.String(50), default='csv_import', comment='数据来源'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now(), comment='更新时间'),
        sa.PrimaryKeyConstraint('symbol', 'trade_date'),
        comment='历史K线数据表，支持高效时序查询'
    )
    
    # Create stock_basic_info table
    op.create_table(
        'stock_basic_info',
        sa.Column('symbol', sa.String(20), primary_key=True, comment='股票代码'),
        sa.Column('name', sa.String(100), nullable=False, comment='股票名称'),
        sa.Column('market', sa.String(10), nullable=False, comment='市场代码 SH/SZ/BJ'),
        sa.Column('industry', sa.String(50), comment='所属行业'),
        sa.Column('sector', sa.String(50), comment='所属板块'),
        sa.Column('list_date', sa.DateTime(), comment='上市日期'),
        sa.Column('delist_date', sa.DateTime(), comment='退市日期'),
        sa.Column('status', sa.String(20), default='L', comment='状态 L-正常 D-退市 P-暂停'),
        sa.Column('total_share', sa.Float(), comment='总股本'),
        sa.Column('float_share', sa.Float(), comment='流通股本'),
        sa.Column('market_cap', sa.Float(), comment='总市值'),
        sa.Column('first_trade_date', sa.DateTime(), comment='首个交易日期'),
        sa.Column('last_trade_date', sa.DateTime(), comment='最后交易日期'),
        sa.Column('total_records', sa.Integer(), default=0, comment='历史数据条数'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now(), comment='更新时间'),
        comment='股票基本信息表'
    )
    
    # Create data_import_log table
    op.create_table(
        'data_import_log',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('symbol', sa.String(20), nullable=False, comment='股票代码'),
        sa.Column('import_type', sa.String(20), nullable=False, comment='导入类型 csv/api/manual'),
        sa.Column('file_path', sa.Text(), comment='原始文件路径'),
        sa.Column('total_records', sa.Integer(), default=0, comment='总记录数'),
        sa.Column('success_records', sa.Integer(), default=0, comment='成功记录数'),
        sa.Column('error_records', sa.Integer(), default=0, comment='错误记录数'),
        sa.Column('start_date', sa.DateTime(), comment='数据开始日期'),
        sa.Column('end_date', sa.DateTime(), comment='数据结束日期'),
        sa.Column('status', sa.String(20), default='processing', comment='状态 processing/completed/failed'),
        sa.Column('error_message', sa.Text(), comment='错误信息'),
        sa.Column('import_started_at', sa.DateTime(), server_default=sa.func.now(), comment='导入开始时间'),
        sa.Column('import_completed_at', sa.DateTime(), comment='导入完成时间'),
        comment='数据导入日志表'
    )
    
    # Create market_calendar table
    op.create_table(
        'market_calendar',
        sa.Column('trade_date', sa.DateTime(), primary_key=True, comment='交易日期'),
        sa.Column('is_trading_day', sa.Integer(), default=1, comment='是否交易日 1-是 0-否'),
        sa.Column('market', sa.String(10), default='ALL', comment='市场 SH/SZ/ALL'),
        sa.Column('holiday_name', sa.String(50), comment='节假日名称'),
        sa.Column('week_day', sa.Integer(), comment='星期几 1-7'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), comment='创建时间'),
        comment='交易日历表'
    )
    
    # Create optimized indexes for historical_kline_data
    op.create_index('idx_symbol_date', 'historical_kline_data', ['symbol', 'trade_date'])
    op.create_index('idx_trade_date', 'historical_kline_data', ['trade_date'])
    op.create_index('idx_symbol', 'historical_kline_data', ['symbol'])
    op.create_index('idx_change_percent', 'historical_kline_data', ['change_percent'])
    op.create_index('idx_amount', 'historical_kline_data', ['amount'])
    op.create_index('idx_volume', 'historical_kline_data', ['volume'])
    
    # Create indexes for stock_basic_info
    op.create_index('idx_market', 'stock_basic_info', ['market'])
    op.create_index('idx_industry', 'stock_basic_info', ['industry'])
    op.create_index('idx_name', 'stock_basic_info', ['name'])
    op.create_index('idx_status', 'stock_basic_info', ['status'])
    
    # Create indexes for data_import_log
    op.create_index('idx_symbol_import', 'data_import_log', ['symbol', 'import_started_at'])
    op.create_index('idx_status_log', 'data_import_log', ['status'])
    op.create_index('idx_import_date', 'data_import_log', ['import_started_at'])
    
    # Create indexes for market_calendar
    op.create_index('idx_trade_date_market', 'market_calendar', ['trade_date', 'market'])
    op.create_index('idx_is_trading', 'market_calendar', ['is_trading_day'])


def downgrade():
    """Drop historical data tables"""
    
    # Drop indexes first
    op.drop_index('idx_is_trading', table_name='market_calendar')
    op.drop_index('idx_trade_date_market', table_name='market_calendar')
    
    op.drop_index('idx_import_date', table_name='data_import_log')
    op.drop_index('idx_status_log', table_name='data_import_log')
    op.drop_index('idx_symbol_import', table_name='data_import_log')
    
    op.drop_index('idx_status', table_name='stock_basic_info')
    op.drop_index('idx_name', table_name='stock_basic_info')
    op.drop_index('idx_industry', table_name='stock_basic_info')
    op.drop_index('idx_market', table_name='stock_basic_info')
    
    op.drop_index('idx_volume', table_name='historical_kline_data')
    op.drop_index('idx_amount', table_name='historical_kline_data')
    op.drop_index('idx_change_percent', table_name='historical_kline_data')
    op.drop_index('idx_symbol', table_name='historical_kline_data')
    op.drop_index('idx_trade_date', table_name='historical_kline_data')
    op.drop_index('idx_symbol_date', table_name='historical_kline_data')
    
    # Drop tables
    op.drop_table('market_calendar')
    op.drop_table('data_import_log')
    op.drop_table('stock_basic_info')
    op.drop_table('historical_kline_data')