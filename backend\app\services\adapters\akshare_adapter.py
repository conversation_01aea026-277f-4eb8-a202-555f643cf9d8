"""
AkShare数据源接口
提供股票、指数、基金等金融数据获取功能
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
import asyncio
import json
# from sqlalchemy.ext.asyncio import AsyncSession
# from ..core.database import get_db_session

# AkShare imports
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

logger = logging.getLogger(__name__)


class AkShareDataSource:
    """AkShare数据源接口"""
    
    def __init__(self):
        self.cache = {}  # 简单缓存机制
        self.cache_timeout = 300  # 5分钟缓存
        
    async def is_available(self) -> bool:
        """检查AkShare是否可用"""
        return AKSHARE_AVAILABLE
    
    async def get_stock_list(self, market: str = "A股") -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 ("A股", "港股", "美股")
            
        Returns:
            股票列表
        """
        if not AKSHARE_AVAILABLE:
            logger.warning("AkShare不可用，返回模拟数据")
            return self._get_mock_stock_list()
        
        try:
            cache_key = f"stock_list_{market}"
            if cache_key in self.cache:
                cached_time, cached_data = self.cache[cache_key]
                if (datetime.now() - cached_time).seconds < self.cache_timeout:
                    return cached_data
            
            if market == "A股":
                # 获取A股股票列表
                stock_info_a_code_name_df = await asyncio.get_event_loop().run_in_executor(
                    None, ak.stock_info_a_code_name
                )
                
                stock_list = []
                for _, row in stock_info_a_code_name_df.iterrows():
                    stock_list.append({
                        "symbol": row["code"],
                        "name": row["name"],
                        "market": "A股",
                        "exchange": "SH" if row["code"].startswith("6") else "SZ"
                    })
                
            elif market == "港股":
                # 获取港股股票列表
                stock_hk_spot_df = await asyncio.get_event_loop().run_in_executor(
                    None, ak.stock_hk_spot
                )
                
                stock_list = []
                for _, row in stock_hk_spot_df.head(100).iterrows():  # 限制数量
                    stock_list.append({
                        "symbol": row["代码"],
                        "name": row["名称"],
                        "market": "港股",
                        "exchange": "HK"
                    })
                    
            elif market == "美股":
                # 获取美股股票列表（纳斯达克）
                stock_us_spot_df = await asyncio.get_event_loop().run_in_executor(
                    None, ak.stock_us_spot
                )
                
                stock_list = []
                for _, row in stock_us_spot_df.head(100).iterrows():  # 限制数量
                    stock_list.append({
                        "symbol": row["代码"],
                        "name": row["名称"],
                        "market": "美股",
                        "exchange": "NASDAQ"
                    })
            else:
                stock_list = []
            
            # 缓存结果
            self.cache[cache_key] = (datetime.now(), stock_list)
            
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return self._get_mock_stock_list()
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        period: str = "daily",
        adjust: str = "qfq"
    ) -> pd.DataFrame:
        """
        获取股票历史数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            period: 数据周期 ("daily", "weekly", "monthly")
            adjust: 复权类型 ("qfq"前复权, "hfq"后复权, "no"不复权)
            
        Returns:
            股票数据DataFrame
        """
        if not AKSHARE_AVAILABLE:
            logger.warning("AkShare不可用，返回模拟数据")
            return self._get_mock_stock_data(symbol, start_date, end_date)
        
        try:
            # 默认日期范围
            if not end_date:
                end_date = datetime.now().strftime("%Y%m%d")
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
            
            # 转换日期格式
            start_date = start_date.replace("-", "")
            end_date = end_date.replace("-", "")
            
            cache_key = f"stock_data_{symbol}_{start_date}_{end_date}_{period}_{adjust}"
            if cache_key in self.cache:
                cached_time, cached_data = self.cache[cache_key]
                if (datetime.now() - cached_time).seconds < self.cache_timeout:
                    return cached_data
            
            # 获取股票历史数据
            if period == "daily":
                stock_df = await asyncio.get_event_loop().run_in_executor(
                    None, ak.stock_zh_a_hist, symbol, period, start_date, end_date, adjust
                )
            else:
                # 其他周期的数据获取
                stock_df = await asyncio.get_event_loop().run_in_executor(
                    None, ak.stock_zh_a_hist, symbol, "daily", start_date, end_date, adjust
                )
            
            if stock_df is not None and not stock_df.empty:
                # 标准化列名
                stock_df = stock_df.rename(columns={
                    "日期": "date",
                    "开盘": "open",
                    "收盘": "close",
                    "最高": "high",
                    "最低": "low",
                    "成交量": "volume",
                    "成交额": "amount",
                    "振幅": "amplitude",
                    "涨跌幅": "pct_chg",
                    "涨跌额": "change",
                    "换手率": "turnover"
                })
                
                # 确保日期格式
                stock_df["date"] = pd.to_datetime(stock_df["date"])
                stock_df = stock_df.set_index("date")
                
                # 缓存结果
                self.cache[cache_key] = (datetime.now(), stock_df)
                
                return stock_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {str(e)}")
            return self._get_mock_stock_data(symbol, start_date, end_date)
    
    async def get_realtime_data(self, symbols: List[str]) -> Dict[str, Any]:
        """
        获取实时股票数据
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            实时数据字典
        """
        if not AKSHARE_AVAILABLE:
            logger.warning("AkShare不可用，返回模拟数据")
            return self._get_mock_realtime_data(symbols)
        
        try:
            realtime_data = {}
            
            for symbol in symbols:
                try:
                    # 获取实时数据
                    realtime_df = await asyncio.get_event_loop().run_in_executor(
                        None, ak.stock_zh_a_spot_em
                    )
                    
                    # 查找指定股票
                    stock_data = realtime_df[realtime_df["代码"] == symbol]
                    
                    if not stock_data.empty:
                        row = stock_data.iloc[0]
                        realtime_data[symbol] = {
                            "symbol": symbol,
                            "name": row["名称"],
                            "price": float(row["最新价"]),
                            "change": float(row["涨跌额"]),
                            "pct_change": float(row["涨跌幅"]),
                            "volume": int(row["成交量"]),
                            "amount": float(row["成交额"]),
                            "high": float(row["最高"]),
                            "low": float(row["最低"]),
                            "open": float(row["今开"]),
                            "yesterday_close": float(row["昨收"]),
                            "timestamp": datetime.now().isoformat()
                        }
                    
                except Exception as e:
                    logger.error(f"获取 {symbol} 实时数据失败: {str(e)}")
                    continue
            
            return realtime_data
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {str(e)}")
            return self._get_mock_realtime_data(symbols)
    
    async def get_index_data(
        self,
        index_code: str = "000001",  # 上证指数
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """
        获取指数数据
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            指数数据
        """
        if not AKSHARE_AVAILABLE:
            return self._get_mock_index_data(index_code, start_date, end_date)
        
        try:
            # 获取指数历史数据
            index_df = await asyncio.get_event_loop().run_in_executor(
                None, ak.stock_zh_index_daily, index_code
            )
            
            if not index_df.empty:
                # 标准化列名
                index_df = index_df.rename(columns={
                    "date": "date",
                    "open": "open",
                    "close": "close",
                    "high": "high",
                    "low": "low",
                    "volume": "volume"
                })
                
                index_df["date"] = pd.to_datetime(index_df["date"])
                index_df = index_df.set_index("date")
                
                # 筛选日期范围
                if start_date:
                    index_df = index_df[index_df.index >= start_date]
                if end_date:
                    index_df = index_df[index_df.index <= end_date]
                
                return index_df
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取指数数据失败 {index_code}: {str(e)}")
            return self._get_mock_index_data(index_code, start_date, end_date)
    
    async def get_financial_indicators(self, symbol: str, year: int = None) -> Dict[str, Any]:
        """
        获取财务指标数据
        
        Args:
            symbol: 股票代码
            year: 年份
            
        Returns:
            财务指标数据
        """
        if not AKSHARE_AVAILABLE:
            return self._get_mock_financial_data(symbol)
        
        try:
            # 获取财务指标
            indicators_df = await asyncio.get_event_loop().run_in_executor(
                None, ak.stock_financial_abstract, symbol
            )
            
            if not indicators_df.empty:
                # 转换为字典格式
                latest_data = indicators_df.iloc[-1].to_dict()
                
                return {
                    "symbol": symbol,
                    "report_date": latest_data.get("报告期", ""),
                    "revenue": latest_data.get("营业总收入", 0),
                    "net_profit": latest_data.get("净利润", 0),
                    "eps": latest_data.get("每股收益", 0),
                    "roe": latest_data.get("净资产收益率", 0),
                    "debt_ratio": latest_data.get("资产负债率", 0),
                    "gross_margin": latest_data.get("销售毛利率", 0)
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"获取财务指标失败 {symbol}: {str(e)}")
            return self._get_mock_financial_data(symbol)
    
    async def get_news_data(self, symbol: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取新闻数据
        
        Args:
            symbol: 股票代码（可选）
            limit: 新闻条数限制
            
        Returns:
            新闻数据列表
        """
        if not AKSHARE_AVAILABLE:
            return self._get_mock_news_data(symbol, limit)
        
        try:
            # 获取财经新闻
            news_df = await asyncio.get_event_loop().run_in_executor(
                None, ak.stock_news_em
            )
            
            if not news_df.empty:
                news_list = []
                for _, row in news_df.head(limit).iterrows():
                    news_list.append({
                        "title": row.get("新闻标题", ""),
                        "content": row.get("新闻内容", ""),
                        "publish_time": row.get("发布时间", ""),
                        "source": row.get("新闻来源", ""),
                        "url": row.get("新闻链接", "")
                    })
                
                return news_list
            
            return []
            
        except Exception as e:
            logger.error(f"获取新闻数据失败: {str(e)}")
            return self._get_mock_news_data(symbol, limit)
    
    # Mock数据方法
    def _get_mock_stock_list(self) -> List[Dict[str, Any]]:
        """获取模拟股票列表"""
        return [
            {"symbol": "000001", "name": "平安银行", "market": "A股", "exchange": "SZ"},
            {"symbol": "000002", "name": "万科A", "market": "A股", "exchange": "SZ"},
            {"symbol": "600000", "name": "浦发银行", "market": "A股", "exchange": "SH"},
            {"symbol": "600036", "name": "招商银行", "market": "A股", "exchange": "SH"},
            {"symbol": "600519", "name": "贵州茅台", "market": "A股", "exchange": "SH"},
            {"symbol": "000858", "name": "五粮液", "market": "A股", "exchange": "SZ"},
            {"symbol": "002415", "name": "海康威视", "market": "A股", "exchange": "SZ"},
            {"symbol": "000625", "name": "长安汽车", "market": "A股", "exchange": "SZ"},
            {"symbol": "002594", "name": "比亚迪", "market": "A股", "exchange": "SZ"},
            {"symbol": "300059", "name": "东方财富", "market": "A股", "exchange": "SZ"}
        ]
    
    def _get_mock_stock_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取模拟股票数据"""
        try:
            start = pd.to_datetime(start_date)
            end = pd.to_datetime(end_date)
            
            # 生成日期范围
            dates = pd.date_range(start=start, end=end, freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日
            
            # 生成随机价格数据
            np.random.seed(hash(symbol) % 1000)  # 为每个股票使用不同的随机种子
            
            base_price = 10 + (hash(symbol) % 100)  # 基础价格
            returns = np.random.normal(0.001, 0.02, len(dates))  # 随机收益率
            prices = base_price * np.exp(np.cumsum(returns))
            
            # 生成OHLC数据
            data = []
            for i, date in enumerate(dates):
                close = prices[i]
                open_price = close * (1 + np.random.normal(0, 0.01))
                high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
                low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
                volume = int(np.random.uniform(1000000, 50000000))
                
                data.append({
                    "date": date,
                    "open": round(open_price, 2),
                    "high": round(high, 2),
                    "low": round(low, 2),
                    "close": round(close, 2),
                    "volume": volume,
                    "amount": round(close * volume, 2)
                })
            
            df = pd.DataFrame(data)
            df = df.set_index("date")
            
            return df
            
        except Exception as e:
            logger.error(f"生成模拟股票数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _get_mock_realtime_data(self, symbols: List[str]) -> Dict[str, Any]:
        """获取模拟实时数据"""
        realtime_data = {}
        
        for symbol in symbols:
            # 生成随机实时数据
            np.random.seed(hash(symbol) % 1000)
            base_price = 10 + (hash(symbol) % 100)
            
            price = round(base_price * (1 + np.random.normal(0, 0.02)), 2)
            change = round(np.random.normal(0, 0.5), 2)
            pct_change = round(change / (price - change) * 100, 2)
            
            realtime_data[symbol] = {
                "symbol": symbol,
                "name": f"股票{symbol}",
                "price": price,
                "change": change,
                "pct_change": pct_change,
                "volume": int(np.random.uniform(1000000, 50000000)),
                "amount": int(np.random.uniform(100000000, 1000000000)),
                "high": round(price * 1.05, 2),
                "low": round(price * 0.95, 2),
                "open": round(price * (1 + np.random.normal(0, 0.01)), 2),
                "yesterday_close": round(price - change, 2),
                "timestamp": datetime.now().isoformat()
            }
        
        return realtime_data
    
    def _get_mock_index_data(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取模拟指数数据"""
        # 与股票数据类似的模拟逻辑
        return self._get_mock_stock_data(index_code, start_date, end_date)
    
    def _get_mock_financial_data(self, symbol: str) -> Dict[str, Any]:
        """获取模拟财务数据"""
        np.random.seed(hash(symbol) % 1000)
        
        return {
            "symbol": symbol,
            "report_date": "2023-12-31",
            "revenue": round(np.random.uniform(1000000000, 100000000000), 2),
            "net_profit": round(np.random.uniform(100000000, 10000000000), 2),
            "eps": round(np.random.uniform(0.1, 5.0), 2),
            "roe": round(np.random.uniform(5.0, 25.0), 2),
            "debt_ratio": round(np.random.uniform(20.0, 70.0), 2),
            "gross_margin": round(np.random.uniform(10.0, 50.0), 2)
        }
    
    def _get_mock_news_data(self, symbol: str, limit: int) -> List[Dict[str, Any]]:
        """获取模拟新闻数据"""
        news_list = []
        
        for i in range(limit):
            news_list.append({
                "title": f"关于{symbol or '市场'}的重要新闻{i+1}",
                "content": f"这是一条关于{symbol or '金融市场'}的模拟新闻内容，包含了重要的市场信息。",
                "publish_time": (datetime.now() - timedelta(hours=i)).isoformat(),
                "source": "财经新闻源",
                "url": f"https://example.com/news/{i+1}"
            })
        
        return news_list


# 全局AkShare数据源实例
akshare_data_source = AkShareDataSource()


async def get_akshare_client() -> AkShareDataSource:
    """获取AkShare客户端实例"""
    return akshare_data_source