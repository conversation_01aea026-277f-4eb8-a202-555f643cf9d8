# 开发环境配置文件
# 复制为 .env.development 并根据需要修改

# === 数据库配置 ===
DATABASE_URL=sqlite:///./quant_platform.db
# 对于PostgreSQL使用：
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/quant_dev

# === Redis配置 ===
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# === API配置 ===
API_V1_PREFIX=/api/v1
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true

# === 安全配置 ===
SECRET_KEY=your-development-secret-key-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# === 外部数据源 ===
TUSHARE_TOKEN=your-tushare-token
AKSHARE_ENABLED=true

# === 日志配置 ===
LOG_LEVEL=DEBUG
LOG_FORMAT=structured
LOG_FILE=logs/app.log

# === 前端配置 ===
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=["http://localhost:5173", "http://127.0.0.1:5173"]

# === 监控配置 ===
ENABLE_MONITORING=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090
