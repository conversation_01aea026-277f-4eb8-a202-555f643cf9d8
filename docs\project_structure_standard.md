# 项目结构标准

## 总体架构
```
quant014/                           # 项目根目录
├── backend/                        # 后端Python应用
│   ├── app/                       # FastAPI应用核心
│   │   ├── api/v1/               # API路由层
│   │   ├── core/                 # 核心基础设施
│   │   ├── services/             # 业务服务层
│   │   ├── models/               # 数据模型
│   │   ├── schemas/              # 数据验证模式
│   │   └── utils/                # 工具函数
│   ├── migrations/               # 数据库迁移
│   ├── tests/                    # 后端测试
│   └── requirements.txt          # Python依赖
├── frontend/                       # 前端Vue应用
│   ├── src/                      # 源码目录
│   │   ├── api/                  # API接口层
│   │   ├── components/           # 可复用组件
│   │   ├── views/                # 页面视图
│   │   ├── stores/               # 状态管理
│   │   ├── router/               # 路由配置
│   │   └── utils/                # 工具函数
│   ├── public/                   # 静态资源
│   └── package.json              # 前端依赖
├── docker/                         # 容器化配置
│   ├── backend/                  # 后端Docker文件
│   ├── frontend/                 # 前端Docker文件
│   ├── nginx/                    # Nginx配置
│   └── compose/                  # Docker Compose文件
├── docs/                          # 项目文档
├── scripts/                       # 自动化脚本
├── monitoring/                    # 监控配置
└── reports/                       # 分析报告
```

## 命名规范

### 文件命名
- **Python文件**: snake_case (例：user_service.py)
- **Vue组件**: PascalCase (例：UserProfile.vue)
- **普通文件**: kebab-case (例：docker-compose.yml)

### 目录命名
- **英文小写**: 使用英文小写字母
- **下划线分隔**: snake_case (例：user_management)
- **功能导向**: 按功能而非技术分组

### 服务层命名规范
- **核心服务**: `{domain}_service.py`
- **数据适配器**: `{source}_adapter.py`
- **增强服务**: `{domain}_service_enhanced.py`
- **门面服务**: `{domain}_facade.py`

## 分层原则

### 后端分层
1. **API层** (api/): 处理HTTP请求和响应
2. **服务层** (services/): 业务逻辑实现
3. **数据层** (models/): 数据模型和数据库操作
4. **工具层** (utils/): 通用工具和帮助函数

### 前端分层
1. **视图层** (views/): 页面组件
2. **组件层** (components/): 可复用组件
3. **服务层** (api/, stores/): 数据获取和状态管理
4. **工具层** (utils/): 通用工具函数

## 依赖管理原则
- **后端**: 使用requirements.txt，Python 3.10.13
- **前端**: 使用pnpm，Node.js >= 18.0.0
- **容器**: 使用Docker，统一运行环境
- **监控**: 使用Prometheus + Grafana
