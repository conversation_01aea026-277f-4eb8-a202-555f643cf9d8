"""
统一错误处理工具
"""
import logging
import traceback
import asyncio
from typing import Any, Dict, Optional
from fastapi import HTTPException
from datetime import datetime

logger = logging.getLogger(__name__)

class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_file_error(error: Exception, context: str) -> Dict[str, Any]:
        """处理文件访问错误"""
        error_msg = f"文件操作失败: {str(error)}"
        logger.error(f"{context} - {error_msg}", exc_info=True)
        
        return {
            "success": False,
            "error": error_msg,
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def handle_memory_error(error: Exception, context: str) -> Dict[str, Any]:
        """处理内存不足错误"""
        error_msg = f"内存不足: {str(error)}"
        logger.error(f"{context} - {error_msg}", exc_info=True)
        
        return {
            "success": False,
            "error": "数据量过大，请缩小查询范围",
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def handle_timeout_error(error: Exception, context: str) -> Dict[str, Any]:
        """处理超时错误"""
        error_msg = f"操作超时: {str(error)}"
        logger.error(f"{context} - {error_msg}", exc_info=True)
        
        return {
            "success": False,
            "error": "操作超时，请稍后重试",
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def handle_api_error(error: Exception, context: str) -> HTTPException:
        """处理API错误"""
        if isinstance(error, FileNotFoundError):
            return HTTPException(status_code=404, detail="资源不存在")
        elif isinstance(error, MemoryError):
            return HTTPException(status_code=413, detail="数据量过大")
        elif isinstance(error, TimeoutError):
            return HTTPException(status_code=408, detail="请求超时")
        elif isinstance(error, asyncio.TimeoutError):
            return HTTPException(status_code=408, detail="请求超时")
        else:
            logger.error(f"{context} - 未知错误: {str(error)}", exc_info=True)
            return HTTPException(status_code=500, detail="服务器内部错误")

class MemoryLimiter:
    """内存使用限制器"""
    
    def __init__(self, max_rows: int = 100000):
        self.max_rows = max_rows
    
    def check_data_size(self, data_size: int, context: str = "数据查询"):
        """检查数据大小是否超限"""
        if data_size > self.max_rows:
            raise MemoryError(f"{context}: 数据量 {data_size} 超过限制 {self.max_rows}")
    
    def safe_slice(self, data: Any, max_size: Optional[int] = None):
        """安全切片数据"""
        limit = max_size or self.max_rows
        if hasattr(data, '__len__') and len(data) > limit:
            logger.warning(f"数据被截断: {len(data)} -> {limit}")
            return data[:limit]
        return data

class TimeoutManager:
    """超时管理器"""
    
    @staticmethod
    def get_timeout(operation_type: str) -> int:
        """获取操作超时时间(秒)"""
        timeouts = {
            "file_read": 30,
            "data_query": 60,
            "api_request": 15,
            "cache_operation": 5,
            "csv_read": 45,
            "large_query": 120
        }
        return timeouts.get(operation_type, 30)
    
    @staticmethod
    async def with_timeout(coro, operation_type: str):
        """带超时的协程执行"""
        timeout = TimeoutManager.get_timeout(operation_type)
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise TimeoutError(f"{operation_type} 操作超时 ({timeout}秒)")

def safe_operation(operation_type: str = "default"):
    """安全操作装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await TimeoutManager.with_timeout(
                        func(*args, **kwargs), 
                        operation_type
                    )
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                return ErrorHandler.handle_api_error(e, f"{func.__name__}")
        return wrapper
    return decorator